#!/usr/bin/env python3
"""
YouTube Spam Classification - Comprehensive ML Algorithm Suite
==============================================================

Implements extensive machine learning pipeline with:
- All SVM kernels (linear, poly, rbf, sigmoid)
- Multiple Naive Bayes variants (Gaussian, Multinomial, Bernoulli, Complement)
- Logistic Regression variants (different solvers and regularization)
- Decision Tree variants (different criteria and splitters)
- Random Forest variants (different parameters)
- C4.5 and C5.0 equivalent implementations
- Both original and reversed split scenarios

Author: AI Assistant
Date: 2025-06-29
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import (accuracy_score, classification_report, confusion_matrix, 
                           precision_recall_fscore_support, roc_auc_score, roc_curve)

# SVM variants
from sklearn.svm import SVC

# Naive Bayes variants
from sklearn.naive_bayes import GaussianNB, MultinomialNB, BernoulliNB, ComplementNB

# Logistic Regression variants
from sklearn.linear_model import LogisticRegression

# Decision Tree variants
from sklearn.tree import DecisionTreeClassifier

# Random Forest variants
from sklearn.ensemble import RandomForestClassifier, ExtraTreesClassifier

# Additional ensemble methods
from sklearn.ensemble import GradientBoostingClassifier, AdaBoostClassifier

import joblib
import json
import os
import re
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class ComprehensiveMLPipeline:
    """Comprehensive ML pipeline with extensive algorithm variants"""
    
    def __init__(self, data_path="e:\\!!PYTHON2023\\github-youtube-sentiment\\dataset\\youtube_spam.csv"):
        self.data_path = data_path
        self.results_dir = "NEW-YOUTUBE-SPAM2025/comprehensive_results"
        self.models_dir = "NEW-YOUTUBE-SPAM2025/comprehensive_models"
        
        # Create directories
        os.makedirs(self.results_dir, exist_ok=True)
        os.makedirs(self.models_dir, exist_ok=True)
        
        # Initialize algorithm configurations
        self.setup_algorithms()
        
        # Load and preprocess data
        self.load_and_preprocess_data()
    
    def setup_algorithms(self):
        """Setup comprehensive algorithm configurations"""
        
        # SVM with all kernels
        self.svm_variants = {
            'SVM_Linear': SVC(kernel='linear', probability=True, random_state=42),
            'SVM_Polynomial': SVC(kernel='poly', degree=3, probability=True, random_state=42),
            'SVM_RBF': SVC(kernel='rbf', probability=True, random_state=42),
            'SVM_Sigmoid': SVC(kernel='sigmoid', probability=True, random_state=42),
            'SVM_RBF_Tuned': SVC(kernel='rbf', C=10, gamma='scale', probability=True, random_state=42),
            'SVM_Linear_Tuned': SVC(kernel='linear', C=1.0, probability=True, random_state=42)
        }
        
        # Naive Bayes variants
        self.nb_variants = {
            'NB_Gaussian': GaussianNB(),
            'NB_Multinomial': MultinomialNB(alpha=1.0),
            'NB_Bernoulli': BernoulliNB(alpha=1.0),
            'NB_Complement': ComplementNB(alpha=1.0),
            'NB_Multinomial_Tuned': MultinomialNB(alpha=0.1),
            'NB_Bernoulli_Tuned': BernoulliNB(alpha=0.1)
        }
        
        # Logistic Regression variants
        self.lr_variants = {
            'LR_L1': LogisticRegression(penalty='l1', solver='liblinear', random_state=42),
            'LR_L2': LogisticRegression(penalty='l2', solver='liblinear', random_state=42),
            'LR_ElasticNet': LogisticRegression(penalty='elasticnet', solver='saga', l1_ratio=0.5, random_state=42, max_iter=1000),
            'LR_LBFGS': LogisticRegression(solver='lbfgs', random_state=42, max_iter=1000),
            'LR_SAG': LogisticRegression(solver='sag', random_state=42, max_iter=1000),
            'LR_SAGA': LogisticRegression(solver='saga', random_state=42, max_iter=1000)
        }
        
        # Decision Tree variants (including C4.5 and C5.0 equivalents)
        self.dt_variants = {
            'DT_Gini': DecisionTreeClassifier(criterion='gini', random_state=42),
            'DT_Entropy': DecisionTreeClassifier(criterion='entropy', random_state=42),  # C4.5 equivalent
            'DT_Log_Loss': DecisionTreeClassifier(criterion='log_loss', random_state=42),  # C5.0 equivalent
            'DT_Gini_Pruned': DecisionTreeClassifier(criterion='gini', max_depth=10, min_samples_split=5, random_state=42),
            'DT_Entropy_Pruned': DecisionTreeClassifier(criterion='entropy', max_depth=10, min_samples_split=5, random_state=42),
            'DT_Best_First': DecisionTreeClassifier(criterion='gini', splitter='best', random_state=42),
            'DT_Random_Split': DecisionTreeClassifier(criterion='gini', splitter='random', random_state=42)
        }
        
        # Random Forest and ensemble variants
        self.rf_variants = {
            'RF_Gini': RandomForestClassifier(criterion='gini', n_estimators=100, random_state=42),
            'RF_Entropy': RandomForestClassifier(criterion='entropy', n_estimators=100, random_state=42),
            'RF_Log_Loss': RandomForestClassifier(criterion='log_loss', n_estimators=100, random_state=42),
            'RF_Large': RandomForestClassifier(n_estimators=200, max_depth=15, random_state=42),
            'RF_Small': RandomForestClassifier(n_estimators=50, max_depth=8, random_state=42),
            'ExtraTrees': ExtraTreesClassifier(n_estimators=100, random_state=42),
            'ExtraTrees_Large': ExtraTreesClassifier(n_estimators=200, max_depth=15, random_state=42)
        }
        
        # Additional ensemble methods
        self.ensemble_variants = {
            'GradientBoosting': GradientBoostingClassifier(n_estimators=100, random_state=42),
            'AdaBoost': AdaBoostClassifier(n_estimators=100, random_state=42),
            'AdaBoost_DT': AdaBoostClassifier(
                estimator=DecisionTreeClassifier(max_depth=3, random_state=42),
                n_estimators=100, random_state=42
            )
        }
        
        # Combine all algorithms
        self.all_algorithms = {}
        self.all_algorithms.update(self.svm_variants)
        self.all_algorithms.update(self.nb_variants)
        self.all_algorithms.update(self.lr_variants)
        self.all_algorithms.update(self.dt_variants)
        self.all_algorithms.update(self.rf_variants)
        self.all_algorithms.update(self.ensemble_variants)
        
        print(f"📊 Configured {len(self.all_algorithms)} algorithm variants:")
        print(f"   - SVM variants: {len(self.svm_variants)}")
        print(f"   - Naive Bayes variants: {len(self.nb_variants)}")
        print(f"   - Logistic Regression variants: {len(self.lr_variants)}")
        print(f"   - Decision Tree variants: {len(self.dt_variants)}")
        print(f"   - Random Forest variants: {len(self.rf_variants)}")
        print(f"   - Ensemble variants: {len(self.ensemble_variants)}")
    
    def load_and_preprocess_data(self):
        """Load and preprocess the YouTube spam dataset"""
        print("📂 Loading and preprocessing data...")
        
        # Load data
        self.df = pd.read_csv(self.data_path)
        print(f"   Dataset shape: {self.df.shape}")
        print(f"   Columns: {list(self.df.columns)}")
        
        # Check class distribution
        class_dist = self.df['CLASS'].value_counts()
        print(f"   Class distribution: {dict(class_dist)}")
        
        # Preprocess text
        self.df['CONTENT_CLEAN'] = self.df['CONTENT'].apply(self.clean_text)
        
        # Prepare features and target
        self.X = self.df['CONTENT_CLEAN']
        self.y = self.df['CLASS']
        
        print("✅ Data preprocessing complete")
    
    def clean_text(self, text):
        """Clean and preprocess text data"""
        if pd.isna(text):
            return ""
        
        # Convert to string and lowercase
        text = str(text).lower()
        
        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # Remove email addresses
        text = re.sub(r'\S+@\S+', '', text)
        
        # Remove special characters but keep spaces
        text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text)
        
        # Remove extra whitespace
        text = ' '.join(text.split())
        
        return text
    
    def create_pipeline(self, algorithm, algorithm_name):
        """Create ML pipeline for given algorithm"""
        
        # Special handling for Naive Bayes variants that need dense arrays
        if 'NB_Gaussian' in algorithm_name:
            pipeline = Pipeline([
                ('tfidf', TfidfVectorizer(max_features=5000, stop_words='english')),
                ('scaler', StandardScaler(with_mean=False)),  # Sparse-safe scaling
                ('classifier', algorithm)
            ])
        elif any(nb_type in algorithm_name for nb_type in ['NB_Multinomial', 'NB_Bernoulli', 'NB_Complement']):
            pipeline = Pipeline([
                ('tfidf', TfidfVectorizer(max_features=5000, stop_words='english')),
                ('classifier', algorithm)
            ])
        else:
            # Standard pipeline for other algorithms
            pipeline = Pipeline([
                ('tfidf', TfidfVectorizer(max_features=5000, stop_words='english')),
                ('classifier', algorithm)
            ])
        
        return pipeline
    
    def train_and_evaluate_comprehensive(self, test_sizes=[0.25, 0.30, 0.35], reversed_splits=False):
        """Train and evaluate all algorithms with comprehensive analysis"""
        
        split_type = "REVERSED" if reversed_splits else "ORIGINAL"
        print(f"\n🚀 Starting {split_type} comprehensive training...")
        print(f"   Test sizes: {test_sizes}")
        print(f"   Total algorithms: {len(self.all_algorithms)}")
        print(f"   Total models to train: {len(self.all_algorithms) * len(test_sizes)}")
        
        all_results = {}
        training_progress = []
        
        for i, test_size in enumerate(test_sizes, 1):
            train_size = 1 - test_size
            split_name = f"train_{int(train_size*100)}_test_{int(test_size*100)}"
            if reversed_splits:
                split_name += "_REVERSED"
            
            print(f"\n📊 Processing split {i}/{len(test_sizes)}: {split_name}")
            print("-" * 60)
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                self.X, self.y, test_size=test_size, random_state=42, stratify=self.y
            )
            
            split_results = {}
            
            for j, (alg_name, algorithm) in enumerate(self.all_algorithms.items(), 1):
                print(f"   Training {j}/{len(self.all_algorithms)}: {alg_name}")
                
                try:
                    # Create pipeline
                    pipeline = self.create_pipeline(algorithm, alg_name)
                    
                    # Train model
                    start_time = datetime.now()
                    pipeline.fit(X_train, y_train)
                    training_time = (datetime.now() - start_time).total_seconds()
                    
                    # Make predictions
                    y_pred = pipeline.predict(X_test)
                    y_pred_proba = None
                    
                    # Get prediction probabilities if available
                    if hasattr(pipeline.named_steps['classifier'], 'predict_proba'):
                        try:
                            y_pred_proba = pipeline.predict_proba(X_test)[:, 1]
                        except:
                            y_pred_proba = None
                    
                    # Calculate metrics
                    accuracy = accuracy_score(y_test, y_pred)
                    precision, recall, f1, support = precision_recall_fscore_support(y_test, y_pred, average=None)
                    
                    # Classification report
                    class_report = classification_report(y_test, y_pred, output_dict=True)
                    
                    # Confusion matrix
                    cm = confusion_matrix(y_test, y_pred)
                    
                    # ROC AUC if probabilities available
                    roc_auc = None
                    if y_pred_proba is not None:
                        try:
                            roc_auc = roc_auc_score(y_test, y_pred_proba)
                        except:
                            roc_auc = None
                    
                    # Store results
                    split_results[alg_name] = {
                        'accuracy': accuracy,
                        'precision': precision.tolist(),
                        'recall': recall.tolist(),
                        'f1_score': f1.tolist(),
                        'support': support.tolist(),
                        'classification_report': class_report,
                        'confusion_matrix': cm.tolist(),
                        'roc_auc': roc_auc,
                        'training_time': training_time,
                        'train_size': len(X_train),
                        'test_size': len(X_test),
                        'algorithm_type': self.get_algorithm_type(alg_name)
                    }
                    
                    # Save model
                    model_filename = f"{split_name}_{alg_name.lower()}_model.pkl"
                    model_path = os.path.join(self.models_dir, model_filename)
                    joblib.dump(pipeline, model_path)
                    
                    # Track progress
                    training_progress.append({
                        'split': split_name,
                        'algorithm': alg_name,
                        'accuracy': accuracy,
                        'training_time': training_time,
                        'status': 'success'
                    })
                    
                    print(f"      ✅ Accuracy: {accuracy:.3f} ({accuracy*100:.1f}%) - Time: {training_time:.2f}s")
                    
                except Exception as e:
                    print(f"      ❌ Error: {str(e)}")
                    training_progress.append({
                        'split': split_name,
                        'algorithm': alg_name,
                        'accuracy': 0.0,
                        'training_time': 0.0,
                        'status': f'error: {str(e)}'
                    })
                    continue
            
            # Save split results
            all_results[split_name] = split_results
            
            # Save split results to JSON
            split_results_path = os.path.join(self.results_dir, f"{split_name}_results.json")
            with open(split_results_path, 'w') as f:
                json.dump(split_results, f, indent=2)
            
            print(f"   ✅ Split {split_name} complete: {len(split_results)} models trained")
        
        # Save comprehensive results
        results_filename = f"comprehensive_results_{split_type.lower()}.json"
        results_path = os.path.join(self.results_dir, results_filename)
        with open(results_path, 'w') as f:
            json.dump(all_results, f, indent=2)
        
        # Save training progress
        progress_df = pd.DataFrame(training_progress)
        progress_path = os.path.join(self.results_dir, f"training_progress_{split_type.lower()}.csv")
        progress_df.to_csv(progress_path, index=False)
        
        print(f"\n🎉 {split_type} comprehensive training complete!")
        print(f"   Total models trained: {len(progress_df[progress_df['status'] == 'success'])}")
        print(f"   Results saved to: {self.results_dir}")
        print(f"   Models saved to: {self.models_dir}")
        
        return all_results, training_progress
    
    def get_algorithm_type(self, algorithm_name):
        """Get algorithm type category"""
        if 'SVM' in algorithm_name:
            return 'Support Vector Machine'
        elif 'NB' in algorithm_name:
            return 'Naive Bayes'
        elif 'LR' in algorithm_name:
            return 'Logistic Regression'
        elif 'DT' in algorithm_name:
            return 'Decision Tree'
        elif 'RF' in algorithm_name or 'ExtraTrees' in algorithm_name:
            return 'Random Forest'
        elif 'Gradient' in algorithm_name or 'Ada' in algorithm_name:
            return 'Ensemble'
        else:
            return 'Other'

def main():
    """Main function to run comprehensive ML pipeline"""
    print("YOUTUBE SPAM CLASSIFICATION - COMPREHENSIVE ML ALGORITHM SUITE")
    print("=" * 70)
    print("Implementing extensive algorithm variants with both split scenarios")
    print("=" * 70)
    
    # Initialize pipeline
    pipeline = ComprehensiveMLPipeline()
    
    # Train original splits (large train, small test)
    print("\n🔄 PHASE 1: ORIGINAL SPLITS (Large Train / Small Test)")
    original_results, original_progress = pipeline.train_and_evaluate_comprehensive(
        test_sizes=[0.25, 0.30, 0.35], 
        reversed_splits=False
    )
    
    # Train reversed splits (small train, large test)
    print("\n🔄 PHASE 2: REVERSED SPLITS (Small Train / Large Test)")
    reversed_results, reversed_progress = pipeline.train_and_evaluate_comprehensive(
        test_sizes=[0.75, 0.70, 0.65], 
        reversed_splits=True
    )
    
    print("\n" + "=" * 70)
    print("🎉 COMPREHENSIVE ML TRAINING COMPLETE!")
    print("=" * 70)
    
    # Quick summary
    total_models = len(original_progress) + len(reversed_progress)
    successful_models = len([p for p in original_progress + reversed_progress if p['status'] == 'success'])
    
    print(f"📊 TRAINING SUMMARY:")
    print(f"   Total models attempted: {total_models}")
    print(f"   Successfully trained: {successful_models}")
    print(f"   Success rate: {(successful_models/total_models)*100:.1f}%")
    print(f"   Algorithm variants: {len(pipeline.all_algorithms)}")
    print(f"   Split scenarios: 6 (3 original + 3 reversed)")

if __name__ == "__main__":
    main()
