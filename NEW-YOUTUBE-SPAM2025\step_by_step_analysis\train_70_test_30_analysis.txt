TRAIN_70_TEST_30 - DE<PERSON>ILED SPLIT ANALYSIS
============================================================
Analysis Date: 2025-06-30 00:39:29

SPLIT CONFIGURATION:
-------------------------
Split Type: Original
Training Size: 1369 samples
Test Size: 587 samples
Train/Test Ratio: 2.33

PERFORMANCE OVERVIEW:
-------------------------
Models Tested: 34
Best Accuracy: 0.9063 (90.63%)
Average Accuracy: 0.8843 (88.43%)
Worst Accuracy: 0.8433 (84.33%)
Standard Deviation: 0.0169

TOP 5 PERFORMERS:
--------------------
1. SVM_RBF
   Accuracy: 0.9063 (90.63%)
   Spam F1: 0.9066
   Training Time: 0.56s

2. SVM_Sigmoid
   Accuracy: 0.9046 (90.46%)
   Spam F1: 0.9018
   Training Time: 0.35s

3. LR_L2
   Accuracy: 0.9046 (90.46%)
   Spam F1: 0.9021
   Training Time: 0.02s

4. LR_SAG
   Accuracy: 0.9046 (90.46%)
   Spam F1: 0.9021
   Training Time: 0.02s

5. LR_SAGA
   Accuracy: 0.9046 (90.46%)
   Spam F1: 0.9021
   Training Time: 0.03s

PERFORMANCE BY ALGORITHM TYPE:
-----------------------------------
Support Vector Machine:
  Models: 6
  Average Accuracy: 0.8992
  Best Accuracy: 0.9063
  Average Training Time: 0.46s

Naive Bayes:
  Models: 5
  Average Accuracy: 0.8811
  Best Accuracy: 0.9029
  Average Training Time: 0.02s

Logistic Regression:
  Models: 6
  Average Accuracy: 0.8958
  Best Accuracy: 0.9046
  Average Training Time: 0.03s

Decision Tree:
  Models: 8
  Average Accuracy: 0.8669
  Best Accuracy: 0.8825
  Average Training Time: 0.17s

Random Forest:
  Models: 7
  Average Accuracy: 0.8820
  Best Accuracy: 0.9012
  Average Training Time: 0.51s

Ensemble:
  Models: 2
  Average Accuracy: 0.8910
  Best Accuracy: 0.8978
  Average Training Time: 0.58s

TRAINING EFFICIENCY:
--------------------
Fastest Training:
  1. NB_Bernoulli_Tuned: 0.00s (Acc: 0.876)
  2. LR_L1: 0.02s (Acc: 0.877)
  3. NB_Multinomial_Tuned: 0.02s (Acc: 0.893)

Slowest Training:
  1. AdaBoost_DT: 0.89s (Acc: 0.865)
  2. ExtraTrees: 0.79s (Acc: 0.901)
  3. GradientBoosting: 0.73s (Acc: 0.898)

============================================================