ENSEMBLE - DETAILED ANALYSIS
============================================================
Analysis Date: 2025-06-30 00:39:29

OVERVIEW:
--------------------
Total Variants Tested: 2
Total Models Trained: 12
Best Accuracy: 0.9018 (90.18%)
Average Accuracy: 0.8863 (88.63%)
Worst Accuracy: 0.8575 (85.75%)

BEST PERFORMER:
--------------------
Algorithm: GradientBoosting
Split Type: Original
Split Name: train_75_test_25
Accuracy: 0.9018 (90.18%)
Spam F1-Score: 0.8961
Training Time: 0.73 seconds

VARIANT COMPARISON:
--------------------
GradientBoosting:
  Average Accuracy: 0.8912
  Best Accuracy: 0.9018
  Consistency (Std): 0.0099
  Average Training Time: 0.52s
  Average Spam F1: 0.8832

AdaBoost:
  Average Accuracy: 0.8815
  Best Accuracy: 0.9018
  Consistency (Std): 0.0156
  Average Training Time: 0.34s
  Average Spam F1: 0.8745

PERFORMANCE BY SPLIT TYPE:
------------------------------
Original Splits:
  Models: 6
  Average Accuracy: 0.8966
  Best Accuracy: 0.9018
  Average Training Time: 0.55s

Reversed Splits:
  Models: 6
  Average Accuracy: 0.8760
  Best Accuracy: 0.8829
  Average Training Time: 0.31s

RECOMMENDATIONS:
--------------------
• Gradient Boosting often provides excellent results
• AdaBoost good for binary classification
• Consider learning rate tuning
• Higher computational cost but better performance

============================================================