{"SVM_Linear": {"accuracy": 0.8905109489051095, "precision": [0.844, 0.9467741935483871], "recall": [0.9504504504504504, 0.8338068181818182], "f1_score": [0.8940677966101694, 0.8867069486404834], "support": [666, 704], "classification_report": {"0": {"precision": 0.844, "recall": 0.9504504504504504, "f1-score": 0.8940677966101694, "support": 666.0}, "1": {"precision": 0.9467741935483871, "recall": 0.8338068181818182, "f1-score": 0.8867069486404834, "support": 704.0}, "accuracy": 0.8905109489051095, "macro avg": {"precision": 0.8953870967741935, "recall": 0.8921286343161343, "f1-score": 0.8903873726253264, "support": 1370.0}, "weighted avg": {"precision": 0.8968124323051564, "recall": 0.8905109489051095, "f1-score": 0.8902852878724621, "support": 1370.0}}, "confusion_matrix": [[633, 33], [117, 587]], "roc_auc": 0.9574396840021839, "training_time": 0.105607, "train_size": 586, "test_size": 1370, "algorithm_type": "Support Vector Machine"}, "SVM_Polynomial": {"accuracy": 0.8467153284671532, "precision": [0.8713355048859935, 0.8267195767195767], "recall": [0.8033033033033034, 0.8877840909090909], "f1_score": [0.8359375, 0.8561643835616438], "support": [666, 704], "classification_report": {"0": {"precision": 0.8713355048859935, "recall": 0.8033033033033034, "f1-score": 0.8359375, "support": 666.0}, "1": {"precision": 0.8267195767195767, "recall": 0.8877840909090909, "f1-score": 0.8561643835616438, "support": 704.0}, "accuracy": 0.8467153284671532, "macro avg": {"precision": 0.8490275408027852, "recall": 0.8455436971061971, "f1-score": 0.846050941780822, "support": 1370.0}, "weighted avg": {"precision": 0.8484087797552217, "recall": 0.8467153284671532, "f1-score": 0.8463314606039397, "support": 1370.0}}, "confusion_matrix": [[535, 131], [79, 625]], "roc_auc": 0.9277988926426426, "training_time": 0.114819, "train_size": 586, "test_size": 1370, "algorithm_type": "Support Vector Machine"}, "SVM_RBF": {"accuracy": 0.8897810218978102, "precision": [0.8694404591104734, 0.9108469539375929], "recall": [0.9099099099099099, 0.8707386363636364], "f1_score": [0.8892149669845928, 0.8903413217138707], "support": [666, 704], "classification_report": {"0": {"precision": 0.8694404591104734, "recall": 0.9099099099099099, "f1-score": 0.8892149669845928, "support": 666.0}, "1": {"precision": 0.9108469539375929, "recall": 0.8707386363636364, "f1-score": 0.8903413217138707, "support": 704.0}, "accuracy": 0.8897810218978102, "macro avg": {"precision": 0.8901437065240332, "recall": 0.8903242731367731, "f1-score": 0.8897781443492317, "support": 1370.0}, "weighted avg": {"precision": 0.8907179571822195, "recall": 0.8897810218978102, "f1-score": 0.889793765327229, "support": 1370.0}}, "confusion_matrix": [[606, 60], [91, 613]], "roc_auc": 0.9347998566748567, "training_time": 0.102122, "train_size": 586, "test_size": 1370, "algorithm_type": "Support Vector Machine"}, "SVM_Sigmoid": {"accuracy": 0.8861313868613139, "precision": [0.8337696335078534, 0.9521452145214522], "recall": [0.9564564564564565, 0.8196022727272727], "f1_score": [0.8909090909090909, 0.8809160305343512], "support": [666, 704], "classification_report": {"0": {"precision": 0.8337696335078534, "recall": 0.9564564564564565, "f1-score": 0.8909090909090909, "support": 666.0}, "1": {"precision": 0.9521452145214522, "recall": 0.8196022727272727, "f1-score": 0.8809160305343512, "support": 704.0}, "accuracy": 0.8861313868613139, "macro avg": {"precision": 0.8929574240146527, "recall": 0.8880293645918647, "f1-score": 0.8859125607217211, "support": 1370.0}, "weighted avg": {"precision": 0.8945991291527976, "recall": 0.8861313868613139, "f1-score": 0.8857739708333123, "support": 1370.0}}, "confusion_matrix": [[637, 29], [127, 577]], "roc_auc": 0.9574599457411958, "training_time": 0.083393, "train_size": 586, "test_size": 1370, "algorithm_type": "Support Vector Machine"}, "SVM_RBF_Tuned": {"accuracy": 0.891970802919708, "precision": [0.8775510204081632, 0.9064327485380117], "recall": [0.9039039039039038, 0.8806818181818182], "f1_score": [0.8905325443786982, 0.8933717579250721], "support": [666, 704], "classification_report": {"0": {"precision": 0.8775510204081632, "recall": 0.9039039039039038, "f1-score": 0.8905325443786982, "support": 666.0}, "1": {"precision": 0.9064327485380117, "recall": 0.8806818181818182, "f1-score": 0.8933717579250721, "support": 704.0}, "accuracy": 0.891970802919708, "macro avg": {"precision": 0.8919918844730874, "recall": 0.8922928610428611, "f1-score": 0.8919521511518851, "support": 1370.0}, "weighted avg": {"precision": 0.892392433987297, "recall": 0.891970802919708, "f1-score": 0.8919915271061779, "support": 1370.0}}, "confusion_matrix": [[602, 64], [84, 620]], "roc_auc": 0.9354162401037401, "training_time": 0.125672, "train_size": 586, "test_size": 1370, "algorithm_type": "Support Vector Machine"}, "SVM_Linear_Tuned": {"accuracy": 0.8905109489051095, "precision": [0.844, 0.9467741935483871], "recall": [0.9504504504504504, 0.8338068181818182], "f1_score": [0.8940677966101694, 0.8867069486404834], "support": [666, 704], "classification_report": {"0": {"precision": 0.844, "recall": 0.9504504504504504, "f1-score": 0.8940677966101694, "support": 666.0}, "1": {"precision": 0.9467741935483871, "recall": 0.8338068181818182, "f1-score": 0.8867069486404834, "support": 704.0}, "accuracy": 0.8905109489051095, "macro avg": {"precision": 0.8953870967741935, "recall": 0.8921286343161343, "f1-score": 0.8903873726253264, "support": 1370.0}, "weighted avg": {"precision": 0.8968124323051564, "recall": 0.8905109489051095, "f1-score": 0.8902852878724621, "support": 1370.0}}, "confusion_matrix": [[633, 33], [117, 587]], "roc_auc": 0.9574396840021839, "training_time": 0.101092, "train_size": 586, "test_size": 1370, "algorithm_type": "Support Vector Machine"}, "NB_Multinomial": {"accuracy": 0.872992700729927, "precision": [0.9006514657980456, 0.8505291005291006], "recall": [0.8303303303303303, 0.9133522727272727], "f1_score": [0.8640625, 0.8808219178082192], "support": [666, 704], "classification_report": {"0": {"precision": 0.9006514657980456, "recall": 0.8303303303303303, "f1-score": 0.8640625, "support": 666.0}, "1": {"precision": 0.8505291005291006, "recall": 0.9133522727272727, "f1-score": 0.8808219178082192, "support": 704.0}, "accuracy": 0.872992700729927, "macro avg": {"precision": 0.8755902831635731, "recall": 0.8718413015288016, "f1-score": 0.8724422089041095, "support": 1370.0}, "weighted avg": {"precision": 0.8748951554700621, "recall": 0.872992700729927, "f1-score": 0.8726746387861214, "support": 1370.0}}, "confusion_matrix": [[553, 113], [61, 643]], "roc_auc": 0.9488103159978158, "training_time": 0.010873, "train_size": 586, "test_size": 1370, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli": {"accuracy": 0.8262773722627738, "precision": [0.7388392857142857, 0.9915611814345991], "recall": [0.993993993993994, 0.6676136363636364], "f1_score": [0.8476312419974392, 0.797962648556876], "support": [666, 704], "classification_report": {"0": {"precision": 0.7388392857142857, "recall": 0.993993993993994, "f1-score": 0.8476312419974392, "support": 666.0}, "1": {"precision": 0.9915611814345991, "recall": 0.6676136363636364, "f1-score": 0.797962648556876, "support": 704.0}, "accuracy": 0.8262773722627738, "macro avg": {"precision": 0.8652002335744424, "recall": 0.8308038151788152, "f1-score": 0.8227969452771576, "support": 1370.0}, "weighted avg": {"precision": 0.8687051357778628, "recall": 0.8262773722627738, "f1-score": 0.8221081107695878, "support": 1370.0}}, "confusion_matrix": [[662, 4], [234, 470]], "roc_auc": 0.9429557398307398, "training_time": 0.014616, "train_size": 586, "test_size": 1370, "algorithm_type": "<PERSON><PERSON>"}, "NB_Complement": {"accuracy": 0.8693430656934307, "precision": [0.8463726884779517, 0.8935532233883059], "recall": [0.8933933933933934, 0.8465909090909091], "f1_score": [0.8692476260043828, 0.8694383661560905], "support": [666, 704], "classification_report": {"0": {"precision": 0.8463726884779517, "recall": 0.8933933933933934, "f1-score": 0.8692476260043828, "support": 666.0}, "1": {"precision": 0.8935532233883059, "recall": 0.8465909090909091, "f1-score": 0.8694383661560905, "support": 704.0}, "accuracy": 0.8693430656934307, "macro avg": {"precision": 0.8699629559331288, "recall": 0.8699921512421512, "f1-score": 0.8693429960802366, "support": 1370.0}, "weighted avg": {"precision": 0.8706172845194767, "recall": 0.8693430656934307, "f1-score": 0.8693456413816106, "support": 1370.0}}, "confusion_matrix": [[595, 71], [108, 596]], "roc_auc": 0.9488103159978158, "training_time": 0.0, "train_size": 586, "test_size": 1370, "algorithm_type": "<PERSON><PERSON>"}, "NB_Multinomial_Tuned": {"accuracy": 0.8788321167883212, "precision": [0.8943217665615142, 0.8654891304347826], "recall": [0.8513513513513513, 0.9048295454545454], "f1_score": [0.8723076923076923, 0.8847222222222222], "support": [666, 704], "classification_report": {"0": {"precision": 0.8943217665615142, "recall": 0.8513513513513513, "f1-score": 0.8723076923076923, "support": 666.0}, "1": {"precision": 0.8654891304347826, "recall": 0.9048295454545454, "f1-score": 0.8847222222222222, "support": 704.0}, "accuracy": 0.8788321167883212, "macro avg": {"precision": 0.8799054484981483, "recall": 0.8780904484029484, "f1-score": 0.8785149572649573, "support": 1370.0}, "weighted avg": {"precision": 0.8795055798219383, "recall": 0.8788321167883212, "f1-score": 0.8786871295776404, "support": 1370.0}}, "confusion_matrix": [[567, 99], [67, 637]], "roc_auc": 0.9476415335790336, "training_time": 0.009315, "train_size": 586, "test_size": 1370, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli_Tuned": {"accuracy": 0.8481751824817518, "precision": [0.7752403846153846, 0.9609665427509294], "recall": [0.9684684684684685, 0.734375], "f1_score": [0.8611481975967957, 0.8325281803542673], "support": [666, 704], "classification_report": {"0": {"precision": 0.7752403846153846, "recall": 0.9684684684684685, "f1-score": 0.8611481975967957, "support": 666.0}, "1": {"precision": 0.9609665427509294, "recall": 0.734375, "f1-score": 0.8325281803542673, "support": 704.0}, "accuracy": 0.8481751824817518, "macro avg": {"precision": 0.868103463683157, "recall": 0.8514217342342343, "f1-score": 0.8468381889755315, "support": 1370.0}, "weighted avg": {"precision": 0.8706792279200734, "recall": 0.8481751824817518, "f1-score": 0.8464412690283725, "support": 1370.0}}, "confusion_matrix": [[645, 21], [187, 517]], "roc_auc": 0.9371971403221403, "training_time": 0.002869, "train_size": 586, "test_size": 1370, "algorithm_type": "<PERSON><PERSON>"}, "LR_L1": {"accuracy": 0.8496350364963504, "precision": [0.7744630071599046, 0.9680451127819549], "recall": [0.9744744744744744, 0.7315340909090909], "f1_score": [0.863031914893617, 0.8333333333333334], "support": [666, 704], "classification_report": {"0": {"precision": 0.7744630071599046, "recall": 0.9744744744744744, "f1-score": 0.863031914893617, "support": 666.0}, "1": {"precision": 0.9680451127819549, "recall": 0.7315340909090909, "f1-score": 0.8333333333333334, "support": 704.0}, "accuracy": 0.8496350364963504, "macro avg": {"precision": 0.8712540599709298, "recall": 0.8530042826917827, "f1-score": 0.8481826241134751, "support": 1370.0}, "weighted avg": {"precision": 0.8739387753043742, "recall": 0.8496350364963504, "f1-score": 0.8477707459750479, "support": 1370.0}}, "confusion_matrix": [[649, 17], [189, 515]], "roc_auc": 0.9153005988943489, "training_time": 0.00866, "train_size": 586, "test_size": 1370, "algorithm_type": "Logistic Regression"}, "LR_L2": {"accuracy": 0.8846715328467153, "precision": [0.8441734417344173, 0.9319620253164557], "recall": [0.9354354354354354, 0.8366477272727273], "f1_score": [0.8874643874643875, 0.8817365269461078], "support": [666, 704], "classification_report": {"0": {"precision": 0.8441734417344173, "recall": 0.9354354354354354, "f1-score": 0.8874643874643875, "support": 666.0}, "1": {"precision": 0.9319620253164557, "recall": 0.8366477272727273, "f1-score": 0.8817365269461078, "support": 704.0}, "accuracy": 0.8846715328467153, "macro avg": {"precision": 0.8880677335254366, "recall": 0.8860415813540814, "f1-score": 0.8846004572052477, "support": 1370.0}, "weighted avg": {"precision": 0.8892852394291291, "recall": 0.8846715328467153, "f1-score": 0.8845210197236073, "support": 1370.0}}, "confusion_matrix": [[623, 43], [115, 589]], "roc_auc": 0.9488828316953316, "training_time": 0.0, "train_size": 586, "test_size": 1370, "algorithm_type": "Logistic Regression"}, "LR_ElasticNet": {"accuracy": 0.8686131386861314, "precision": [0.8099489795918368, 0.947098976109215], "recall": [0.9534534534534534, 0.7883522727272727], "f1_score": [0.8758620689655172, 0.8604651162790697], "support": [666, 704], "classification_report": {"0": {"precision": 0.8099489795918368, "recall": 0.9534534534534534, "f1-score": 0.8758620689655172, "support": 666.0}, "1": {"precision": 0.947098976109215, "recall": 0.7883522727272727, "f1-score": 0.8604651162790697, "support": 704.0}, "accuracy": 0.8686131386861314, "macro avg": {"precision": 0.8785239778505258, "recall": 0.870902863090363, "f1-score": 0.8681635926222935, "support": 1370.0}, "weighted avg": {"precision": 0.8804260580941976, "recall": 0.8686131386861314, "f1-score": 0.8679500582419705, "support": 1370.0}}, "confusion_matrix": [[635, 31], [149, 555]], "roc_auc": 0.9312817789380289, "training_time": 0.025944, "train_size": 586, "test_size": 1370, "algorithm_type": "Logistic Regression"}, "LR_LBFGS": {"accuracy": 0.8846715328467153, "precision": [0.8432432432432433, 0.9333333333333333], "recall": [0.9369369369369369, 0.8352272727272727], "f1_score": [0.887624466571835, 0.881559220389805], "support": [666, 704], "classification_report": {"0": {"precision": 0.8432432432432433, "recall": 0.9369369369369369, "f1-score": 0.887624466571835, "support": 666.0}, "1": {"precision": 0.9333333333333333, "recall": 0.8352272727272727, "f1-score": 0.881559220389805, "support": 704.0}, "accuracy": 0.8846715328467153, "macro avg": {"precision": 0.8882882882882883, "recall": 0.8860821048321048, "f1-score": 0.88459184348082, "support": 1370.0}, "weighted avg": {"precision": 0.8895377128953772, "recall": 0.8846715328467153, "f1-score": 0.8845077269279306, "support": 1370.0}}, "confusion_matrix": [[624, 42], [116, 588]], "roc_auc": 0.9489105582855583, "training_time": 0.013647, "train_size": 586, "test_size": 1370, "algorithm_type": "Logistic Regression"}, "LR_SAG": {"accuracy": 0.8854014598540146, "precision": [0.8434547908232118, 0.9348171701112877], "recall": [0.9384384384384384, 0.8352272727272727], "f1_score": [0.8884150675195451, 0.8822205551387847], "support": [666, 704], "classification_report": {"0": {"precision": 0.8434547908232118, "recall": 0.9384384384384384, "f1-score": 0.8884150675195451, "support": 666.0}, "1": {"precision": 0.9348171701112877, "recall": 0.8352272727272727, "f1-score": 0.8822205551387847, "support": 704.0}, "accuracy": 0.8854014598540146, "macro avg": {"precision": 0.8891359804672498, "recall": 0.8868328555828555, "f1-score": 0.8853178113291649, "support": 1370.0}, "weighted avg": {"precision": 0.890403049961026, "recall": 0.8854014598540146, "f1-score": 0.8852319020333734, "support": 1370.0}}, "confusion_matrix": [[625, 41], [116, 588]], "roc_auc": 0.9489105582855583, "training_time": 0.004015, "train_size": 586, "test_size": 1370, "algorithm_type": "Logistic Regression"}, "LR_SAGA": {"accuracy": 0.8854014598540146, "precision": [0.8434547908232118, 0.9348171701112877], "recall": [0.9384384384384384, 0.8352272727272727], "f1_score": [0.8884150675195451, 0.8822205551387847], "support": [666, 704], "classification_report": {"0": {"precision": 0.8434547908232118, "recall": 0.9384384384384384, "f1-score": 0.8884150675195451, "support": 666.0}, "1": {"precision": 0.9348171701112877, "recall": 0.8352272727272727, "f1-score": 0.8822205551387847, "support": 704.0}, "accuracy": 0.8854014598540146, "macro avg": {"precision": 0.8891359804672498, "recall": 0.8868328555828555, "f1-score": 0.8853178113291649, "support": 1370.0}, "weighted avg": {"precision": 0.890403049961026, "recall": 0.8854014598540146, "f1-score": 0.8852319020333734, "support": 1370.0}}, "confusion_matrix": [[625, 41], [116, 588]], "roc_auc": 0.9489873396123396, "training_time": 0.011063, "train_size": 586, "test_size": 1370, "algorithm_type": "Logistic Regression"}, "DT_Gini": {"accuracy": 0.8306569343065694, "precision": [0.8959854014598541, 0.7871046228710462], "recall": [0.7372372372372372, 0.9190340909090909], "f1_score": [0.8088962108731467, 0.8479685452162516], "support": [666, 704], "classification_report": {"0": {"precision": 0.8959854014598541, "recall": 0.7372372372372372, "f1-score": 0.8088962108731467, "support": 666.0}, "1": {"precision": 0.7871046228710462, "recall": 0.9190340909090909, "f1-score": 0.8479685452162516, "support": 704.0}, "accuracy": 0.8306569343065694, "macro avg": {"precision": 0.8415450121654502, "recall": 0.8281356640731641, "f1-score": 0.8284323780446992, "support": 1370.0}, "weighted avg": {"precision": 0.840034986768963, "recall": 0.8306569343065694, "f1-score": 0.8289742571341291, "support": 1370.0}}, "confusion_matrix": [[491, 175], [57, 647]], "roc_auc": 0.9205355924105925, "training_time": 0.029084, "train_size": 586, "test_size": 1370, "algorithm_type": "Decision Tree"}, "DT_Entropy": {"accuracy": 0.8262773722627738, "precision": [0.8614864864864865, 0.7994858611825193], "recall": [0.7657657657657657, 0.8835227272727273], "f1_score": [0.8108108108108109, 0.8394062078272605], "support": [666, 704], "classification_report": {"0": {"precision": 0.8614864864864865, "recall": 0.7657657657657657, "f1-score": 0.8108108108108109, "support": 666.0}, "1": {"precision": 0.7994858611825193, "recall": 0.8835227272727273, "f1-score": 0.8394062078272605, "support": 704.0}, "accuracy": 0.8262773722627738, "macro avg": {"precision": 0.8304861738345028, "recall": 0.8246442465192465, "f1-score": 0.8251085093190357, "support": 1370.0}, "weighted avg": {"precision": 0.8296263111478055, "recall": 0.8262773722627738, "f1-score": 0.8255050878178039, "support": 1370.0}}, "confusion_matrix": [[510, 156], [82, 622]], "roc_auc": 0.9043134043134042, "training_time": 0.029088, "train_size": 586, "test_size": 1370, "algorithm_type": "Decision Tree"}, "DT_Log_Loss": {"accuracy": 0.8262773722627738, "precision": [0.8614864864864865, 0.7994858611825193], "recall": [0.7657657657657657, 0.8835227272727273], "f1_score": [0.8108108108108109, 0.8394062078272605], "support": [666, 704], "classification_report": {"0": {"precision": 0.8614864864864865, "recall": 0.7657657657657657, "f1-score": 0.8108108108108109, "support": 666.0}, "1": {"precision": 0.7994858611825193, "recall": 0.8835227272727273, "f1-score": 0.8394062078272605, "support": 704.0}, "accuracy": 0.8262773722627738, "macro avg": {"precision": 0.8304861738345028, "recall": 0.8246442465192465, "f1-score": 0.8251085093190357, "support": 1370.0}, "weighted avg": {"precision": 0.8296263111478055, "recall": 0.8262773722627738, "f1-score": 0.8255050878178039, "support": 1370.0}}, "confusion_matrix": [[510, 156], [82, 622]], "roc_auc": 0.9043134043134042, "training_time": 0.018223, "train_size": 586, "test_size": 1370, "algorithm_type": "Decision Tree"}, "DT_Gini_Pruned": {"accuracy": 0.8759124087591241, "precision": [0.803921568627451, 0.9819494584837545], "recall": [0.984984984984985, 0.7727272727272727], "f1_score": [0.8852901484480432, 0.8648648648648649], "support": [666, 704], "classification_report": {"0": {"precision": 0.803921568627451, "recall": 0.984984984984985, "f1-score": 0.8852901484480432, "support": 666.0}, "1": {"precision": 0.9819494584837545, "recall": 0.7727272727272727, "f1-score": 0.8648648648648649, "support": 704.0}, "accuracy": 0.8759124087591241, "macro avg": {"precision": 0.8929355135556027, "recall": 0.8788561288561288, "f1-score": 0.875077506656454, "support": 1370.0}, "weighted avg": {"precision": 0.8954045134879164, "recall": 0.8759124087591241, "f1-score": 0.8747942363001909, "support": 1370.0}}, "confusion_matrix": [[656, 10], [160, 544]], "roc_auc": 0.8730548730548731, "training_time": 0.025902, "train_size": 586, "test_size": 1370, "algorithm_type": "Decision Tree"}, "DT_Entropy_Pruned": {"accuracy": 0.8423357664233576, "precision": [0.7616279069767442, 0.9784313725490196], "recall": [0.9834834834834835, 0.7088068181818182], "f1_score": [0.8584534731323722, 0.8220757825370676], "support": [666, 704], "classification_report": {"0": {"precision": 0.7616279069767442, "recall": 0.9834834834834835, "f1-score": 0.8584534731323722, "support": 666.0}, "1": {"precision": 0.9784313725490196, "recall": 0.7088068181818182, "f1-score": 0.8220757825370676, "support": 704.0}, "accuracy": 0.8423357664233576, "macro avg": {"precision": 0.870029639762882, "recall": 0.8461451508326508, "f1-score": 0.8402646278347199, "support": 1370.0}, "weighted avg": {"precision": 0.8730364031540302, "recall": 0.8423357664233576, "f1-score": 0.8397601197169747, "support": 1370.0}}, "confusion_matrix": [[655, 11], [205, 499]], "roc_auc": 0.8744337377149878, "training_time": 0.022502, "train_size": 586, "test_size": 1370, "algorithm_type": "Decision Tree"}, "DT_Best_First": {"accuracy": 0.8306569343065694, "precision": [0.8959854014598541, 0.7871046228710462], "recall": [0.7372372372372372, 0.9190340909090909], "f1_score": [0.8088962108731467, 0.8479685452162516], "support": [666, 704], "classification_report": {"0": {"precision": 0.8959854014598541, "recall": 0.7372372372372372, "f1-score": 0.8088962108731467, "support": 666.0}, "1": {"precision": 0.7871046228710462, "recall": 0.9190340909090909, "f1-score": 0.8479685452162516, "support": 704.0}, "accuracy": 0.8306569343065694, "macro avg": {"precision": 0.8415450121654502, "recall": 0.8281356640731641, "f1-score": 0.8284323780446992, "support": 1370.0}, "weighted avg": {"precision": 0.840034986768963, "recall": 0.8306569343065694, "f1-score": 0.8289742571341291, "support": 1370.0}}, "confusion_matrix": [[491, 175], [57, 647]], "roc_auc": 0.9205355924105925, "training_time": 0.024967, "train_size": 586, "test_size": 1370, "algorithm_type": "Decision Tree"}, "DT_Random_Split": {"accuracy": 0.8218978102189781, "precision": [0.8936567164179104, 0.7757793764988009], "recall": [0.7192192192192193, 0.9190340909090909], "f1_score": [0.7970049916805324, 0.8413524057217165], "support": [666, 704], "classification_report": {"0": {"precision": 0.8936567164179104, "recall": 0.7192192192192193, "f1-score": 0.7970049916805324, "support": 666.0}, "1": {"precision": 0.7757793764988009, "recall": 0.9190340909090909, "f1-score": 0.8413524057217165, "support": 704.0}, "accuracy": 0.8218978102189781, "macro avg": {"precision": 0.8347180464583557, "recall": 0.8191266550641552, "f1-score": 0.8191786987011245, "support": 1370.0}, "weighted avg": {"precision": 0.8330832512332003, "recall": 0.8218978102189781, "f1-score": 0.8197937358301629, "support": 1370.0}}, "confusion_matrix": [[479, 187], [57, 647]], "roc_auc": 0.9082943028255529, "training_time": 0.022355, "train_size": 586, "test_size": 1370, "algorithm_type": "Decision Tree"}, "RF_Gini": {"accuracy": 0.8335766423357664, "precision": [0.9025735294117647, 0.788135593220339], "recall": [0.7372372372372372, 0.9247159090909091], "f1_score": [0.8115702479338843, 0.8509803921568627], "support": [666, 704], "classification_report": {"0": {"precision": 0.9025735294117647, "recall": 0.7372372372372372, "f1-score": 0.8115702479338843, "support": 666.0}, "1": {"precision": 0.788135593220339, "recall": 0.9247159090909091, "f1-score": 0.8509803921568627, "support": 704.0}, "accuracy": 0.8335766423357664, "macro avg": {"precision": 0.8453545613160518, "recall": 0.8309765731640731, "f1-score": 0.8312753200453735, "support": 1370.0}, "weighted avg": {"precision": 0.8437674658506232, "recall": 0.8335766423357664, "f1-score": 0.8318218840893419, "support": 1370.0}}, "confusion_matrix": [[491, 175], [53, 651]], "roc_auc": 0.9191993840431341, "training_time": 0.332043, "train_size": 586, "test_size": 1370, "algorithm_type": "Random Forest"}, "RF_Entropy": {"accuracy": 0.8328467153284671, "precision": [0.9038817005545287, 0.7864897466827503], "recall": [0.7342342342342343, 0.9261363636363636], "f1_score": [0.8102734051367025, 0.8506196999347684], "support": [666, 704], "classification_report": {"0": {"precision": 0.9038817005545287, "recall": 0.7342342342342343, "f1-score": 0.8102734051367025, "support": 666.0}, "1": {"precision": 0.7864897466827503, "recall": 0.9261363636363636, "f1-score": 0.8506196999347684, "support": 704.0}, "accuracy": 0.8328467153284671, "macro avg": {"precision": 0.8451857236186395, "recall": 0.8301852989352989, "f1-score": 0.8304465525357354, "support": 1370.0}, "weighted avg": {"precision": 0.8435576600247973, "recall": 0.8328467153284671, "f1-score": 0.8310060996898693, "support": 1370.0}}, "confusion_matrix": [[489, 177], [52, 652]], "roc_auc": 0.9189818369505869, "training_time": 0.256431, "train_size": 586, "test_size": 1370, "algorithm_type": "Random Forest"}, "RF_Log_Loss": {"accuracy": 0.8328467153284671, "precision": [0.9038817005545287, 0.7864897466827503], "recall": [0.7342342342342343, 0.9261363636363636], "f1_score": [0.8102734051367025, 0.8506196999347684], "support": [666, 704], "classification_report": {"0": {"precision": 0.9038817005545287, "recall": 0.7342342342342343, "f1-score": 0.8102734051367025, "support": 666.0}, "1": {"precision": 0.7864897466827503, "recall": 0.9261363636363636, "f1-score": 0.8506196999347684, "support": 704.0}, "accuracy": 0.8328467153284671, "macro avg": {"precision": 0.8451857236186395, "recall": 0.8301852989352989, "f1-score": 0.8304465525357354, "support": 1370.0}, "weighted avg": {"precision": 0.8435576600247973, "recall": 0.8328467153284671, "f1-score": 0.8310060996898693, "support": 1370.0}}, "confusion_matrix": [[489, 177], [52, 652]], "roc_auc": 0.9189818369505869, "training_time": 0.301991, "train_size": 586, "test_size": 1370, "algorithm_type": "Random Forest"}, "RF_Large": {"accuracy": 0.8678832116788321, "precision": [0.8019925280199253, 0.9611992945326279], "recall": [0.9669669669669669, 0.7741477272727273], "f1_score": [0.876786929884275, 0.8575924468922108], "support": [666, 704], "classification_report": {"0": {"precision": 0.8019925280199253, "recall": 0.9669669669669669, "f1-score": 0.876786929884275, "support": 666.0}, "1": {"precision": 0.9611992945326279, "recall": 0.7741477272727273, "f1-score": 0.8575924468922108, "support": 704.0}, "accuracy": 0.8678832116788321, "macro avg": {"precision": 0.8815959112762766, "recall": 0.8705573471198471, "f1-score": 0.8671896883882428, "support": 1370.0}, "weighted avg": {"precision": 0.8838038883301025, "recall": 0.8678832116788321, "f1-score": 0.8669234875292289, "support": 1370.0}}, "confusion_matrix": [[644, 22], [159, 545]], "roc_auc": 0.9431828845891347, "training_time": 0.289289, "train_size": 586, "test_size": 1370, "algorithm_type": "Random Forest"}, "RF_Small": {"accuracy": 0.8583941605839416, "precision": [0.7920792079207921, 0.9537366548042705], "recall": [0.960960960960961, 0.7613636363636364], "f1_score": [0.8683853459972863, 0.8467614533965245], "support": [666, 704], "classification_report": {"0": {"precision": 0.7920792079207921, "recall": 0.960960960960961, "f1-score": 0.8683853459972863, "support": 666.0}, "1": {"precision": 0.9537366548042705, "recall": 0.7613636363636364, "f1-score": 0.8467614533965245, "support": 704.0}, "accuracy": 0.8583941605839416, "macro avg": {"precision": 0.8729079313625313, "recall": 0.8611622986622987, "f1-score": 0.8575733996969055, "support": 1370.0}, "weighted avg": {"precision": 0.8751498959543459, "recall": 0.8583941605839416, "f1-score": 0.857273506295873, "support": 1370.0}}, "confusion_matrix": [[640, 26], [168, 536]], "roc_auc": 0.9330573471198471, "training_time": 0.064888, "train_size": 586, "test_size": 1370, "algorithm_type": "Random Forest"}, "ExtraTrees": {"accuracy": 0.845985401459854, "precision": [0.8956521739130435, 0.810062893081761], "recall": [0.7732732732732732, 0.9147727272727273], "f1_score": [0.8299758259468171, 0.8592394929953302], "support": [666, 704], "classification_report": {"0": {"precision": 0.8956521739130435, "recall": 0.7732732732732732, "f1-score": 0.8299758259468171, "support": 666.0}, "1": {"precision": 0.810062893081761, "recall": 0.9147727272727273, "f1-score": 0.8592394929953302, "support": 704.0}, "accuracy": 0.845985401459854, "macro avg": {"precision": 0.8528575334974022, "recall": 0.8440230002730003, "f1-score": 0.8446076594710736, "support": 1370.0}, "weighted avg": {"precision": 0.851670528872735, "recall": 0.845985401459854, "f1-score": 0.8450135059483888, "support": 1370.0}}, "confusion_matrix": [[515, 151], [60, 644]], "roc_auc": 0.9288951593639094, "training_time": 0.332318, "train_size": 586, "test_size": 1370, "algorithm_type": "Random Forest"}, "ExtraTrees_Large": {"accuracy": 0.8722627737226277, "precision": [0.8080301129234629, 0.9616055846422339], "recall": [0.9669669669669669, 0.7826704545454546], "f1_score": [0.8803827751196173, 0.8629600626468285], "support": [666, 704], "classification_report": {"0": {"precision": 0.8080301129234629, "recall": 0.9669669669669669, "f1-score": 0.8803827751196173, "support": 666.0}, "1": {"precision": 0.9616055846422339, "recall": 0.7826704545454546, "f1-score": 0.8629600626468285, "support": 704.0}, "accuracy": 0.8722627737226277, "macro avg": {"precision": 0.8848178487828484, "recall": 0.8748187107562108, "f1-score": 0.8716714188832229, "support": 1370.0}, "weighted avg": {"precision": 0.8869477275877073, "recall": 0.8722627737226277, "f1-score": 0.8714297900241113, "support": 1370.0}}, "confusion_matrix": [[644, 22], [153, 551]], "roc_auc": 0.9411225856538357, "training_time": 0.226227, "train_size": 586, "test_size": 1370, "algorithm_type": "Random Forest"}, "GradientBoosting": {"accuracy": 0.8824817518248175, "precision": [0.8136645962732919, 0.9805309734513274], "recall": [0.9834834834834835, 0.7869318181818182], "f1_score": [0.8905506458191706, 0.8731284475965327], "support": [666, 704], "classification_report": {"0": {"precision": 0.8136645962732919, "recall": 0.9834834834834835, "f1-score": 0.8905506458191706, "support": 666.0}, "1": {"precision": 0.9805309734513274, "recall": 0.7869318181818182, "f1-score": 0.8731284475965327, "support": 704.0}, "accuracy": 0.8824817518248175, "macro avg": {"precision": 0.8970977848623096, "recall": 0.8852076508326508, "f1-score": 0.8818395467078517, "support": 1370.0}, "weighted avg": {"precision": 0.899411990093246, "recall": 0.8824817518248175, "f1-score": 0.8815979249806764, "support": 1370.0}}, "confusion_matrix": [[655, 11], [150, 554]], "roc_auc": 0.9383339305214305, "training_time": 0.370832, "train_size": 586, "test_size": 1370, "algorithm_type": "Ensemble"}, "AdaBoost": {"accuracy": 0.8737226277372263, "precision": [0.8116308470290771, 0.9585492227979274], "recall": [0.963963963963964, 0.7883522727272727], "f1_score": [0.8812628689087165, 0.8651597817614964], "support": [666, 704], "classification_report": {"0": {"precision": 0.8116308470290771, "recall": 0.963963963963964, "f1-score": 0.8812628689087165, "support": 666.0}, "1": {"precision": 0.9585492227979274, "recall": 0.7883522727272727, "f1-score": 0.8651597817614964, "support": 704.0}, "accuracy": 0.8737226277372263, "macro avg": {"precision": 0.8850900349135022, "recall": 0.8761581183456184, "f1-score": 0.8732113253351065, "support": 1370.0}, "weighted avg": {"precision": 0.8871275890300045, "recall": 0.8737226277372263, "f1-score": 0.8729879978491233, "support": 1370.0}}, "confusion_matrix": [[642, 24], [149, 555]], "roc_auc": 0.9254357340294841, "training_time": 0.24281, "train_size": 586, "test_size": 1370, "algorithm_type": "Ensemble"}, "AdaBoost_DT": {"accuracy": 0.8452554744525548, "precision": [0.8913793103448275, 0.8113924050632911], "recall": [0.7762762762762763, 0.9105113636363636], "f1_score": [0.8298555377207063, 0.85809906291834], "support": [666, 704], "classification_report": {"0": {"precision": 0.8913793103448275, "recall": 0.7762762762762763, "f1-score": 0.8298555377207063, "support": 666.0}, "1": {"precision": 0.8113924050632911, "recall": 0.9105113636363636, "f1-score": 0.85809906291834, "support": 704.0}, "accuracy": 0.8452554744525548, "macro avg": {"precision": 0.8513858577040594, "recall": 0.84339381995632, "f1-score": 0.8439773003195232, "support": 1370.0}, "weighted avg": {"precision": 0.850276550258549, "recall": 0.8452554744525548, "f1-score": 0.8443689988441617, "support": 1370.0}}, "confusion_matrix": [[517, 149], [63, 641]], "roc_auc": 0.9453082343707343, "training_time": 0.431648, "train_size": 586, "test_size": 1370, "algorithm_type": "Decision Tree"}}