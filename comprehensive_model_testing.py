#!/usr/bin/env python3
"""
Comprehensive Model Testing Script
=================================

This script applies all 204 trained models to the test dataset 
/dataset/youtube_comments_i6IOiUi6IYY.xlsx and generates comprehensive 
predictions and analysis.

Features:
- Tests all available trained models (SVM, Naive Bayes, Logistic Regression, Decision Trees, Random Forest, etc.)
- Generates predictions for each model
- Creates ensemble predictions using majority voting
- Provides confidence scores and detailed analysis
- Exports results to multiple formats (CSV, Excel, JSON)

Author: AI Assistant
Date: 2025-06-29
"""

import os
import pandas as pd
import numpy as np
import pickle
import joblib
from datetime import datetime
import json
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

class ComprehensiveModelTester:
    def __init__(self, test_data_path="dataset/youtube_comments_i6IOiUi6IYY.xlsx", 
                 models_dir="NEW-YOUTUBE-SPAM2025/comprehensive_models"):
        """
        Initialize the comprehensive model tester
        
        Args:
            test_data_path: Path to the test dataset
            models_dir: Directory containing trained models
        """
        self.test_data_path = test_data_path
        self.models_dir = models_dir
        self.loaded_models = {}
        self.test_data = None
        self.results = {}
        
        print("🚀 COMPREHENSIVE MODEL TESTING INITIALIZED")
        print("=" * 60)
        print(f"Test dataset: {test_data_path}")
        print(f"Models directory: {models_dir}")
        
    def load_test_data(self):
        """Load and prepare the test dataset"""
        print("\n📊 Loading test dataset...")
        
        try:
            # Load the Excel file
            self.test_data = pd.read_excel(self.test_data_path)
            print(f"✅ Dataset loaded successfully!")
            print(f"   Shape: {self.test_data.shape}")
            print(f"   Columns: {self.test_data.columns.tolist()}")
            
            # Check if we have the required 'Comment' column
            if 'Comment' not in self.test_data.columns:
                raise ValueError("Dataset must contain a 'Comment' column")
            
            # Remove any null comments
            initial_count = len(self.test_data)
            self.test_data = self.test_data.dropna(subset=['Comment'])
            final_count = len(self.test_data)
            
            if initial_count != final_count:
                print(f"   Removed {initial_count - final_count} rows with null comments")
            
            print(f"   Final dataset size: {final_count} comments")
            
            # Show sample comments
            print("\n📝 Sample comments:")
            for i, comment in enumerate(self.test_data['Comment'].head(3)):
                print(f"   {i+1}. {comment[:100]}{'...' if len(comment) > 100 else ''}")
                
        except Exception as e:
            print(f"❌ Error loading test dataset: {e}")
            raise
    
    def load_models(self):
        """Load all available trained models"""
        print("\n🤖 Loading trained models...")
        
        if not os.path.exists(self.models_dir):
            raise FileNotFoundError(f"Models directory not found: {self.models_dir}")
        
        model_files = [f for f in os.listdir(self.models_dir) if f.endswith('.pkl')]
        
        print(f"   Found {len(model_files)} model files")
        
        successful_loads = 0
        failed_loads = 0
        
        for model_file in model_files:
            model_path = os.path.join(self.models_dir, model_file)
            model_name = model_file.replace('.pkl', '')
            
            try:
                # Load the model using joblib (which handles sklearn pipelines better)
                model = joblib.load(model_path)
                self.loaded_models[model_name] = model
                successful_loads += 1
                
                if successful_loads <= 5:  # Show first 5 loaded models
                    print(f"   ✅ {model_name}")
                elif successful_loads == 6:
                    print("   ... (loading more models)")
                    
            except Exception as e:
                failed_loads += 1
                if failed_loads <= 3:  # Show first 3 failures
                    print(f"   ❌ Failed to load {model_file}: {e}")
        
        print(f"\n📈 Model loading summary:")
        print(f"   Successfully loaded: {successful_loads}")
        print(f"   Failed to load: {failed_loads}")
        print(f"   Total available models: {len(self.loaded_models)}")
        
        if len(self.loaded_models) == 0:
            raise RuntimeError("No models were successfully loaded!")
    
    def predict_with_all_models(self):
        """Generate predictions using all loaded models"""
        print("\n🔮 Generating predictions with all models...")

        comments = self.test_data['Comment'].tolist()
        total_models = len(self.loaded_models)

        print(f"   Testing {len(comments)} comments with {total_models} models")
        print(f"   Total predictions to generate: {len(comments) * total_models:,}")

        # Initialize results storage
        all_predictions = {}
        model_performance = {}

        processed_models = 0
        batch_size = 10  # Process models in batches to show progress

        model_items = list(self.loaded_models.items())

        for i in range(0, len(model_items), batch_size):
            batch = model_items[i:i + batch_size]
            print(f"   Processing batch {i//batch_size + 1}/{(len(model_items) + batch_size - 1)//batch_size}")

            for model_name, model in batch:
                try:
                    if processed_models % 20 == 0:  # Show progress every 20 models
                        print(f"   Progress: {processed_models + 1}/{total_models} models")

                    # Generate predictions
                    predictions = model.predict(comments)

                    # Try to get prediction probabilities if available
                    try:
                        probabilities = model.predict_proba(comments)
                        # Get confidence scores (max probability)
                        confidences = np.max(probabilities, axis=1)
                    except:
                        # If probabilities not available, set confidence to 1.0
                        confidences = np.ones(len(predictions))

                    # Store results
                    all_predictions[model_name] = {
                        'predictions': predictions.tolist(),
                        'confidences': confidences.tolist()
                    }

                    # Calculate basic statistics
                    spam_count = np.sum(predictions == 1)
                    ham_count = np.sum(predictions == 0)
                    avg_confidence = np.mean(confidences)

                    model_performance[model_name] = {
                        'spam_predictions': int(spam_count),
                        'ham_predictions': int(ham_count),
                        'spam_percentage': float(spam_count / len(predictions) * 100),
                        'average_confidence': float(avg_confidence),
                        'model_type': self.get_model_type(model_name)
                    }

                    processed_models += 1

                except Exception as e:
                    print(f"   ❌ Error with model {model_name}: {e}")
                    continue

        self.results['individual_predictions'] = all_predictions
        self.results['model_performance'] = model_performance

        print(f"   ✅ Successfully generated predictions from {processed_models} models")
        
    def get_model_type(self, model_name):
        """Determine the algorithm type from model name"""
        name_lower = model_name.lower()
        
        if 'svm' in name_lower:
            return 'SVM'
        elif 'nb_' in name_lower or 'naive' in name_lower:
            return 'Naive Bayes'
        elif 'lr_' in name_lower or 'logistic' in name_lower:
            return 'Logistic Regression'
        elif 'dt_' in name_lower and 'ada' not in name_lower:
            return 'Decision Tree'
        elif 'rf_' in name_lower or 'random' in name_lower:
            return 'Random Forest'
        elif 'ada' in name_lower:
            return 'AdaBoost'
        elif 'gradient' in name_lower:
            return 'Gradient Boosting'
        elif 'extra' in name_lower:
            return 'Extra Trees'
        else:
            return 'Other'
    
    def create_ensemble_predictions(self):
        """Create ensemble predictions using majority voting"""
        print("\n🗳️  Creating ensemble predictions...")
        
        if not self.results.get('individual_predictions'):
            print("   ❌ No individual predictions available for ensemble")
            return
        
        comments_count = len(self.test_data)
        ensemble_predictions = []
        ensemble_confidences = []
        
        # For each comment, collect all model predictions
        for i in range(comments_count):
            comment_predictions = []
            comment_confidences = []
            
            for model_name, model_results in self.results['individual_predictions'].items():
                pred = model_results['predictions'][i]
                conf = model_results['confidences'][i]
                
                comment_predictions.append(pred)
                comment_confidences.append(conf)
            
            # Majority voting
            prediction_counts = Counter(comment_predictions)
            ensemble_pred = prediction_counts.most_common(1)[0][0]
            
            # Average confidence of models that made the majority prediction
            majority_confidences = [conf for pred, conf in zip(comment_predictions, comment_confidences) 
                                  if pred == ensemble_pred]
            ensemble_conf = np.mean(majority_confidences) if majority_confidences else 0.5
            
            ensemble_predictions.append(ensemble_pred)
            ensemble_confidences.append(ensemble_conf)
        
        # Store ensemble results
        self.results['ensemble'] = {
            'predictions': ensemble_predictions,
            'confidences': ensemble_confidences,
            'spam_count': sum(ensemble_predictions),
            'ham_count': len(ensemble_predictions) - sum(ensemble_predictions),
            'spam_percentage': sum(ensemble_predictions) / len(ensemble_predictions) * 100
        }
        
        print(f"   ✅ Ensemble predictions created")
        print(f"   Spam: {self.results['ensemble']['spam_count']} ({self.results['ensemble']['spam_percentage']:.1f}%)")
        print(f"   Ham: {self.results['ensemble']['ham_count']} ({100 - self.results['ensemble']['spam_percentage']:.1f}%)")

    def analyze_model_agreement(self):
        """Analyze agreement between different models"""
        print("\n📊 Analyzing model agreement...")

        if not self.results.get('individual_predictions'):
            return

        comments_count = len(self.test_data)
        model_names = list(self.results['individual_predictions'].keys())

        # Calculate pairwise agreement
        agreement_matrix = {}

        for i, model1 in enumerate(model_names):
            for j, model2 in enumerate(model_names):
                if i <= j:  # Only calculate upper triangle
                    preds1 = self.results['individual_predictions'][model1]['predictions']
                    preds2 = self.results['individual_predictions'][model2]['predictions']

                    agreement = sum(p1 == p2 for p1, p2 in zip(preds1, preds2)) / len(preds1)
                    agreement_matrix[f"{model1}_{model2}"] = agreement

        # Find highest and lowest agreement pairs
        agreements = list(agreement_matrix.values())
        avg_agreement = np.mean(agreements)

        self.results['model_agreement'] = {
            'average_agreement': float(avg_agreement),
            'min_agreement': float(min(agreements)),
            'max_agreement': float(max(agreements)),
            'total_comparisons': len(agreements)
        }

        print(f"   Average model agreement: {avg_agreement:.3f}")
        print(f"   Agreement range: {min(agreements):.3f} - {max(agreements):.3f}")

    def export_results(self):
        """Export all results to various formats"""
        print("\n💾 Exporting results...")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Create results directory
        results_dir = f"test_results_{timestamp}"
        os.makedirs(results_dir, exist_ok=True)

        # 1. Create detailed predictions CSV
        self.export_detailed_csv(results_dir, timestamp)

        # 2. Create summary Excel file
        self.export_summary_excel(results_dir, timestamp)

        # 3. Export raw results as JSON
        self.export_json_results(results_dir, timestamp)

        # 4. Create analysis report
        self.create_analysis_report(results_dir, timestamp)

        print(f"   ✅ All results exported to: {results_dir}/")

        return results_dir

    def export_detailed_csv(self, results_dir, timestamp):
        """Export detailed predictions to CSV"""

        # Prepare the main dataframe
        df_results = self.test_data.copy()

        # Add ensemble predictions
        if 'ensemble' in self.results:
            df_results['Ensemble_Prediction'] = self.results['ensemble']['predictions']
            df_results['Ensemble_Confidence'] = self.results['ensemble']['confidences']
            df_results['Ensemble_Label'] = ['SPAM' if p == 1 else 'HAM' for p in self.results['ensemble']['predictions']]

        # Add individual model predictions (first 10 models to avoid too many columns)
        model_names = list(self.results['individual_predictions'].keys())[:10]

        for model_name in model_names:
            preds = self.results['individual_predictions'][model_name]['predictions']
            confs = self.results['individual_predictions'][model_name]['confidences']

            df_results[f'{model_name}_Prediction'] = preds
            df_results[f'{model_name}_Confidence'] = confs
            df_results[f'{model_name}_Label'] = ['SPAM' if p == 1 else 'HAM' for p in preds]

        # Save to CSV
        csv_path = os.path.join(results_dir, f"detailed_predictions_{timestamp}.csv")
        df_results.to_csv(csv_path, index=False)
        print(f"   📄 Detailed CSV: {csv_path}")

    def export_summary_excel(self, results_dir, timestamp):
        """Export summary results to Excel with multiple sheets"""

        excel_path = os.path.join(results_dir, f"model_testing_summary_{timestamp}.xlsx")

        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:

            # Sheet 1: Model Performance Summary
            perf_data = []
            for model_name, perf in self.results['model_performance'].items():
                perf_data.append({
                    'Model_Name': model_name,
                    'Model_Type': perf['model_type'],
                    'Spam_Predictions': perf['spam_predictions'],
                    'Ham_Predictions': perf['ham_predictions'],
                    'Spam_Percentage': perf['spam_percentage'],
                    'Average_Confidence': perf['average_confidence']
                })

            df_performance = pd.DataFrame(perf_data)
            df_performance.to_excel(writer, sheet_name='Model_Performance', index=False)

            # Sheet 2: Algorithm Type Summary
            algo_summary = df_performance.groupby('Model_Type').agg({
                'Spam_Percentage': ['mean', 'std', 'min', 'max'],
                'Average_Confidence': ['mean', 'std'],
                'Model_Name': 'count'
            }).round(3)

            algo_summary.columns = ['Spam_Pct_Mean', 'Spam_Pct_Std', 'Spam_Pct_Min', 'Spam_Pct_Max',
                                  'Confidence_Mean', 'Confidence_Std', 'Model_Count']
            algo_summary.to_excel(writer, sheet_name='Algorithm_Summary')

            # Sheet 3: Ensemble Results
            if 'ensemble' in self.results:
                ensemble_data = {
                    'Metric': ['Total_Comments', 'Spam_Count', 'Ham_Count', 'Spam_Percentage', 'Average_Confidence'],
                    'Value': [
                        len(self.test_data),
                        self.results['ensemble']['spam_count'],
                        self.results['ensemble']['ham_count'],
                        self.results['ensemble']['spam_percentage'],
                        np.mean(self.results['ensemble']['confidences'])
                    ]
                }
                df_ensemble = pd.DataFrame(ensemble_data)
                df_ensemble.to_excel(writer, sheet_name='Ensemble_Summary', index=False)

        print(f"   📊 Summary Excel: {excel_path}")

    def export_json_results(self, results_dir, timestamp):
        """Export raw results as JSON"""

        json_path = os.path.join(results_dir, f"raw_results_{timestamp}.json")

        # Prepare JSON-serializable results
        json_results = {
            'metadata': {
                'test_dataset': self.test_data_path,
                'models_directory': self.models_dir,
                'timestamp': timestamp,
                'total_comments': len(self.test_data),
                'total_models_tested': len(self.results.get('individual_predictions', {}))
            },
            'model_performance': self.results.get('model_performance', {}),
            'ensemble_summary': self.results.get('ensemble', {}),
            'model_agreement': self.results.get('model_agreement', {})
        }

        with open(json_path, 'w') as f:
            json.dump(json_results, f, indent=2)

        print(f"   🔧 JSON results: {json_path}")

    def create_analysis_report(self, results_dir, timestamp):
        """Create a human-readable analysis report"""

        report_path = os.path.join(results_dir, f"analysis_report_{timestamp}.txt")

        with open(report_path, 'w') as f:
            f.write("COMPREHENSIVE MODEL TESTING ANALYSIS REPORT\n")
            f.write("=" * 50 + "\n\n")

            f.write(f"Test Dataset: {self.test_data_path}\n")
            f.write(f"Models Directory: {self.models_dir}\n")
            f.write(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # Dataset summary
            f.write("DATASET SUMMARY\n")
            f.write("-" * 20 + "\n")
            f.write(f"Total Comments: {len(self.test_data)}\n")
            f.write(f"Columns: {', '.join(self.test_data.columns)}\n\n")

            # Model testing summary
            f.write("MODEL TESTING SUMMARY\n")
            f.write("-" * 25 + "\n")
            f.write(f"Total Models Tested: {len(self.results.get('individual_predictions', {}))}\n")

            if 'model_performance' in self.results:
                # Algorithm type breakdown
                algo_counts = {}
                for model_name, perf in self.results['model_performance'].items():
                    algo_type = perf['model_type']
                    algo_counts[algo_type] = algo_counts.get(algo_type, 0) + 1

                f.write("\nAlgorithm Distribution:\n")
                for algo, count in sorted(algo_counts.items()):
                    f.write(f"  {algo}: {count} models\n")

            # Ensemble results
            if 'ensemble' in self.results:
                f.write("\nENSEMBLE PREDICTIONS\n")
                f.write("-" * 25 + "\n")
                f.write(f"Spam Comments: {self.results['ensemble']['spam_count']} ({self.results['ensemble']['spam_percentage']:.1f}%)\n")
                f.write(f"Ham Comments: {self.results['ensemble']['ham_count']} ({100 - self.results['ensemble']['spam_percentage']:.1f}%)\n")
                f.write(f"Average Confidence: {np.mean(self.results['ensemble']['confidences']):.3f}\n")

            # Model agreement
            if 'model_agreement' in self.results:
                f.write("\nMODEL AGREEMENT ANALYSIS\n")
                f.write("-" * 30 + "\n")
                f.write(f"Average Agreement: {self.results['model_agreement']['average_agreement']:.3f}\n")
                f.write(f"Agreement Range: {self.results['model_agreement']['min_agreement']:.3f} - {self.results['model_agreement']['max_agreement']:.3f}\n")

        print(f"   📝 Analysis report: {report_path}")

    def run_comprehensive_testing(self):
        """Run the complete testing pipeline"""
        print("\n🎯 STARTING COMPREHENSIVE MODEL TESTING")
        print("=" * 60)

        try:
            # Step 1: Load test data
            self.load_test_data()

            # Step 2: Load all models
            self.load_models()

            # Step 3: Generate predictions
            self.predict_with_all_models()

            # Step 4: Create ensemble predictions
            self.create_ensemble_predictions()

            # Step 5: Analyze model agreement
            self.analyze_model_agreement()

            # Step 6: Export results
            results_dir = self.export_results()

            print("\n🎉 COMPREHENSIVE TESTING COMPLETED SUCCESSFULLY!")
            print("=" * 60)
            print(f"📁 Results saved to: {results_dir}/")

            return results_dir

        except Exception as e:
            print(f"\n❌ Error during testing: {e}")
            raise

def main():
    """Main function to run comprehensive model testing"""

    # Initialize and run the tester
    tester = ComprehensiveModelTester()
    results_dir = tester.run_comprehensive_testing()

    print(f"\n📋 QUICK SUMMARY:")
    if tester.results.get('ensemble'):
        print(f"   Total comments tested: {len(tester.test_data)}")
        print(f"   Models used: {len(tester.loaded_models)}")
        print(f"   Ensemble spam detection: {tester.results['ensemble']['spam_count']} comments ({tester.results['ensemble']['spam_percentage']:.1f}%)")
        print(f"   Results directory: {results_dir}")

if __name__ == "__main__":
    main()
