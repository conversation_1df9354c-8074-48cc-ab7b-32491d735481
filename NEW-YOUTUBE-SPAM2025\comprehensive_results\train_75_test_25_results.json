{"SVM_Linear": {"accuracy": 0.9038854805725971, "precision": [0.8715953307392996, 0.9396551724137931], "recall": [0.9411764705882353, 0.8685258964143426], "f1_score": [0.9050505050505051, 0.9026915113871635], "support": [238, 251], "classification_report": {"0": {"precision": 0.8715953307392996, "recall": 0.9411764705882353, "f1-score": 0.9050505050505051, "support": 238.0}, "1": {"precision": 0.9396551724137931, "recall": 0.8685258964143426, "f1-score": 0.9026915113871635, "support": 251.0}, "accuracy": 0.9038854805725971, "macro avg": {"precision": 0.9056252515765464, "recall": 0.9048511835012889, "f1-score": 0.9038710082188344, "support": 489.0}, "weighted avg": {"precision": 0.9065299324986, "recall": 0.9038854805725971, "f1-score": 0.9038396514523482, "support": 489.0}}, "confusion_matrix": [[224, 14], [33, 218]], "roc_auc": 0.9646288794402224, "training_time": 0.410989, "train_size": 1467, "test_size": 489, "algorithm_type": "Support Vector Machine"}, "SVM_Polynomial": {"accuracy": 0.8834355828220859, "precision": [0.8851063829787233, 0.8818897637795275], "recall": [0.8739495798319328, 0.8924302788844621], "f1_score": [0.879492600422833, 0.8871287128712871], "support": [238, 251], "classification_report": {"0": {"precision": 0.8851063829787233, "recall": 0.8739495798319328, "f1-score": 0.879492600422833, "support": 238.0}, "1": {"precision": 0.8818897637795275, "recall": 0.8924302788844621, "f1-score": 0.8871287128712871, "support": 251.0}, "accuracy": 0.8834355828220859, "macro avg": {"precision": 0.8834980733791254, "recall": 0.8831899293581975, "f1-score": 0.8833106566470601, "support": 489.0}, "weighted avg": {"precision": 0.8834553166822036, "recall": 0.8834355828220859, "f1-score": 0.8834121591642685, "support": 489.0}}, "confusion_matrix": [[208, 30], [27, 224]], "roc_auc": 0.9406491680337474, "training_time": 0.707755, "train_size": 1467, "test_size": 489, "algorithm_type": "Support Vector Machine"}, "SVM_RBF": {"accuracy": 0.918200408997955, "precision": [0.9090909090909091, 0.9271255060728745], "recall": [0.9243697478991597, 0.9123505976095617], "f1_score": [0.9166666666666666, 0.9196787148594378], "support": [238, 251], "classification_report": {"0": {"precision": 0.9090909090909091, "recall": 0.9243697478991597, "f1-score": 0.9166666666666666, "support": 238.0}, "1": {"precision": 0.9271255060728745, "recall": 0.9123505976095617, "f1-score": 0.9196787148594378, "support": 251.0}, "accuracy": 0.918200408997955, "macro avg": {"precision": 0.9181082075818918, "recall": 0.9183601727543607, "f1-score": 0.9181726907630522, "support": 489.0}, "weighted avg": {"precision": 0.9183479312636561, "recall": 0.918200408997955, "f1-score": 0.9182127282134673, "support": 489.0}}, "confusion_matrix": [[220, 18], [22, 229]], "roc_auc": 0.9517643710870803, "training_time": 0.659157, "train_size": 1467, "test_size": 489, "algorithm_type": "Support Vector Machine"}, "SVM_Sigmoid": {"accuracy": 0.9120654396728016, "precision": [0.8735632183908046, 0.956140350877193], "recall": [0.957983193277311, 0.8685258964143426], "f1_score": [0.9138276553106213, 0.9102296450939458], "support": [238, 251], "classification_report": {"0": {"precision": 0.8735632183908046, "recall": 0.957983193277311, "f1-score": 0.9138276553106213, "support": 238.0}, "1": {"precision": 0.956140350877193, "recall": 0.8685258964143426, "f1-score": 0.9102296450939458, "support": 251.0}, "accuracy": 0.9120654396728016, "macro avg": {"precision": 0.9148517846339987, "recall": 0.9132545448458268, "f1-score": 0.9120286502022835, "support": 489.0}, "weighted avg": {"precision": 0.915949435679319, "recall": 0.9120654396728016, "f1-score": 0.91198082389061, "support": 489.0}}, "confusion_matrix": [[228, 10], [33, 218]], "roc_auc": 0.9652315109310656, "training_time": 0.392051, "train_size": 1467, "test_size": 489, "algorithm_type": "Support Vector Machine"}, "SVM_RBF_Tuned": {"accuracy": 0.918200408997955, "precision": [0.9125, 0.9236947791164659], "recall": [0.9201680672268907, 0.9163346613545816], "f1_score": [0.9163179916317992, 0.92], "support": [238, 251], "classification_report": {"0": {"precision": 0.9125, "recall": 0.9201680672268907, "f1-score": 0.9163179916317992, "support": 238.0}, "1": {"precision": 0.9236947791164659, "recall": 0.9163346613545816, "f1-score": 0.92, "support": 251.0}, "accuracy": 0.918200408997955, "macro avg": {"precision": 0.918097389558233, "recall": 0.9182513642907362, "f1-score": 0.9181589958158995, "support": 489.0}, "weighted avg": {"precision": 0.9182461954156093, "recall": 0.918200408997955, "f1-score": 0.9182079386674197, "support": 489.0}}, "confusion_matrix": [[219, 19], [21, 230]], "roc_auc": 0.9509608624326225, "training_time": 0.700371, "train_size": 1467, "test_size": 489, "algorithm_type": "Support Vector Machine"}, "SVM_Linear_Tuned": {"accuracy": 0.9038854805725971, "precision": [0.8715953307392996, 0.9396551724137931], "recall": [0.9411764705882353, 0.8685258964143426], "f1_score": [0.9050505050505051, 0.9026915113871635], "support": [238, 251], "classification_report": {"0": {"precision": 0.8715953307392996, "recall": 0.9411764705882353, "f1-score": 0.9050505050505051, "support": 238.0}, "1": {"precision": 0.9396551724137931, "recall": 0.8685258964143426, "f1-score": 0.9026915113871635, "support": 251.0}, "accuracy": 0.9038854805725971, "macro avg": {"precision": 0.9056252515765464, "recall": 0.9048511835012889, "f1-score": 0.9038710082188344, "support": 489.0}, "weighted avg": {"precision": 0.9065299324986, "recall": 0.9038854805725971, "f1-score": 0.9038396514523482, "support": 489.0}}, "confusion_matrix": [[224, 14], [33, 218]], "roc_auc": 0.9646288794402224, "training_time": 0.423068, "train_size": 1467, "test_size": 489, "algorithm_type": "Support Vector Machine"}, "NB_Multinomial": {"accuracy": 0.8997955010224948, "precision": [0.9276018099547512, 0.8768656716417911], "recall": [0.8613445378151261, 0.9362549800796812], "f1_score": [0.8932461873638344, 0.905587668593449], "support": [238, 251], "classification_report": {"0": {"precision": 0.9276018099547512, "recall": 0.8613445378151261, "f1-score": 0.8932461873638344, "support": 238.0}, "1": {"precision": 0.8768656716417911, "recall": 0.9362549800796812, "f1-score": 0.905587668593449, "support": 251.0}, "accuracy": 0.8997955010224948, "macro avg": {"precision": 0.9022337407982711, "recall": 0.8987997589474037, "f1-score": 0.8994169279786417, "support": 489.0}, "weighted avg": {"precision": 0.9015593340517798, "recall": 0.8997955010224948, "f1-score": 0.8995809762976448, "support": 489.0}}, "confusion_matrix": [[205, 33], [16, 235]], "roc_auc": 0.9589792092135659, "training_time": 0.025735, "train_size": 1467, "test_size": 489, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli": {"accuracy": 0.8445807770961146, "precision": [0.7682119205298014, 0.9679144385026738], "recall": [0.9747899159663865, 0.7211155378486056], "f1_score": [0.8592592592592593, 0.8264840182648402], "support": [238, 251], "classification_report": {"0": {"precision": 0.7682119205298014, "recall": 0.9747899159663865, "f1-score": 0.8592592592592593, "support": 238.0}, "1": {"precision": 0.9679144385026738, "recall": 0.7211155378486056, "f1-score": 0.8264840182648402, "support": 251.0}, "accuracy": 0.8445807770961146, "macro avg": {"precision": 0.8680631795162376, "recall": 0.847952726907496, "f1-score": 0.8428716387620497, "support": 489.0}, "weighted avg": {"precision": 0.8707177119637297, "recall": 0.8445807770961146, "f1-score": 0.842435976049445, "support": 489.0}}, "confusion_matrix": [[232, 6], [70, 181]], "roc_auc": 0.9440891894606448, "training_time": 0.022064, "train_size": 1467, "test_size": 489, "algorithm_type": "<PERSON><PERSON>"}, "NB_Complement": {"accuracy": 0.8875255623721882, "precision": [0.8674698795180723, 0.9083333333333333], "recall": [0.907563025210084, 0.8685258964143426], "f1_score": [0.8870636550308009, 0.8879837067209776], "support": [238, 251], "classification_report": {"0": {"precision": 0.8674698795180723, "recall": 0.907563025210084, "f1-score": 0.8870636550308009, "support": 238.0}, "1": {"precision": 0.9083333333333333, "recall": 0.8685258964143426, "f1-score": 0.8879837067209776, "support": 251.0}, "accuracy": 0.8875255623721882, "macro avg": {"precision": 0.8879016064257028, "recall": 0.8880444608122133, "f1-score": 0.8875236808758893, "support": 489.0}, "weighted avg": {"precision": 0.8884447811696684, "recall": 0.8875255623721882, "f1-score": 0.8875359106018322, "support": 489.0}}, "confusion_matrix": [[216, 22], [33, 218]], "roc_auc": 0.9589792092135659, "training_time": 0.034594, "train_size": 1467, "test_size": 489, "algorithm_type": "<PERSON><PERSON>"}, "NB_Multinomial_Tuned": {"accuracy": 0.8936605316973415, "precision": [0.9151785714285714, 0.8754716981132076], "recall": [0.8613445378151261, 0.9243027888446215], "f1_score": [0.8874458874458875, 0.8992248062015504], "support": [238, 251], "classification_report": {"0": {"precision": 0.9151785714285714, "recall": 0.8613445378151261, "f1-score": 0.8874458874458875, "support": 238.0}, "1": {"precision": 0.8754716981132076, "recall": 0.9243027888446215, "f1-score": 0.8992248062015504, "support": 251.0}, "accuracy": 0.8936605316973415, "macro avg": {"precision": 0.8953251347708895, "recall": 0.8928236633298738, "f1-score": 0.893335346823719, "support": 489.0}, "weighted avg": {"precision": 0.89479733379635, "recall": 0.8936605316973415, "f1-score": 0.893491917318426, "support": 489.0}}, "confusion_matrix": [[205, 33], [19, 232]], "roc_auc": 0.9531872509960159, "training_time": 0.004936, "train_size": 1467, "test_size": 489, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli_Tuned": {"accuracy": 0.8752556237218814, "precision": [0.8218181818181818, 0.9439252336448598], "recall": [0.9495798319327731, 0.8047808764940239], "f1_score": [0.8810916179337231, 0.8688172043010752], "support": [238, 251], "classification_report": {"0": {"precision": 0.8218181818181818, "recall": 0.9495798319327731, "f1-score": 0.8810916179337231, "support": 238.0}, "1": {"precision": 0.9439252336448598, "recall": 0.8047808764940239, "f1-score": 0.8688172043010752, "support": 251.0}, "accuracy": 0.8752556237218814, "macro avg": {"precision": 0.8828717077315208, "recall": 0.8771803542133985, "f1-score": 0.8749544111173992, "support": 489.0}, "weighted avg": {"precision": 0.8844948076024275, "recall": 0.8752556237218814, "f1-score": 0.8747912542899714, "support": 489.0}}, "confusion_matrix": [[226, 12], [49, 202]], "roc_auc": 0.9344805651344203, "training_time": 0.011625, "train_size": 1467, "test_size": 489, "algorithm_type": "<PERSON><PERSON>"}, "LR_L1": {"accuracy": 0.8773006134969326, "precision": [0.8201438848920863, 0.95260663507109], "recall": [0.957983193277311, 0.8007968127490039], "f1_score": [0.8837209302325582, 0.8701298701298701], "support": [238, 251], "classification_report": {"0": {"precision": 0.8201438848920863, "recall": 0.957983193277311, "f1-score": 0.8837209302325582, "support": 238.0}, "1": {"precision": 0.95260663507109, "recall": 0.8007968127490039, "f1-score": 0.8701298701298701, "support": 251.0}, "accuracy": 0.8773006134969326, "macro avg": {"precision": 0.8863752599815882, "recall": 0.8793900030131574, "f1-score": 0.8769254001812141, "support": 489.0}, "weighted avg": {"precision": 0.8881360122845811, "recall": 0.8773006134969326, "f1-score": 0.8767447419180905, "support": 489.0}}, "confusion_matrix": [[228, 10], [50, 201]], "roc_auc": 0.9432187217516489, "training_time": 0.037543, "train_size": 1467, "test_size": 489, "algorithm_type": "Logistic Regression"}, "LR_L2": {"accuracy": 0.9059304703476483, "precision": [0.8692307692307693, 0.9475982532751092], "recall": [0.9495798319327731, 0.8645418326693227], "f1_score": [0.9076305220883534, 0.9041666666666667], "support": [238, 251], "classification_report": {"0": {"precision": 0.8692307692307693, "recall": 0.9495798319327731, "f1-score": 0.9076305220883534, "support": 238.0}, "1": {"precision": 0.9475982532751092, "recall": 0.8645418326693227, "f1-score": 0.9041666666666667, "support": 251.0}, "accuracy": 0.9059304703476483, "macro avg": {"precision": 0.9084145112529391, "recall": 0.9070608323010478, "f1-score": 0.90589859437751, "support": 489.0}, "weighted avg": {"precision": 0.9094562058261257, "recall": 0.9059304703476483, "f1-score": 0.9058525513095326, "support": 489.0}}, "confusion_matrix": [[226, 12], [34, 217]], "roc_auc": 0.9571043556864977, "training_time": 0.035401, "train_size": 1467, "test_size": 489, "algorithm_type": "Logistic Regression"}, "LR_ElasticNet": {"accuracy": 0.885480572597137, "precision": [0.8395522388059702, 0.9411764705882353], "recall": [0.9453781512605042, 0.8286852589641435], "f1_score": [0.8893280632411067, 0.8813559322033898], "support": [238, 251], "classification_report": {"0": {"precision": 0.8395522388059702, "recall": 0.9453781512605042, "f1-score": 0.8893280632411067, "support": 238.0}, "1": {"precision": 0.9411764705882353, "recall": 0.8286852589641435, "f1-score": 0.8813559322033898, "support": 251.0}, "accuracy": 0.885480572597137, "macro avg": {"precision": 0.8903643546971027, "recall": 0.8870317051123238, "f1-score": 0.8853419977222483, "support": 489.0}, "weighted avg": {"precision": 0.8917151880439018, "recall": 0.885480572597137, "f1-score": 0.8852360287002745, "support": 489.0}}, "confusion_matrix": [[225, 13], [43, 208]], "roc_auc": 0.9490357896146506, "training_time": 0.062065, "train_size": 1467, "test_size": 489, "algorithm_type": "Logistic Regression"}, "LR_LBFGS": {"accuracy": 0.9059304703476483, "precision": [0.8692307692307693, 0.9475982532751092], "recall": [0.9495798319327731, 0.8645418326693227], "f1_score": [0.9076305220883534, 0.9041666666666667], "support": [238, 251], "classification_report": {"0": {"precision": 0.8692307692307693, "recall": 0.9495798319327731, "f1-score": 0.9076305220883534, "support": 238.0}, "1": {"precision": 0.9475982532751092, "recall": 0.8645418326693227, "f1-score": 0.9041666666666667, "support": 251.0}, "accuracy": 0.9059304703476483, "macro avg": {"precision": 0.9084145112529391, "recall": 0.9070608323010478, "f1-score": 0.90589859437751, "support": 489.0}, "weighted avg": {"precision": 0.9094562058261257, "recall": 0.9059304703476483, "f1-score": 0.9058525513095326, "support": 489.0}}, "confusion_matrix": [[226, 12], [34, 217]], "roc_auc": 0.957271753322843, "training_time": 0.023275, "train_size": 1467, "test_size": 489, "algorithm_type": "Logistic Regression"}, "LR_SAG": {"accuracy": 0.9059304703476483, "precision": [0.8692307692307693, 0.9475982532751092], "recall": [0.9495798319327731, 0.8645418326693227], "f1_score": [0.9076305220883534, 0.9041666666666667], "support": [238, 251], "classification_report": {"0": {"precision": 0.8692307692307693, "recall": 0.9495798319327731, "f1-score": 0.9076305220883534, "support": 238.0}, "1": {"precision": 0.9475982532751092, "recall": 0.8645418326693227, "f1-score": 0.9041666666666667, "support": 251.0}, "accuracy": 0.9059304703476483, "macro avg": {"precision": 0.9084145112529391, "recall": 0.9070608323010478, "f1-score": 0.90589859437751, "support": 489.0}, "weighted avg": {"precision": 0.9094562058261257, "recall": 0.9059304703476483, "f1-score": 0.9058525513095326, "support": 489.0}}, "confusion_matrix": [[226, 12], [34, 217]], "roc_auc": 0.9571378352137667, "training_time": 0.03352, "train_size": 1467, "test_size": 489, "algorithm_type": "Logistic Regression"}, "LR_SAGA": {"accuracy": 0.9059304703476483, "precision": [0.8692307692307693, 0.9475982532751092], "recall": [0.9495798319327731, 0.8645418326693227], "f1_score": [0.9076305220883534, 0.9041666666666667], "support": [238, 251], "classification_report": {"0": {"precision": 0.8692307692307693, "recall": 0.9495798319327731, "f1-score": 0.9076305220883534, "support": 238.0}, "1": {"precision": 0.9475982532751092, "recall": 0.8645418326693227, "f1-score": 0.9041666666666667, "support": 251.0}, "accuracy": 0.9059304703476483, "macro avg": {"precision": 0.9084145112529391, "recall": 0.9070608323010478, "f1-score": 0.90589859437751, "support": 489.0}, "weighted avg": {"precision": 0.9094562058261257, "recall": 0.9059304703476483, "f1-score": 0.9058525513095326, "support": 489.0}}, "confusion_matrix": [[226, 12], [34, 217]], "roc_auc": 0.9571210954501322, "training_time": 0.032705, "train_size": 1467, "test_size": 489, "algorithm_type": "Logistic Regression"}, "DT_Gini": {"accuracy": 0.8752556237218814, "precision": [0.9359605911330049, 0.8321678321678322], "recall": [0.7983193277310925, 0.9482071713147411], "f1_score": [0.8616780045351474, 0.8864059590316573], "support": [238, 251], "classification_report": {"0": {"precision": 0.9359605911330049, "recall": 0.7983193277310925, "f1-score": 0.8616780045351474, "support": 238.0}, "1": {"precision": 0.8321678321678322, "recall": 0.9482071713147411, "f1-score": 0.8864059590316573, "support": 251.0}, "accuracy": 0.8752556237218814, "macro avg": {"precision": 0.8840642116504185, "recall": 0.8732632495229168, "f1-score": 0.8740419817834024, "support": 489.0}, "weighted avg": {"precision": 0.8826845533001657, "recall": 0.8752556237218814, "f1-score": 0.8743706764750738, "support": 489.0}}, "confusion_matrix": [[190, 48], [13, 238]], "roc_auc": 0.9408918946064482, "training_time": 0.085818, "train_size": 1467, "test_size": 489, "algorithm_type": "Decision Tree"}, "DT_Entropy": {"accuracy": 0.852760736196319, "precision": [0.8990384615384616, 0.8185053380782918], "recall": [0.7857142857142857, 0.9163346613545816], "f1_score": [0.8385650224215246, 0.8646616541353384], "support": [238, 251], "classification_report": {"0": {"precision": 0.8990384615384616, "recall": 0.7857142857142857, "f1-score": 0.8385650224215246, "support": 238.0}, "1": {"precision": 0.8185053380782918, "recall": 0.9163346613545816, "f1-score": 0.8646616541353384, "support": 251.0}, "accuracy": 0.852760736196319, "macro avg": {"precision": 0.8587718998083766, "recall": 0.8510244735344337, "f1-score": 0.8516133382784314, "support": 489.0}, "weighted avg": {"precision": 0.8577014186171883, "recall": 0.852760736196319, "f1-score": 0.8519602260210487, "support": 489.0}}, "confusion_matrix": [[187, 51], [21, 230]], "roc_auc": 0.9155227828183066, "training_time": 0.084084, "train_size": 1467, "test_size": 489, "algorithm_type": "Decision Tree"}, "DT_Log_Loss": {"accuracy": 0.852760736196319, "precision": [0.8990384615384616, 0.8185053380782918], "recall": [0.7857142857142857, 0.9163346613545816], "f1_score": [0.8385650224215246, 0.8646616541353384], "support": [238, 251], "classification_report": {"0": {"precision": 0.8990384615384616, "recall": 0.7857142857142857, "f1-score": 0.8385650224215246, "support": 238.0}, "1": {"precision": 0.8185053380782918, "recall": 0.9163346613545816, "f1-score": 0.8646616541353384, "support": 251.0}, "accuracy": 0.852760736196319, "macro avg": {"precision": 0.8587718998083766, "recall": 0.8510244735344337, "f1-score": 0.8516133382784314, "support": 489.0}, "weighted avg": {"precision": 0.8577014186171883, "recall": 0.852760736196319, "f1-score": 0.8519602260210487, "support": 489.0}}, "confusion_matrix": [[187, 51], [21, 230]], "roc_auc": 0.9155227828183066, "training_time": 0.093953, "train_size": 1467, "test_size": 489, "algorithm_type": "Decision Tree"}, "DT_Gini_Pruned": {"accuracy": 0.8875255623721882, "precision": [0.818815331010453, 0.9851485148514851], "recall": [0.9873949579831933, 0.7928286852589641], "f1_score": [0.8952380952380953, 0.8785871964679912], "support": [238, 251], "classification_report": {"0": {"precision": 0.818815331010453, "recall": 0.9873949579831933, "f1-score": 0.8952380952380953, "support": 238.0}, "1": {"precision": 0.9851485148514851, "recall": 0.7928286852589641, "f1-score": 0.8785871964679912, "support": 251.0}, "accuracy": 0.8875255623721882, "macro avg": {"precision": 0.9019819229309691, "recall": 0.8901118216210787, "f1-score": 0.8869126458530432, "support": 489.0}, "weighted avg": {"precision": 0.904192895722312, "recall": 0.8875255623721882, "f1-score": 0.8866913148877964, "support": 489.0}}, "confusion_matrix": [[235, 3], [52, 199]], "roc_auc": 0.8881532692758377, "training_time": 0.051334, "train_size": 1467, "test_size": 489, "algorithm_type": "Decision Tree"}, "DT_Entropy_Pruned": {"accuracy": 0.8670756646216768, "precision": [0.7912457912457912, 0.984375], "recall": [0.9873949579831933, 0.7529880478087649], "f1_score": [0.8785046728971962, 0.8532731376975169], "support": [238, 251], "classification_report": {"0": {"precision": 0.7912457912457912, "recall": 0.9873949579831933, "f1-score": 0.8785046728971962, "support": 238.0}, "1": {"precision": 0.984375, "recall": 0.7529880478087649, "f1-score": 0.8532731376975169, "support": 251.0}, "accuracy": 0.8670756646216768, "macro avg": {"precision": 0.8878103956228955, "recall": 0.8701915028959791, "f1-score": 0.8658889052973566, "support": 489.0}, "weighted avg": {"precision": 0.890377552794475, "recall": 0.8670756646216768, "f1-score": 0.8655535167926574, "support": 489.0}}, "confusion_matrix": [[235, 3], [62, 189]], "roc_auc": 0.8742843751046235, "training_time": 0.040649, "train_size": 1467, "test_size": 489, "algorithm_type": "Decision Tree"}, "DT_Best_First": {"accuracy": 0.8752556237218814, "precision": [0.9359605911330049, 0.8321678321678322], "recall": [0.7983193277310925, 0.9482071713147411], "f1_score": [0.8616780045351474, 0.8864059590316573], "support": [238, 251], "classification_report": {"0": {"precision": 0.9359605911330049, "recall": 0.7983193277310925, "f1-score": 0.8616780045351474, "support": 238.0}, "1": {"precision": 0.8321678321678322, "recall": 0.9482071713147411, "f1-score": 0.8864059590316573, "support": 251.0}, "accuracy": 0.8752556237218814, "macro avg": {"precision": 0.8840642116504185, "recall": 0.8732632495229168, "f1-score": 0.8740419817834024, "support": 489.0}, "weighted avg": {"precision": 0.8826845533001657, "recall": 0.8752556237218814, "f1-score": 0.8743706764750738, "support": 489.0}}, "confusion_matrix": [[190, 48], [13, 238]], "roc_auc": 0.9408918946064482, "training_time": 0.084186, "train_size": 1467, "test_size": 489, "algorithm_type": "Decision Tree"}, "DT_Random_Split": {"accuracy": 0.8752556237218814, "precision": [0.9402985074626866, 0.8298611111111112], "recall": [0.7941176470588235, 0.952191235059761], "f1_score": [0.8610478359908884, 0.8868274582560297], "support": [238, 251], "classification_report": {"0": {"precision": 0.9402985074626866, "recall": 0.7941176470588235, "f1-score": 0.8610478359908884, "support": 238.0}, "1": {"precision": 0.8298611111111112, "recall": 0.952191235059761, "f1-score": 0.8868274582560297, "support": 251.0}, "accuracy": 0.8752556237218814, "macro avg": {"precision": 0.8850798092868989, "recall": 0.8731544410592922, "f1-score": 0.873937647123459, "support": 489.0}, "weighted avg": {"precision": 0.8836118275358044, "recall": 0.8752556237218814, "f1-score": 0.8742803210390488, "support": 489.0}}, "confusion_matrix": [[189, 49], [12, 239]], "roc_auc": 0.9377448190431552, "training_time": 0.077542, "train_size": 1467, "test_size": 489, "algorithm_type": "Decision Tree"}, "RF_Gini": {"accuracy": 0.8936605316973415, "precision": [0.9428571428571428, 0.8566308243727598], "recall": [0.8319327731092437, 0.952191235059761], "f1_score": [0.8839285714285714, 0.9018867924528302], "support": [238, 251], "classification_report": {"0": {"precision": 0.9428571428571428, "recall": 0.8319327731092437, "f1-score": 0.8839285714285714, "support": 238.0}, "1": {"precision": 0.8566308243727598, "recall": 0.952191235059761, "f1-score": 0.9018867924528302, "support": 251.0}, "accuracy": 0.8936605316973415, "macro avg": {"precision": 0.8997439836149513, "recall": 0.8920620040845024, "f1-score": 0.8929076819407008, "support": 489.0}, "weighted avg": {"precision": 0.8985978260072858, "recall": 0.8936605316973415, "f1-score": 0.8931463904001234, "support": 489.0}}, "confusion_matrix": [[198, 40], [12, 239]], "roc_auc": 0.9604857879406741, "training_time": 0.611209, "train_size": 1467, "test_size": 489, "algorithm_type": "Random Forest"}, "RF_Entropy": {"accuracy": 0.8813905930470347, "precision": [0.9368932038834952, 0.8409893992932862], "recall": [0.8109243697478992, 0.9482071713147411], "f1_score": [0.8693693693693694, 0.8913857677902621], "support": [238, 251], "classification_report": {"0": {"precision": 0.9368932038834952, "recall": 0.8109243697478992, "f1-score": 0.8693693693693694, "support": 238.0}, "1": {"precision": 0.8409893992932862, "recall": 0.9482071713147411, "f1-score": 0.8913857677902621, "support": 251.0}, "accuracy": 0.8813905930470347, "macro avg": {"precision": 0.8889413015883907, "recall": 0.8795657705313201, "f1-score": 0.8803775685798158, "support": 489.0}, "weighted avg": {"precision": 0.8876665066398501, "recall": 0.8813905930470347, "f1-score": 0.8806702200925679, "support": 489.0}}, "confusion_matrix": [[193, 45], [13, 238]], "roc_auc": 0.9620007365495999, "training_time": 0.616691, "train_size": 1467, "test_size": 489, "algorithm_type": "Random Forest"}, "RF_Log_Loss": {"accuracy": 0.8813905930470347, "precision": [0.9368932038834952, 0.8409893992932862], "recall": [0.8109243697478992, 0.9482071713147411], "f1_score": [0.8693693693693694, 0.8913857677902621], "support": [238, 251], "classification_report": {"0": {"precision": 0.9368932038834952, "recall": 0.8109243697478992, "f1-score": 0.8693693693693694, "support": 238.0}, "1": {"precision": 0.8409893992932862, "recall": 0.9482071713147411, "f1-score": 0.8913857677902621, "support": 251.0}, "accuracy": 0.8813905930470347, "macro avg": {"precision": 0.8889413015883907, "recall": 0.8795657705313201, "f1-score": 0.8803775685798158, "support": 489.0}, "weighted avg": {"precision": 0.8876665066398501, "recall": 0.8813905930470347, "f1-score": 0.8806702200925679, "support": 489.0}}, "confusion_matrix": [[193, 45], [13, 238]], "roc_auc": 0.9620007365495999, "training_time": 0.633383, "train_size": 1467, "test_size": 489, "algorithm_type": "Random Forest"}, "RF_Large": {"accuracy": 0.8752556237218814, "precision": [0.8083623693379791, 0.9702970297029703], "recall": [0.9747899159663865, 0.7808764940239044], "f1_score": [0.8838095238095238, 0.8653421633554084], "support": [238, 251], "classification_report": {"0": {"precision": 0.8083623693379791, "recall": 0.9747899159663865, "f1-score": 0.8838095238095238, "support": 238.0}, "1": {"precision": 0.9702970297029703, "recall": 0.7808764940239044, "f1-score": 0.8653421633554084, "support": 251.0}, "accuracy": 0.8752556237218814, "macro avg": {"precision": 0.8893296995204747, "recall": 0.8778332049951454, "f1-score": 0.8745758435824661, "support": 489.0}, "weighted avg": {"precision": 0.8914822052308478, "recall": 0.8752556237218814, "f1-score": 0.8743303674210104, "support": 489.0}}, "confusion_matrix": [[232, 6], [55, 196]], "roc_auc": 0.9502243128327028, "training_time": 0.429908, "train_size": 1467, "test_size": 489, "algorithm_type": "Random Forest"}, "RF_Small": {"accuracy": 0.8773006134969326, "precision": [0.8156028368794326, 0.961352657004831], "recall": [0.9663865546218487, 0.7928286852589641], "f1_score": [0.8846153846153846, 0.868995633187773], "support": [238, 251], "classification_report": {"0": {"precision": 0.8156028368794326, "recall": 0.9663865546218487, "f1-score": 0.8846153846153846, "support": 238.0}, "1": {"precision": 0.961352657004831, "recall": 0.7928286852589641, "f1-score": 0.868995633187773, "support": 251.0}, "accuracy": 0.8773006134969326, "macro avg": {"precision": 0.8884777469421318, "recall": 0.8796076199404064, "f1-score": 0.8768055089015787, "support": 489.0}, "weighted avg": {"precision": 0.8904151167392996, "recall": 0.8773006134969326, "f1-score": 0.8765978843938498, "support": 489.0}}, "confusion_matrix": [[230, 8], [52, 199]], "roc_auc": 0.9426411999062574, "training_time": 0.131241, "train_size": 1467, "test_size": 489, "algorithm_type": "Random Forest"}, "ExtraTrees": {"accuracy": 0.8916155419222904, "precision": [0.9302325581395349, 0.8613138686131386], "recall": [0.8403361344537815, 0.9402390438247012], "f1_score": [0.8830022075055187, 0.8990476190476191], "support": [238, 251], "classification_report": {"0": {"precision": 0.9302325581395349, "recall": 0.8403361344537815, "f1-score": 0.8830022075055187, "support": 238.0}, "1": {"precision": 0.8613138686131386, "recall": 0.9402390438247012, "f1-score": 0.8990476190476191, "support": 251.0}, "accuracy": 0.8916155419222904, "macro avg": {"precision": 0.8957732133763368, "recall": 0.8902875891392414, "f1-score": 0.8910249132765689, "support": 489.0}, "weighted avg": {"precision": 0.8948571162762926, "recall": 0.8916155419222904, "f1-score": 0.8912381958430795, "support": 489.0}}, "confusion_matrix": [[200, 38], [15, 236]], "roc_auc": 0.9634236164585356, "training_time": 0.879046, "train_size": 1467, "test_size": 489, "algorithm_type": "Random Forest"}, "ExtraTrees_Large": {"accuracy": 0.8732106339468303, "precision": [0.8165467625899281, 0.9478672985781991], "recall": [0.9537815126050421, 0.796812749003984], "f1_score": [0.8798449612403101, 0.8658008658008658], "support": [238, 251], "classification_report": {"0": {"precision": 0.8165467625899281, "recall": 0.9537815126050421, "f1-score": 0.8798449612403101, "support": 238.0}, "1": {"precision": 0.9478672985781991, "recall": 0.796812749003984, "f1-score": 0.8658008658008658, "support": 251.0}, "accuracy": 0.8732106339468303, "macro avg": {"precision": 0.8822070305840636, "recall": 0.875297130804513, "f1-score": 0.8728229135205879, "support": 489.0}, "weighted avg": {"precision": 0.8839526000808402, "recall": 0.8732106339468303, "f1-score": 0.8726362333153601, "support": 489.0}}, "confusion_matrix": [[227, 11], [51, 200]], "roc_auc": 0.9458050152331848, "training_time": 0.340362, "train_size": 1467, "test_size": 489, "algorithm_type": "Random Forest"}, "GradientBoosting": {"accuracy": 0.901840490797546, "precision": [0.841726618705036, 0.981042654028436], "recall": [0.9831932773109243, 0.8247011952191236], "f1_score": [0.9069767441860465, 0.8961038961038961], "support": [238, 251], "classification_report": {"0": {"precision": 0.841726618705036, "recall": 0.9831932773109243, "f1-score": 0.9069767441860465, "support": 238.0}, "1": {"precision": 0.981042654028436, "recall": 0.8247011952191236, "f1-score": 0.8961038961038961, "support": 251.0}, "accuracy": 0.901840490797546, "macro avg": {"precision": 0.911384636366736, "recall": 0.9039472362650239, "f1-score": 0.9015403201449712, "support": 489.0}, "weighted avg": {"precision": 0.9132364855070266, "recall": 0.901840490797546, "f1-score": 0.9013957935344723, "support": 489.0}}, "confusion_matrix": [[234, 4], [44, 207]], "roc_auc": 0.9520154675415983, "training_time": 0.734363, "train_size": 1467, "test_size": 489, "algorithm_type": "Ensemble"}, "AdaBoost": {"accuracy": 0.901840490797546, "precision": [0.8492647058823529, 0.967741935483871], "recall": [0.9705882352941176, 0.8366533864541833], "f1_score": [0.9058823529411765, 0.8974358974358975], "support": [238, 251], "classification_report": {"0": {"precision": 0.8492647058823529, "recall": 0.9705882352941176, "f1-score": 0.9058823529411765, "support": 238.0}, "1": {"precision": 0.967741935483871, "recall": 0.8366533864541833, "f1-score": 0.8974358974358975, "support": 251.0}, "accuracy": 0.901840490797546, "macro avg": {"precision": 0.9085033206831119, "recall": 0.9036208108741505, "f1-score": 0.901659125188537, "support": 489.0}, "weighted avg": {"precision": 0.9100781713833366, "recall": 0.901840490797546, "f1-score": 0.9015468512401028, "support": 489.0}}, "confusion_matrix": [[231, 7], [41, 210]], "roc_auc": 0.9406993873246509, "training_time": 0.39252, "train_size": 1467, "test_size": 489, "algorithm_type": "Ensemble"}, "AdaBoost_DT": {"accuracy": 0.8609406952965235, "precision": [0.9166666666666666, 0.8210526315789474], "recall": [0.7857142857142857, 0.9322709163346613], "f1_score": [0.8461538461538461, 0.8731343283582089], "support": [238, 251], "classification_report": {"0": {"precision": 0.9166666666666666, "recall": 0.7857142857142857, "f1-score": 0.8461538461538461, "support": 238.0}, "1": {"precision": 0.8210526315789474, "recall": 0.9322709163346613, "f1-score": 0.8731343283582089, "support": 251.0}, "accuracy": 0.8609406952965235, "macro avg": {"precision": 0.868859649122807, "recall": 0.8589926010244735, "f1-score": 0.8596440872560276, "support": 489.0}, "weighted avg": {"precision": 0.8675887059161195, "recall": 0.8609406952965235, "f1-score": 0.8600027235225478, "support": 489.0}}, "confusion_matrix": [[187, 51], [17, 234]], "roc_auc": 0.9501322441327127, "training_time": 0.847828, "train_size": 1467, "test_size": 489, "algorithm_type": "Decision Tree"}}