{"SVM_Linear": {"accuracy": 0.8962264150943396, "precision": [0.8521739130434782, 0.9484536082474226], "recall": [0.9514563106796117, 0.8440366972477065], "f1_score": [0.8990825688073395, 0.8932038834951457], "support": [618, 654], "classification_report": {"0": {"precision": 0.8521739130434782, "recall": 0.9514563106796117, "f1-score": 0.8990825688073395, "support": 618.0}, "1": {"precision": 0.9484536082474226, "recall": 0.8440366972477065, "f1-score": 0.8932038834951457, "support": 654.0}, "accuracy": 0.8962264150943396, "macro avg": {"precision": 0.9003137606454504, "recall": 0.8977465039636591, "f1-score": 0.8961432261512425, "support": 1272.0}, "weighted avg": {"precision": 0.9016762091624874, "recall": 0.8962264150943396, "f1-score": 0.8960600372081455, "support": 1272.0}}, "confusion_matrix": [[588, 30], [102, 552]], "roc_auc": 0.9617638035291907, "training_time": 0.113852, "train_size": 684, "test_size": 1272, "algorithm_type": "Support Vector Machine"}, "SVM_Polynomial": {"accuracy": 0.8592767295597484, "precision": [0.8830715532286213, 0.8397711015736766], "recall": [0.8187702265372169, 0.8975535168195719], "f1_score": [0.8497061293031066, 0.8677014042867701], "support": [618, 654], "classification_report": {"0": {"precision": 0.8830715532286213, "recall": 0.8187702265372169, "f1-score": 0.8497061293031066, "support": 618.0}, "1": {"precision": 0.8397711015736766, "recall": 0.8975535168195719, "f1-score": 0.8677014042867701, "support": 654.0}, "accuracy": 0.8592767295597484, "macro avg": {"precision": 0.861421327401149, "recall": 0.8581618716783943, "f1-score": 0.8587037667949384, "support": 1272.0}, "weighted avg": {"precision": 0.8608085851607489, "recall": 0.8592767295597484, "f1-score": 0.8589584169126319, "support": 1272.0}}, "confusion_matrix": [[506, 112], [67, 587]], "roc_auc": 0.9358824955711924, "training_time": 0.137816, "train_size": 684, "test_size": 1272, "algorithm_type": "Support Vector Machine"}, "SVM_RBF": {"accuracy": 0.8954402515723271, "precision": [0.8783151326053042, 0.9128367670364501], "recall": [0.9110032362459547, 0.8807339449541285], "f1_score": [0.8943606036536934, 0.8964980544747082], "support": [618, 654], "classification_report": {"0": {"precision": 0.8783151326053042, "recall": 0.9110032362459547, "f1-score": 0.8943606036536934, "support": 618.0}, "1": {"precision": 0.9128367670364501, "recall": 0.8807339449541285, "f1-score": 0.8964980544747082, "support": 654.0}, "accuracy": 0.8954402515723271, "macro avg": {"precision": 0.8955759498208772, "recall": 0.8958685906000416, "f1-score": 0.8954293290642008, "support": 1272.0}, "weighted avg": {"precision": 0.8960644635156574, "recall": 0.8954402515723271, "f1-score": 0.8954595760097813, "support": 1272.0}}, "confusion_matrix": [[563, 55], [78, 576]], "roc_auc": 0.9396618271428996, "training_time": 0.146745, "train_size": 684, "test_size": 1272, "algorithm_type": "Support Vector Machine"}, "SVM_Sigmoid": {"accuracy": 0.8946540880503144, "precision": [0.8447293447293447, 0.956140350877193], "recall": [0.959546925566343, 0.8333333333333334], "f1_score": [0.8984848484848484, 0.8905228758169934], "support": [618, 654], "classification_report": {"0": {"precision": 0.8447293447293447, "recall": 0.959546925566343, "f1-score": 0.8984848484848484, "support": 618.0}, "1": {"precision": 0.956140350877193, "recall": 0.8333333333333334, "f1-score": 0.8905228758169934, "support": 654.0}, "accuracy": 0.8946540880503144, "macro avg": {"precision": 0.9004348478032689, "recall": 0.8964401294498382, "f1-score": 0.8945038621509209, "support": 1272.0}, "weighted avg": {"precision": 0.9020114186449836, "recall": 0.8946540880503144, "f1-score": 0.8943911927263757, "support": 1272.0}}, "confusion_matrix": [[593, 25], [109, 545]], "roc_auc": 0.9616871035103867, "training_time": 0.123403, "train_size": 684, "test_size": 1272, "algorithm_type": "Support Vector Machine"}, "SVM_RBF_Tuned": {"accuracy": 0.8970125786163522, "precision": [0.8822605965463108, 0.9118110236220472], "recall": [0.9093851132686084, 0.8853211009174312], "f1_score": [0.895617529880478, 0.8983708301008534], "support": [618, 654], "classification_report": {"0": {"precision": 0.8822605965463108, "recall": 0.9093851132686084, "f1-score": 0.895617529880478, "support": 618.0}, "1": {"precision": 0.9118110236220472, "recall": 0.8853211009174312, "f1-score": 0.8983708301008534, "support": 654.0}, "accuracy": 0.8970125786163522, "macro avg": {"precision": 0.8970358100841791, "recall": 0.8973531070930199, "f1-score": 0.8969941799906658, "support": 1272.0}, "weighted avg": {"precision": 0.8974539765050622, "recall": 0.8970125786163522, "f1-score": 0.897033141786237, "support": 1272.0}}, "confusion_matrix": [[562, 56], [75, 579]], "roc_auc": 0.9389344140613403, "training_time": 0.171334, "train_size": 684, "test_size": 1272, "algorithm_type": "Support Vector Machine"}, "SVM_Linear_Tuned": {"accuracy": 0.8962264150943396, "precision": [0.8521739130434782, 0.9484536082474226], "recall": [0.9514563106796117, 0.8440366972477065], "f1_score": [0.8990825688073395, 0.8932038834951457], "support": [618, 654], "classification_report": {"0": {"precision": 0.8521739130434782, "recall": 0.9514563106796117, "f1-score": 0.8990825688073395, "support": 618.0}, "1": {"precision": 0.9484536082474226, "recall": 0.8440366972477065, "f1-score": 0.8932038834951457, "support": 654.0}, "accuracy": 0.8962264150943396, "macro avg": {"precision": 0.9003137606454504, "recall": 0.8977465039636591, "f1-score": 0.8961432261512425, "support": 1272.0}, "weighted avg": {"precision": 0.9016762091624874, "recall": 0.8962264150943396, "f1-score": 0.8960600372081455, "support": 1272.0}}, "confusion_matrix": [[588, 30], [102, 552]], "roc_auc": 0.9617638035291907, "training_time": 0.124466, "train_size": 684, "test_size": 1272, "algorithm_type": "Support Vector Machine"}, "NB_Multinomial": {"accuracy": 0.8797169811320755, "precision": [0.91005291005291, 0.8553191489361702], "recall": [0.8349514563106796, 0.9220183486238532], "f1_score": [0.8708860759493671, 0.8874172185430463], "support": [618, 654], "classification_report": {"0": {"precision": 0.91005291005291, "recall": 0.8349514563106796, "f1-score": 0.8708860759493671, "support": 618.0}, "1": {"precision": 0.8553191489361702, "recall": 0.9220183486238532, "f1-score": 0.8874172185430463, "support": 654.0}, "accuracy": 0.8797169811320755, "macro avg": {"precision": 0.8826860294945401, "recall": 0.8784849024672664, "f1-score": 0.8791516472462066, "support": 1272.0}, "weighted avg": {"precision": 0.8819114951391146, "recall": 0.8797169811320755, "f1-score": 0.8793855785093248, "support": 1272.0}}, "confusion_matrix": [[516, 102], [51, 603]], "roc_auc": 0.9552393535425512, "training_time": 0.02435, "train_size": 684, "test_size": 1272, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli": {"accuracy": 0.8301886792452831, "precision": [0.7439320388349514, 0.9888392857142857], "recall": [0.9919093851132686, 0.6773700305810397], "f1_score": [0.8502080443828016, 0.8039927404718693], "support": [618, 654], "classification_report": {"0": {"precision": 0.7439320388349514, "recall": 0.9919093851132686, "f1-score": 0.8502080443828016, "support": 618.0}, "1": {"precision": 0.9888392857142857, "recall": 0.6773700305810397, "f1-score": 0.8039927404718693, "support": 654.0}, "accuracy": 0.8301886792452831, "macro avg": {"precision": 0.8663856622746186, "recall": 0.8346397078471541, "f1-score": 0.8271003924273355, "support": 1272.0}, "weighted avg": {"precision": 0.8698513308625337, "recall": 0.8301886792452831, "f1-score": 0.8264464022776525, "support": 1272.0}}, "confusion_matrix": [[613, 5], [211, 443]], "roc_auc": 0.9495239650445849, "training_time": 0.011567, "train_size": 684, "test_size": 1272, "algorithm_type": "<PERSON><PERSON>"}, "NB_Complement": {"accuracy": 0.8781446540880503, "precision": [0.8545176110260337, 0.9030694668820679], "recall": [0.9029126213592233, 0.8547400611620795], "f1_score": [0.8780487804878049, 0.8782403770620582], "support": [618, 654], "classification_report": {"0": {"precision": 0.8545176110260337, "recall": 0.9029126213592233, "f1-score": 0.8780487804878049, "support": 618.0}, "1": {"precision": 0.9030694668820679, "recall": 0.8547400611620795, "f1-score": 0.8782403770620582, "support": 654.0}, "accuracy": 0.8781446540880503, "macro avg": {"precision": 0.8787935389540509, "recall": 0.8788263412606514, "f1-score": 0.8781445787749316, "support": 1272.0}, "weighted avg": {"precision": 0.8794805935180513, "recall": 0.8781446540880503, "f1-score": 0.8781472900472086, "support": 1272.0}}, "confusion_matrix": [[558, 60], [95, 559]], "roc_auc": 0.9552393535425512, "training_time": 0.012167, "train_size": 684, "test_size": 1272, "algorithm_type": "<PERSON><PERSON>"}, "NB_Multinomial_Tuned": {"accuracy": 0.8773584905660378, "precision": [0.9010416666666666, 0.8577586206896551], "recall": [0.8398058252427184, 0.9128440366972477], "f1_score": [0.8693467336683417, 0.8844444444444445], "support": [618, 654], "classification_report": {"0": {"precision": 0.9010416666666666, "recall": 0.8398058252427184, "f1-score": 0.8693467336683417, "support": 618.0}, "1": {"precision": 0.8577586206896551, "recall": 0.9128440366972477, "f1-score": 0.8844444444444445, "support": 654.0}, "accuracy": 0.8773584905660378, "macro avg": {"precision": 0.8794001436781609, "recall": 0.876324930969983, "f1-score": 0.8768955890563931, "support": 1272.0}, "weighted avg": {"precision": 0.8787876477445239, "recall": 0.8773584905660378, "f1-score": 0.8771092359069984, "support": 1272.0}}, "confusion_matrix": [[519, 99], [57, 597]], "roc_auc": 0.9533564917906238, "training_time": 0.002376, "train_size": 684, "test_size": 1272, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli_Tuned": {"accuracy": 0.8529874213836478, "precision": [0.7816993464052288, 0.960552268244576], "recall": [0.9676375404530745, 0.7446483180428135], "f1_score": [0.8647866955892987, 0.8389319552110249], "support": [618, 654], "classification_report": {"0": {"precision": 0.7816993464052288, "recall": 0.9676375404530745, "f1-score": 0.8647866955892987, "support": 618.0}, "1": {"precision": 0.960552268244576, "recall": 0.7446483180428135, "f1-score": 0.8389319552110249, "support": 654.0}, "accuracy": 0.8529874213836478, "macro avg": {"precision": 0.8711258073249024, "recall": 0.856142929247944, "f1-score": 0.8518593254001618, "support": 1272.0}, "weighted avg": {"precision": 0.8736567448981007, "recall": 0.8529874213836478, "f1-score": 0.8514934564325448, "support": 1272.0}}, "confusion_matrix": [[598, 20], [167, 487]], "roc_auc": 0.9440559959621151, "training_time": 0.002589, "train_size": 684, "test_size": 1272, "algorithm_type": "<PERSON><PERSON>"}, "LR_L1": {"accuracy": 0.8498427672955975, "precision": [0.7726692209450831, 0.9734151329243353], "recall": [0.9789644012944984, 0.72782874617737], "f1_score": [0.8636688079942898, 0.8328958880139983], "support": [618, 654], "classification_report": {"0": {"precision": 0.7726692209450831, "recall": 0.9789644012944984, "f1-score": 0.8636688079942898, "support": 618.0}, "1": {"precision": 0.9734151329243353, "recall": 0.72782874617737, "f1-score": 0.8328958880139983, "support": 654.0}, "accuracy": 0.8498427672955975, "macro avg": {"precision": 0.8730421769347092, "recall": 0.8533965737359341, "f1-score": 0.848282348004144, "support": 1272.0}, "weighted avg": {"precision": 0.8758829209721514, "recall": 0.8498427672955975, "f1-score": 0.8478468821553663, "support": 1272.0}}, "confusion_matrix": [[605, 13], [178, 476]], "roc_auc": 0.9206575418386231, "training_time": 0.020663, "train_size": 684, "test_size": 1272, "algorithm_type": "Logistic Regression"}, "LR_L2": {"accuracy": 0.8922955974842768, "precision": [0.8531571218795888, 0.937394247038917], "recall": [0.9401294498381877, 0.8470948012232415], "f1_score": [0.8945342571208622, 0.8899598393574297], "support": [618, 654], "classification_report": {"0": {"precision": 0.8531571218795888, "recall": 0.9401294498381877, "f1-score": 0.8945342571208622, "support": 618.0}, "1": {"precision": 0.937394247038917, "recall": 0.8470948012232415, "f1-score": 0.8899598393574297, "support": 654.0}, "accuracy": 0.8922955974842768, "macro avg": {"precision": 0.8952756844592529, "recall": 0.8936121255307146, "f1-score": 0.892247048239146, "support": 1272.0}, "weighted avg": {"precision": 0.8964677192492434, "recall": 0.8922955974842768, "f1-score": 0.8921823159123049, "support": 1272.0}}, "confusion_matrix": [[581, 37], [100, 554]], "roc_auc": 0.9534678305275972, "training_time": 0.0, "train_size": 684, "test_size": 1272, "algorithm_type": "Logistic Regression"}, "LR_ElasticNet": {"accuracy": 0.8765723270440252, "precision": [0.8196948682385575, 0.9509981851179673], "recall": [0.9563106796116505, 0.8012232415902141], "f1_score": [0.8827483196415236, 0.8697095435684647], "support": [618, 654], "classification_report": {"0": {"precision": 0.8196948682385575, "recall": 0.9563106796116505, "f1-score": 0.8827483196415236, "support": 618.0}, "1": {"precision": 0.9509981851179673, "recall": 0.8012232415902141, "f1-score": 0.8697095435684647, "support": 654.0}, "accuracy": 0.8765723270440252, "macro avg": {"precision": 0.8853465266782624, "recall": 0.8787669606009323, "f1-score": 0.8762289316049942, "support": 1272.0}, "weighted avg": {"precision": 0.8872045924831596, "recall": 0.8765723270440252, "f1-score": 0.8760444206228282, "support": 1272.0}}, "confusion_matrix": [[591, 27], [130, 524]], "roc_auc": 0.9397979078214225, "training_time": 0.058822, "train_size": 684, "test_size": 1272, "algorithm_type": "Logistic Regression"}, "LR_LBFGS": {"accuracy": 0.8930817610062893, "precision": [0.8533724340175953, 0.9389830508474576], "recall": [0.941747572815534, 0.8470948012232415], "f1_score": [0.8953846153846153, 0.8906752411575563], "support": [618, 654], "classification_report": {"0": {"precision": 0.8533724340175953, "recall": 0.941747572815534, "f1-score": 0.8953846153846153, "support": 618.0}, "1": {"precision": 0.9389830508474576, "recall": 0.8470948012232415, "f1-score": 0.8906752411575563, "support": 654.0}, "accuracy": 0.8930817610062893, "macro avg": {"precision": 0.8961777424325265, "recall": 0.8944211870193878, "f1-score": 0.8930299282710858, "support": 1272.0}, "weighted avg": {"precision": 0.8973892134254018, "recall": 0.8930817610062893, "f1-score": 0.8929632861829669, "support": 1272.0}}, "confusion_matrix": [[582, 36], [100, 554]], "roc_auc": 0.9535148402165414, "training_time": 0.022874, "train_size": 684, "test_size": 1272, "algorithm_type": "Logistic Regression"}, "LR_SAG": {"accuracy": 0.8922955974842768, "precision": [0.8521229868228404, 0.9388794567062818], "recall": [0.941747572815534, 0.845565749235474], "f1_score": [0.8946963873943121, 0.8897827835880934], "support": [618, 654], "classification_report": {"0": {"precision": 0.8521229868228404, "recall": 0.941747572815534, "f1-score": 0.8946963873943121, "support": 618.0}, "1": {"precision": 0.9388794567062818, "recall": 0.845565749235474, "f1-score": 0.8897827835880934, "support": 654.0}, "accuracy": 0.8922955974842768, "macro avg": {"precision": 0.895501221764561, "recall": 0.893656661025504, "f1-score": 0.8922395854912027, "support": 1272.0}, "weighted avg": {"precision": 0.8967289076591382, "recall": 0.8922955974842768, "f1-score": 0.8921700533618696, "support": 1272.0}}, "confusion_matrix": [[582, 36], [101, 553]], "roc_auc": 0.9534925724691468, "training_time": 0.017925, "train_size": 684, "test_size": 1272, "algorithm_type": "Logistic Regression"}, "LR_SAGA": {"accuracy": 0.8922955974842768, "precision": [0.8531571218795888, 0.937394247038917], "recall": [0.9401294498381877, 0.8470948012232415], "f1_score": [0.8945342571208622, 0.8899598393574297], "support": [618, 654], "classification_report": {"0": {"precision": 0.8531571218795888, "recall": 0.9401294498381877, "f1-score": 0.8945342571208622, "support": 618.0}, "1": {"precision": 0.937394247038917, "recall": 0.8470948012232415, "f1-score": 0.8899598393574297, "support": 654.0}, "accuracy": 0.8922955974842768, "macro avg": {"precision": 0.8952756844592529, "recall": 0.8936121255307146, "f1-score": 0.892247048239146, "support": 1272.0}, "weighted avg": {"precision": 0.8964677192492434, "recall": 0.8922955974842768, "f1-score": 0.8921823159123049, "support": 1272.0}}, "confusion_matrix": [[581, 37], [100, 554]], "roc_auc": 0.953547004740556, "training_time": 0.001811, "train_size": 684, "test_size": 1272, "algorithm_type": "Logistic Regression"}, "DT_Gini": {"accuracy": 0.8459119496855346, "precision": [0.8892988929889298, 0.8136986301369863], "recall": [0.7799352750809061, 0.908256880733945], "f1_score": [0.8310344827586207, 0.8583815028901735], "support": [618, 654], "classification_report": {"0": {"precision": 0.8892988929889298, "recall": 0.7799352750809061, "f1-score": 0.8310344827586207, "support": 618.0}, "1": {"precision": 0.8136986301369863, "recall": 0.908256880733945, "f1-score": 0.8583815028901735, "support": 654.0}, "accuracy": 0.8459119496855346, "macro avg": {"precision": 0.8514987615629581, "recall": 0.8440960779074256, "f1-score": 0.844707992824397, "support": 1272.0}, "weighted avg": {"precision": 0.8504289465226003, "recall": 0.8459119496855346, "f1-score": 0.8450949789583342, "support": 1272.0}}, "confusion_matrix": [[482, 136], [60, 594]], "roc_auc": 0.9242624427224053, "training_time": 0.032715, "train_size": 684, "test_size": 1272, "algorithm_type": "Decision Tree"}, "DT_Entropy": {"accuracy": 0.8325471698113207, "precision": [0.867513611615245, 0.8058252427184466], "recall": [0.7734627831715211, 0.8883792048929664], "f1_score": [0.8177929854576561, 0.8450909090909091], "support": [618, 654], "classification_report": {"0": {"precision": 0.867513611615245, "recall": 0.7734627831715211, "f1-score": 0.8177929854576561, "support": 618.0}, "1": {"precision": 0.8058252427184466, "recall": 0.8883792048929664, "f1-score": 0.8450909090909091, "support": 654.0}, "accuracy": 0.8325471698113207, "macro avg": {"precision": 0.8366694271668458, "recall": 0.8309209940322437, "f1-score": 0.8314419472742827, "support": 1272.0}, "weighted avg": {"precision": 0.8357964785503817, "recall": 0.8325471698113207, "f1-score": 0.8318282386464513, "support": 1272.0}}, "confusion_matrix": [[478, 140], [73, 581]], "roc_auc": 0.9078894629019328, "training_time": 0.033966, "train_size": 684, "test_size": 1272, "algorithm_type": "Decision Tree"}, "DT_Log_Loss": {"accuracy": 0.8325471698113207, "precision": [0.867513611615245, 0.8058252427184466], "recall": [0.7734627831715211, 0.8883792048929664], "f1_score": [0.8177929854576561, 0.8450909090909091], "support": [618, 654], "classification_report": {"0": {"precision": 0.867513611615245, "recall": 0.7734627831715211, "f1-score": 0.8177929854576561, "support": 618.0}, "1": {"precision": 0.8058252427184466, "recall": 0.8883792048929664, "f1-score": 0.8450909090909091, "support": 654.0}, "accuracy": 0.8325471698113207, "macro avg": {"precision": 0.8366694271668458, "recall": 0.8309209940322437, "f1-score": 0.8314419472742827, "support": 1272.0}, "weighted avg": {"precision": 0.8357964785503817, "recall": 0.8325471698113207, "f1-score": 0.8318282386464513, "support": 1272.0}}, "confusion_matrix": [[478, 140], [73, 581]], "roc_auc": 0.9078894629019328, "training_time": 0.02322, "train_size": 684, "test_size": 1272, "algorithm_type": "Decision Tree"}, "DT_Gini_Pruned": {"accuracy": 0.8537735849056604, "precision": [0.7755102040816326, 0.9795081967213115], "recall": [0.9838187702265372, 0.7308868501529052], "f1_score": [0.8673323823109843, 0.8371278458844134], "support": [618, 654], "classification_report": {"0": {"precision": 0.7755102040816326, "recall": 0.9838187702265372, "f1-score": 0.8673323823109843, "support": 618.0}, "1": {"precision": 0.9795081967213115, "recall": 0.7308868501529052, "f1-score": 0.8371278458844134, "support": 654.0}, "accuracy": 0.8537735849056604, "macro avg": {"precision": 0.8775092004014721, "recall": 0.8573528101897212, "f1-score": 0.8522301140976989, "support": 1272.0}, "weighted avg": {"precision": 0.8803959644482598, "recall": 0.8537735849056604, "f1-score": 0.8518026914124173, "support": 1272.0}}, "confusion_matrix": [[608, 10], [176, 478]], "roc_auc": 0.8734251754203656, "training_time": 0.018067, "train_size": 684, "test_size": 1272, "algorithm_type": "Decision Tree"}, "DT_Entropy_Pruned": {"accuracy": 0.845125786163522, "precision": [0.762796504369538, 0.9851380042462845], "recall": [0.988673139158576, 0.709480122324159], "f1_score": [0.8611698379140239, 0.8248888888888889], "support": [618, 654], "classification_report": {"0": {"precision": 0.762796504369538, "recall": 0.988673139158576, "f1-score": 0.8611698379140239, "support": 618.0}, "1": {"precision": 0.9851380042462845, "recall": 0.709480122324159, "f1-score": 0.8248888888888889, "support": 654.0}, "accuracy": 0.845125786163522, "macro avg": {"precision": 0.8739672543079113, "recall": 0.8490766307413675, "f1-score": 0.8430293634014564, "support": 1272.0}, "weighted avg": {"precision": 0.8771135962872991, "recall": 0.845125786163522, "f1-score": 0.8425159537454403, "support": 1272.0}}, "confusion_matrix": [[611, 7], [190, 464]], "roc_auc": 0.874453203091753, "training_time": 0.023296, "train_size": 684, "test_size": 1272, "algorithm_type": "Decision Tree"}, "DT_Best_First": {"accuracy": 0.8459119496855346, "precision": [0.8892988929889298, 0.8136986301369863], "recall": [0.7799352750809061, 0.908256880733945], "f1_score": [0.8310344827586207, 0.8583815028901735], "support": [618, 654], "classification_report": {"0": {"precision": 0.8892988929889298, "recall": 0.7799352750809061, "f1-score": 0.8310344827586207, "support": 618.0}, "1": {"precision": 0.8136986301369863, "recall": 0.908256880733945, "f1-score": 0.8583815028901735, "support": 654.0}, "accuracy": 0.8459119496855346, "macro avg": {"precision": 0.8514987615629581, "recall": 0.8440960779074256, "f1-score": 0.844707992824397, "support": 1272.0}, "weighted avg": {"precision": 0.8504289465226003, "recall": 0.8459119496855346, "f1-score": 0.8450949789583342, "support": 1272.0}}, "confusion_matrix": [[482, 136], [60, 594]], "roc_auc": 0.9242624427224053, "training_time": 0.022969, "train_size": 684, "test_size": 1272, "algorithm_type": "Decision Tree"}, "DT_Random_Split": {"accuracy": 0.8482704402515723, "precision": [0.9001883239171374, 0.8110661268556005], "recall": [0.7734627831715211, 0.918960244648318], "f1_score": [0.8320278503046127, 0.8616487455197133], "support": [618, 654], "classification_report": {"0": {"precision": 0.9001883239171374, "recall": 0.7734627831715211, "f1-score": 0.8320278503046127, "support": 618.0}, "1": {"precision": 0.8110661268556005, "recall": 0.918960244648318, "f1-score": 0.8616487455197133, "support": 654.0}, "accuracy": 0.8482704402515723, "macro avg": {"precision": 0.855627225386369, "recall": 0.8462115139099196, "f1-score": 0.8468382979121629, "support": 1272.0}, "weighted avg": {"precision": 0.8543660622204038, "recall": 0.8482704402515723, "f1-score": 0.8472574615236974, "support": 1272.0}}, "confusion_matrix": [[478, 140], [53, 601]], "roc_auc": 0.9242104846451511, "training_time": 0.033225, "train_size": 684, "test_size": 1272, "algorithm_type": "Decision Tree"}, "RF_Gini": {"accuracy": 0.85062893081761, "precision": [0.9147286821705426, 0.8068783068783069], "recall": [0.7637540453074434, 0.9327217125382263], "f1_score": [0.8324514991181657, 0.8652482269503546], "support": [618, 654], "classification_report": {"0": {"precision": 0.9147286821705426, "recall": 0.7637540453074434, "f1-score": 0.8324514991181657, "support": 618.0}, "1": {"precision": 0.8068783068783069, "recall": 0.9327217125382263, "f1-score": 0.8652482269503546, "support": 654.0}, "accuracy": 0.85062893081761, "macro avg": {"precision": 0.8608034945244247, "recall": 0.8482378789228349, "f1-score": 0.8488498630342602, "support": 1272.0}, "weighted avg": {"precision": 0.8592773099684026, "recall": 0.85062893081761, "f1-score": 0.8493139676733948, "support": 1272.0}}, "confusion_matrix": [[472, 146], [44, 610]], "roc_auc": 0.9272042595726573, "training_time": 0.295358, "train_size": 684, "test_size": 1272, "algorithm_type": "Random Forest"}, "RF_Entropy": {"accuracy": 0.8427672955974843, "precision": [0.908203125, 0.7986842105263158], "recall": [0.7524271844660194, 0.9281345565749235], "f1_score": [0.8230088495575221, 0.8585572842998586], "support": [618, 654], "classification_report": {"0": {"precision": 0.908203125, "recall": 0.7524271844660194, "f1-score": 0.8230088495575221, "support": 618.0}, "1": {"precision": 0.7986842105263158, "recall": 0.9281345565749235, "f1-score": 0.8585572842998586, "support": 654.0}, "accuracy": 0.8427672955974843, "macro avg": {"precision": 0.8534436677631578, "recall": 0.8402808705204714, "f1-score": 0.8407830669286903, "support": 1272.0}, "weighted avg": {"precision": 0.8518938718036246, "recall": 0.8427672955974843, "f1-score": 0.8412861108165537, "support": 1272.0}}, "confusion_matrix": [[465, 153], [47, 607]], "roc_auc": 0.9250814009876982, "training_time": 0.321762, "train_size": 684, "test_size": 1272, "algorithm_type": "Random Forest"}, "RF_Log_Loss": {"accuracy": 0.8427672955974843, "precision": [0.908203125, 0.7986842105263158], "recall": [0.7524271844660194, 0.9281345565749235], "f1_score": [0.8230088495575221, 0.8585572842998586], "support": [618, 654], "classification_report": {"0": {"precision": 0.908203125, "recall": 0.7524271844660194, "f1-score": 0.8230088495575221, "support": 618.0}, "1": {"precision": 0.7986842105263158, "recall": 0.9281345565749235, "f1-score": 0.8585572842998586, "support": 654.0}, "accuracy": 0.8427672955974843, "macro avg": {"precision": 0.8534436677631578, "recall": 0.8402808705204714, "f1-score": 0.8407830669286903, "support": 1272.0}, "weighted avg": {"precision": 0.8518938718036246, "recall": 0.8427672955974843, "f1-score": 0.8412861108165537, "support": 1272.0}}, "confusion_matrix": [[465, 153], [47, 607]], "roc_auc": 0.9250814009876982, "training_time": 0.28244, "train_size": 684, "test_size": 1272, "algorithm_type": "Random Forest"}, "RF_Large": {"accuracy": 0.875, "precision": [0.8113975576662144, 0.9626168224299065], "recall": [0.9676375404530745, 0.7874617737003058], "f1_score": [0.8826568265682657, 0.8662741799831791], "support": [618, 654], "classification_report": {"0": {"precision": 0.8113975576662144, "recall": 0.9676375404530745, "f1-score": 0.8826568265682657, "support": 618.0}, "1": {"precision": 0.9626168224299065, "recall": 0.7874617737003058, "f1-score": 0.8662741799831791, "support": 654.0}, "accuracy": 0.875, "macro avg": {"precision": 0.8870071900480605, "recall": 0.8775496570766901, "f1-score": 0.8744655032757225, "support": 1272.0}, "weighted avg": {"precision": 0.8891470853041503, "recall": 0.875, "f1-score": 0.8742336733712164, "support": 1272.0}}, "confusion_matrix": [[598, 20], [139, 515]], "roc_auc": 0.9499606603129361, "training_time": 0.290423, "train_size": 684, "test_size": 1272, "algorithm_type": "Random Forest"}, "RF_Small": {"accuracy": 0.8742138364779874, "precision": [0.8061497326203209, 0.9713740458015268], "recall": [0.9757281553398058, 0.7782874617737003], "f1_score": [0.8828696925329429, 0.8641765704584041], "support": [618, 654], "classification_report": {"0": {"precision": 0.8061497326203209, "recall": 0.9757281553398058, "f1-score": 0.8828696925329429, "support": 618.0}, "1": {"precision": 0.9713740458015268, "recall": 0.7782874617737003, "f1-score": 0.8641765704584041, "support": 654.0}, "accuracy": 0.8742138364779874, "macro avg": {"precision": 0.8887618892109238, "recall": 0.8770078085567531, "f1-score": 0.8735231314956735, "support": 1272.0}, "weighted avg": {"precision": 0.8910999691144313, "recall": 0.8742138364779874, "f1-score": 0.8732586061832979, "support": 1272.0}}, "confusion_matrix": [[603, 15], [145, 509]], "roc_auc": 0.9452275268944904, "training_time": 0.079271, "train_size": 684, "test_size": 1272, "algorithm_type": "Random Forest"}, "ExtraTrees": {"accuracy": 0.8694968553459119, "precision": [0.9124087591240876, 0.8370165745856354], "recall": [0.8090614886731392, 0.926605504587156], "f1_score": [0.8576329331046312, 0.8795355587808418], "support": [618, 654], "classification_report": {"0": {"precision": 0.9124087591240876, "recall": 0.8090614886731392, "f1-score": 0.8576329331046312, "support": 618.0}, "1": {"precision": 0.8370165745856354, "recall": 0.926605504587156, "f1-score": 0.8795355587808418, "support": 654.0}, "accuracy": 0.8694968553459119, "macro avg": {"precision": 0.8747126668548615, "recall": 0.8678334966301475, "f1-score": 0.8685842459427364, "support": 1272.0}, "weighted avg": {"precision": 0.8736457963189401, "recall": 0.8694968553459119, "f1-score": 0.8688941887589094, "support": 1272.0}}, "confusion_matrix": [[500, 118], [48, 606]], "roc_auc": 0.9334639707847153, "training_time": 0.346005, "train_size": 684, "test_size": 1272, "algorithm_type": "Random Forest"}, "ExtraTrees_Large": {"accuracy": 0.8765723270440252, "precision": [0.8188105117565698, 0.9526411657559198], "recall": [0.9579288025889967, 0.7996941896024465], "f1_score": [0.8829231916480239, 0.8694929343308395], "support": [618, 654], "classification_report": {"0": {"precision": 0.8188105117565698, "recall": 0.9579288025889967, "f1-score": 0.8829231916480239, "support": 618.0}, "1": {"precision": 0.9526411657559198, "recall": 0.7996941896024465, "f1-score": 0.8694929343308395, "support": 654.0}, "accuracy": 0.8765723270440252, "macro avg": {"precision": 0.8857258387562448, "recall": 0.8788114960957216, "f1-score": 0.8762080629894318, "support": 1272.0}, "weighted avg": {"precision": 0.8876196687656697, "recall": 0.8765723270440252, "f1-score": 0.8760180121783395, "support": 1272.0}}, "confusion_matrix": [[592, 26], [131, 523]], "roc_auc": 0.9459858674029867, "training_time": 0.249743, "train_size": 684, "test_size": 1272, "algorithm_type": "Random Forest"}, "GradientBoosting": {"accuracy": 0.8828616352201258, "precision": [0.8105960264900662, 0.988394584139265], "recall": [0.9902912621359223, 0.7813455657492355], "f1_score": [0.8914785142024764, 0.8727583262169086], "support": [618, 654], "classification_report": {"0": {"precision": 0.8105960264900662, "recall": 0.9902912621359223, "f1-score": 0.8914785142024764, "support": 618.0}, "1": {"precision": 0.988394584139265, "recall": 0.7813455657492355, "f1-score": 0.8727583262169086, "support": 654.0}, "accuracy": 0.8828616352201258, "macro avg": {"precision": 0.8994953053146656, "recall": 0.8858184139425789, "f1-score": 0.8821184202096926, "support": 1272.0}, "weighted avg": {"precision": 0.90201132263989, "recall": 0.8828616352201258, "f1-score": 0.881853511889142, "support": 1272.0}}, "confusion_matrix": [[612, 6], [143, 511]], "roc_auc": 0.9350944647328366, "training_time": 0.3896, "train_size": 684, "test_size": 1272, "algorithm_type": "Ensemble"}, "AdaBoost": {"accuracy": 0.8781446540880503, "precision": [0.8141112618724559, 0.9663551401869159], "recall": [0.970873786407767, 0.790519877675841], "f1_score": [0.8856088560885609, 0.8696383515559294], "support": [618, 654], "classification_report": {"0": {"precision": 0.8141112618724559, "recall": 0.970873786407767, "f1-score": 0.8856088560885609, "support": 618.0}, "1": {"precision": 0.9663551401869159, "recall": 0.790519877675841, "f1-score": 0.8696383515559294, "support": 654.0}, "accuracy": 0.8781446540880503, "macro avg": {"precision": 0.8902332010296858, "recall": 0.880696832041804, "f1-score": 0.8776236038222451, "support": 1272.0}, "weighted avg": {"precision": 0.8923875955341357, "recall": 0.8781446540880503, "f1-score": 0.8773976061165948, "support": 1272.0}}, "confusion_matrix": [[600, 18], [137, 517]], "roc_auc": 0.9218451550330057, "training_time": 0.260594, "train_size": 684, "test_size": 1272, "algorithm_type": "Ensemble"}, "AdaBoost_DT": {"accuracy": 0.8608490566037735, "precision": [0.8795180722891566, 0.8451519536903039], "recall": [0.8268608414239482, 0.8929663608562691], "f1_score": [0.8523769808173478, 0.8684014869888476], "support": [618, 654], "classification_report": {"0": {"precision": 0.8795180722891566, "recall": 0.8268608414239482, "f1-score": 0.8523769808173478, "support": 618.0}, "1": {"precision": 0.8451519536903039, "recall": 0.8929663608562691, "f1-score": 0.8684014869888476, "support": 654.0}, "accuracy": 0.8608490566037735, "macro avg": {"precision": 0.8623350129897303, "recall": 0.8599136011401087, "f1-score": 0.8603892339030976, "support": 1272.0}, "weighted avg": {"precision": 0.86184869999069, "recall": 0.8608490566037735, "f1-score": 0.8606159957828831, "support": 1272.0}}, "confusion_matrix": [[511, 107], [70, 584]], "roc_auc": 0.9512311590115101, "training_time": 0.48064, "train_size": 684, "test_size": 1272, "algorithm_type": "Decision Tree"}}