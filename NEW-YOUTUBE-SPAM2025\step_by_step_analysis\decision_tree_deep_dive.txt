DECISION TREE - DETAILED ANALYSIS
============================================================
Analysis Date: 2025-06-30 00:39:29

OVERVIEW:
--------------------
Total Variants Tested: 8
Total Models Trained: 48
Best Accuracy: 0.8875 (88.75%)
Average Accuracy: 0.8524 (85.24%)
Worst Accuracy: 0.8160 (81.60%)

BEST PERFORMER:
--------------------
Algorithm: DT_Gini_Pruned
Split Type: Original
Split Name: train_75_test_25
Accuracy: 0.8875 (88.75%)
Spam F1-Score: 0.8786
Training Time: 0.05 seconds

VARIANT COMPARISON:
--------------------
DT_Gini:
  Average Accuracy: 0.8506
  Best Accuracy: 0.8753
  Consistency (Std): 0.0216
  Average Training Time: 0.05s
  Average Spam F1: 0.8646

DT_Entropy:
  Average Accuracy: 0.8415
  Best Accuracy: 0.8613
  Consistency (Std): 0.0185
  Average Training Time: 0.05s
  Average Spam F1: 0.8550

DT_Log_Loss:
  Average Accuracy: 0.8415
  Best Accuracy: 0.8613
  Consistency (Std): 0.0185
  Average Training Time: 0.05s
  Average Spam F1: 0.8550

DT_Gini_Pruned:
  Average Accuracy: 0.8733
  Best Accuracy: 0.8875
  Consistency (Std): 0.0140
  Average Training Time: 0.03s
  Average Spam F1: 0.8614

DT_Entropy_Pruned:
  Average Accuracy: 0.8527
  Best Accuracy: 0.8671
  Consistency (Std): 0.0162
  Average Training Time: 0.03s
  Average Spam F1: 0.8351

DT_Best_First:
  Average Accuracy: 0.8506
  Best Accuracy: 0.8753
  Consistency (Std): 0.0216
  Average Training Time: 0.05s
  Average Spam F1: 0.8646

DT_Random_Split:
  Average Accuracy: 0.8502
  Best Accuracy: 0.8753
  Consistency (Std): 0.0262
  Average Training Time: 0.05s
  Average Spam F1: 0.8644

AdaBoost_DT:
  Average Accuracy: 0.8591
  Best Accuracy: 0.8672
  Consistency (Std): 0.0080
  Average Training Time: 0.65s
  Average Spam F1: 0.8694

PERFORMANCE BY SPLIT TYPE:
------------------------------
Original Splits:
  Models: 24
  Average Accuracy: 0.8674
  Best Accuracy: 0.8875
  Average Training Time: 0.16s

Reversed Splits:
  Models: 24
  Average Accuracy: 0.8375
  Best Accuracy: 0.8759
  Average Training Time: 0.08s

RECOMMENDATIONS:
--------------------
• Entropy criterion (C4.5) often outperforms Gini
• Pruning helps prevent overfitting
• Consider max_depth and min_samples_split tuning
• Good for interpretable models

============================================================