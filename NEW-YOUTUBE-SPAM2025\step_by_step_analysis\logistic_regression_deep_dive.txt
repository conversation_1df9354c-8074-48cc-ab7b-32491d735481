LOGISTIC REGRESSION - DETAILED ANALYSIS
============================================================
Analysis Date: 2025-06-30 00:39:29

OVERVIEW:
--------------------
Total Variants Tested: 6
Total Models Trained: 36
Best Accuracy: 0.9059 (90.59%)
Average Accuracy: 0.8860 (88.60%)
Worst Accuracy: 0.8425 (84.25%)

BEST PERFORMER:
--------------------
Algorithm: LR_L2
Split Type: Original
Split Name: train_75_test_25
Accuracy: 0.9059 (90.59%)
Spam F1-Score: 0.9042
Training Time: 0.04 seconds

VARIANT COMPARISON:
--------------------
LR_L1:
  Average Accuracy: 0.8623
  Best Accuracy: 0.8774
  Consistency (Std): 0.0166
  Average Training Time: 0.02s
  Average Spam F1: 0.8499

LR_L2:
  Average Accuracy: 0.8939
  Best Accuracy: 0.9059
  Consistency (Std): 0.0111
  Average Training Time: 0.01s
  Average Spam F1: 0.8915

LR_ElasticNet:
  Average Accuracy: 0.8781
  Best Accuracy: 0.8855
  Consistency (Std): 0.0068
  Average Training Time: 0.04s
  Average Spam F1: 0.8719

LR_LBFGS:
  Average Accuracy: 0.8937
  Best Accuracy: 0.9059
  Consistency (Std): 0.0108
  Average Training Time: 0.03s
  Average Spam F1: 0.8913

LR_SAG:
  Average Accuracy: 0.8939
  Best Accuracy: 0.9059
  Consistency (Std): 0.0112
  Average Training Time: 0.02s
  Average Spam F1: 0.8914

LR_SAGA:
  Average Accuracy: 0.8940
  Best Accuracy: 0.9059
  Consistency (Std): 0.0110
  Average Training Time: 0.02s
  Average Spam F1: 0.8915

PERFORMANCE BY SPLIT TYPE:
------------------------------
Original Splits:
  Models: 18
  Average Accuracy: 0.8953
  Best Accuracy: 0.9059
  Average Training Time: 0.03s

Reversed Splits:
  Models: 18
  Average Accuracy: 0.8767
  Best Accuracy: 0.8931
  Average Training Time: 0.01s

RECOMMENDATIONS:
--------------------
• L2 regularization generally performs well
• LBFGS solver recommended for small datasets
• ElasticNet for feature selection scenarios
• Fast training and good interpretability

============================================================