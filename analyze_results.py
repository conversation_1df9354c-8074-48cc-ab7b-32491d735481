#!/usr/bin/env python3
"""
Results Analysis Script
======================

Analyzes the comprehensive model testing results and provides detailed insights.
"""

import pandas as pd
import numpy as np

def analyze_results():
    """Analyze the comprehensive testing results"""
    
    # Load the detailed results
    df = pd.read_csv('test_results_20250630_062403/detailed_predictions_20250630_062403.csv')
    
    print("🎯 COMPREHENSIVE SPAM DETECTION ANALYSIS")
    print("=" * 60)
    
    # Basic statistics
    total_comments = len(df)
    spam_count = df['Ensemble_Prediction'].sum()
    spam_pct = df['Ensemble_Prediction'].mean() * 100
    ham_count = (df['Ensemble_Prediction'] == 0).sum()
    ham_pct = (df['Ensemble_Prediction'] == 0).mean() * 100
    avg_confidence = df['Ensemble_Confidence'].mean()
    
    print(f"\n📊 OVERALL RESULTS:")
    print(f"   Total Comments Analyzed: {total_comments}")
    print(f"   Spam Comments: {spam_count} ({spam_pct:.1f}%)")
    print(f"   Ham Comments: {ham_count} ({ham_pct:.1f}%)")
    print(f"   Average Confidence: {avg_confidence:.3f}")
    
    # Confidence analysis
    high_conf = df[df['Ensemble_Confidence'] > 0.8]
    medium_conf = df[(df['Ensemble_Confidence'] >= 0.6) & (df['Ensemble_Confidence'] <= 0.8)]
    low_conf = df[df['Ensemble_Confidence'] < 0.6]
    
    print(f"\n🎯 CONFIDENCE DISTRIBUTION:")
    print(f"   High Confidence (>0.8): {len(high_conf)} ({len(high_conf)/total_comments*100:.1f}%)")
    print(f"   Medium Confidence (0.6-0.8): {len(medium_conf)} ({len(medium_conf)/total_comments*100:.1f}%)")
    print(f"   Low Confidence (<0.6): {len(low_conf)} ({len(low_conf)/total_comments*100:.1f}%)")
    
    # Examples of spam comments
    print(f"\n🚨 EXAMPLES OF COMMENTS CLASSIFIED AS SPAM:")
    spam_comments = df[df['Ensemble_Prediction'] == 1].head(10)
    
    for i, (idx, row) in enumerate(spam_comments.iterrows()):
        author = row['Author']
        comment = row['Comment']
        confidence = row['Ensemble_Confidence']
        
        # Truncate long comments
        display_comment = comment[:100] + "..." if len(comment) > 100 else comment
        
        print(f"   {i+1}. [{author}] {display_comment}")
        print(f"      Confidence: {confidence:.3f}")
        print()
    
    # Examples of ham comments
    print(f"\n✅ EXAMPLES OF COMMENTS CLASSIFIED AS HAM:")
    ham_comments = df[df['Ensemble_Prediction'] == 0].head(10)
    
    for i, (idx, row) in enumerate(ham_comments.iterrows()):
        author = row['Author']
        comment = row['Comment']
        confidence = row['Ensemble_Confidence']
        
        # Truncate long comments
        display_comment = comment[:100] + "..." if len(comment) > 100 else comment
        
        print(f"   {i+1}. [{author}] {display_comment}")
        print(f"      Confidence: {confidence:.3f}")
        print()
    
    # Uncertain predictions
    print(f"\n❓ UNCERTAIN PREDICTIONS (Low Confidence):")
    if len(low_conf) > 0:
        for i, (idx, row) in enumerate(low_conf.head(5).iterrows()):
            label = "SPAM" if row['Ensemble_Prediction'] == 1 else "HAM"
            author = row['Author']
            comment = row['Comment']
            confidence = row['Ensemble_Confidence']
            
            display_comment = comment[:80] + "..." if len(comment) > 80 else comment
            
            print(f"   {i+1}. [{label}] [{author}] {display_comment}")
            print(f"      Confidence: {confidence:.3f}")
            print()
    else:
        print("   No low confidence predictions found!")
    
    # Top liked comments analysis
    print(f"\n👍 ANALYSIS BY LIKES:")
    df['Likes'] = pd.to_numeric(df['Likes'], errors='coerce').fillna(0)
    
    # Top liked spam comments
    spam_with_likes = df[df['Ensemble_Prediction'] == 1].sort_values('Likes', ascending=False)
    if len(spam_with_likes) > 0:
        print(f"   Top liked SPAM comments:")
        for i, (idx, row) in enumerate(spam_with_likes.head(3).iterrows()):
            likes = int(row['Likes']) if not pd.isna(row['Likes']) else 0
            comment = row['Comment'][:80] + "..." if len(row['Comment']) > 80 else row['Comment']
            print(f"      {i+1}. {likes} likes: {comment}")
    
    # Top liked ham comments
    ham_with_likes = df[df['Ensemble_Prediction'] == 0].sort_values('Likes', ascending=False)
    print(f"   Top liked HAM comments:")
    for i, (idx, row) in enumerate(ham_with_likes.head(3).iterrows()):
        likes = int(row['Likes']) if not pd.isna(row['Likes']) else 0
        comment = row['Comment'][:80] + "..." if len(row['Comment']) > 80 else row['Comment']
        print(f"      {i+1}. {likes} likes: {comment}")
    
    # Algorithm performance summary
    print(f"\n🤖 ALGORITHM PERFORMANCE SUMMARY:")
    try:
        algo_summary = pd.read_excel('test_results_20250630_062403/model_testing_summary_20250630_062403.xlsx', 
                                   sheet_name='Algorithm_Summary')
        
        print(f"   Algorithm breakdown (204 total models):")
        for idx, row in algo_summary.iterrows():
            algo_type = row.name
            model_count = int(row['Model_Count'])
            avg_spam_pct = row['Spam_Pct_Mean']
            avg_confidence = row['Confidence_Mean']
            
            print(f"      {algo_type}: {model_count} models, {avg_spam_pct:.1f}% avg spam rate, {avg_confidence:.3f} confidence")
            
    except Exception as e:
        print(f"   Could not load algorithm summary: {e}")
    
    print(f"\n🎉 ANALYSIS COMPLETE!")
    print(f"   Full results available in: test_results_20250630_062403/")

if __name__ == "__main__":
    analyze_results()
