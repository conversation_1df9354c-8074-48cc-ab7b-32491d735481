#!/usr/bin/env python3
"""
YouTube Spam Classification - Comprehensive Visualizations
==========================================================

Creates detailed visualizations for all algorithm variants and split scenarios:
- Algorithm type performance comparisons
- SVM kernel comparisons
- Naive Bayes variant analysis
- Decision Tree and Random Forest comparisons
- Original vs Reversed split analysis
- Training time analysis

Author: AI Assistant
Date: 2025-06-29
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class ComprehensiveVisualizations:
    """Create comprehensive visualizations for all algorithm analysis"""
    
    def __init__(self):
        self.analysis_dir = "NEW-YOUTUBE-SPAM2025/comprehensive_analysis"
        self.viz_dir = "NEW-YOUTUBE-SPAM2025/comprehensive_visualizations"
        os.makedirs(self.viz_dir, exist_ok=True)
        
        # Load master results
        self.df = pd.read_csv(os.path.join(self.analysis_dir, "master_results.csv"))
        
        # Set style
        plt.style.use('default')
        sns.set_palette("husl")
    
    def create_algorithm_type_comparison(self):
        """Create algorithm type performance comparison"""
        print("📊 Creating algorithm type comparison...")
        
        fig, axes = plt.subplots(2, 2, figsize=(20, 16))
        fig.suptitle('ALGORITHM TYPE PERFORMANCE COMPARISON\nAll Variants Across Original and Reversed Splits', 
                     fontsize=16, fontweight='bold')
        
        # 1. Average accuracy by algorithm type
        ax1 = axes[0, 0]
        type_accuracy = self.df.groupby(['Algorithm_Type', 'Split_Type'])['Accuracy'].mean().unstack()
        type_accuracy.plot(kind='bar', ax=ax1, width=0.8)
        ax1.set_title('Average Accuracy by Algorithm Type', fontweight='bold')
        ax1.set_ylabel('Accuracy')
        ax1.set_xlabel('Algorithm Type')
        ax1.legend(title='Split Type')
        ax1.grid(True, alpha=0.3)
        ax1.tick_params(axis='x', rotation=45)
        
        # Add value labels
        for container in ax1.containers:
            ax1.bar_label(container, fmt='%.3f', rotation=90, fontsize=8)
        
        # 2. Training time by algorithm type
        ax2 = axes[0, 1]
        type_time = self.df.groupby(['Algorithm_Type', 'Split_Type'])['Training_Time'].mean().unstack()
        type_time.plot(kind='bar', ax=ax2, width=0.8, color=['lightcoral', 'lightblue'])
        ax2.set_title('Average Training Time by Algorithm Type', fontweight='bold')
        ax2.set_ylabel('Training Time (seconds)')
        ax2.set_xlabel('Algorithm Type')
        ax2.legend(title='Split Type')
        ax2.grid(True, alpha=0.3)
        ax2.tick_params(axis='x', rotation=45)
        
        # 3. Spam F1-Score by algorithm type
        ax3 = axes[1, 0]
        type_f1 = self.df.groupby(['Algorithm_Type', 'Split_Type'])['Spam_F1'].mean().unstack()
        type_f1.plot(kind='bar', ax=ax3, width=0.8, color=['gold', 'lightgreen'])
        ax3.set_title('Average Spam F1-Score by Algorithm Type', fontweight='bold')
        ax3.set_ylabel('Spam F1-Score')
        ax3.set_xlabel('Algorithm Type')
        ax3.legend(title='Split Type')
        ax3.grid(True, alpha=0.3)
        ax3.tick_params(axis='x', rotation=45)
        
        # 4. Performance distribution
        ax4 = axes[1, 1]
        sns.boxplot(data=self.df, x='Algorithm_Type', y='Accuracy', hue='Split_Type', ax=ax4)
        ax4.set_title('Accuracy Distribution by Algorithm Type', fontweight='bold')
        ax4.set_ylabel('Accuracy')
        ax4.set_xlabel('Algorithm Type')
        ax4.tick_params(axis='x', rotation=45)
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.viz_dir, "algorithm_type_comparison.png"), dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ Algorithm type comparison created")
    
    def create_svm_kernel_analysis(self):
        """Create detailed SVM kernel comparison"""
        print("📊 Creating SVM kernel analysis...")
        
        # Filter SVM algorithms
        svm_data = self.df[self.df['Algorithm_Type'] == 'Support Vector Machine'].copy()
        
        fig, axes = plt.subplots(2, 2, figsize=(18, 14))
        fig.suptitle('SVM KERNEL PERFORMANCE ANALYSIS\nComparison of All SVM Variants', 
                     fontsize=16, fontweight='bold')
        
        # 1. Accuracy comparison
        ax1 = axes[0, 0]
        svm_accuracy = svm_data.groupby(['Algorithm', 'Split_Type'])['Accuracy'].mean().unstack()
        svm_accuracy.plot(kind='bar', ax=ax1, width=0.8)
        ax1.set_title('SVM Kernel Accuracy Comparison', fontweight='bold')
        ax1.set_ylabel('Accuracy')
        ax1.set_xlabel('SVM Variant')
        ax1.legend(title='Split Type')
        ax1.grid(True, alpha=0.3)
        ax1.tick_params(axis='x', rotation=45)
        
        # 2. Training time comparison
        ax2 = axes[0, 1]
        svm_time = svm_data.groupby(['Algorithm', 'Split_Type'])['Training_Time'].mean().unstack()
        svm_time.plot(kind='bar', ax=ax2, width=0.8, color=['orange', 'purple'])
        ax2.set_title('SVM Kernel Training Time Comparison', fontweight='bold')
        ax2.set_ylabel('Training Time (seconds)')
        ax2.set_xlabel('SVM Variant')
        ax2.legend(title='Split Type')
        ax2.grid(True, alpha=0.3)
        ax2.tick_params(axis='x', rotation=45)
        
        # 3. Performance vs Training Time scatter
        ax3 = axes[1, 0]
        for split_type in ['Original', 'Reversed']:
            split_data = svm_data[svm_data['Split_Type'] == split_type]
            ax3.scatter(split_data['Training_Time'], split_data['Accuracy'], 
                       label=split_type, alpha=0.7, s=60)
        
        ax3.set_title('SVM Performance vs Training Time', fontweight='bold')
        ax3.set_xlabel('Training Time (seconds)')
        ax3.set_ylabel('Accuracy')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. Spam detection performance
        ax4 = axes[1, 1]
        svm_spam_f1 = svm_data.groupby(['Algorithm', 'Split_Type'])['Spam_F1'].mean().unstack()
        svm_spam_f1.plot(kind='bar', ax=ax4, width=0.8, color=['red', 'blue'])
        ax4.set_title('SVM Spam Detection F1-Score', fontweight='bold')
        ax4.set_ylabel('Spam F1-Score')
        ax4.set_xlabel('SVM Variant')
        ax4.legend(title='Split Type')
        ax4.grid(True, alpha=0.3)
        ax4.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.viz_dir, "svm_kernel_analysis.png"), dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ SVM kernel analysis created")
    
    def create_naive_bayes_analysis(self):
        """Create Naive Bayes variant comparison"""
        print("📊 Creating Naive Bayes analysis...")
        
        # Filter Naive Bayes algorithms
        nb_data = self.df[self.df['Algorithm_Type'] == 'Naive Bayes'].copy()
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('NAIVE BAYES VARIANT ANALYSIS\nComparison of All Naive Bayes Types', 
                     fontsize=16, fontweight='bold')
        
        # 1. Accuracy comparison
        ax1 = axes[0, 0]
        nb_accuracy = nb_data.groupby(['Algorithm', 'Split_Type'])['Accuracy'].mean().unstack()
        nb_accuracy.plot(kind='bar', ax=ax1, width=0.8, color=['green', 'orange'])
        ax1.set_title('Naive Bayes Accuracy Comparison', fontweight='bold')
        ax1.set_ylabel('Accuracy')
        ax1.set_xlabel('Naive Bayes Variant')
        ax1.legend(title='Split Type')
        ax1.grid(True, alpha=0.3)
        ax1.tick_params(axis='x', rotation=45)
        
        # 2. Training speed
        ax2 = axes[0, 1]
        nb_time = nb_data.groupby(['Algorithm', 'Split_Type'])['Training_Time'].mean().unstack()
        nb_time.plot(kind='bar', ax=ax2, width=0.8, color=['cyan', 'magenta'])
        ax2.set_title('Naive Bayes Training Speed', fontweight='bold')
        ax2.set_ylabel('Training Time (seconds)')
        ax2.set_xlabel('Naive Bayes Variant')
        ax2.legend(title='Split Type')
        ax2.grid(True, alpha=0.3)
        ax2.tick_params(axis='x', rotation=45)
        
        # 3. Precision vs Recall
        ax3 = axes[1, 0]
        for split_type in ['Original', 'Reversed']:
            split_data = nb_data[nb_data['Split_Type'] == split_type]
            ax3.scatter(split_data['Spam_Recall'], split_data['Spam_Precision'], 
                       label=split_type, alpha=0.7, s=80)
        
        ax3.set_title('Naive Bayes Precision vs Recall', fontweight='bold')
        ax3.set_xlabel('Spam Recall')
        ax3.set_ylabel('Spam Precision')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. F1-Score comparison
        ax4 = axes[1, 1]
        nb_f1 = nb_data.groupby(['Algorithm', 'Split_Type'])['Spam_F1'].mean().unstack()
        nb_f1.plot(kind='bar', ax=ax4, width=0.8, color=['yellow', 'pink'])
        ax4.set_title('Naive Bayes F1-Score Comparison', fontweight='bold')
        ax4.set_ylabel('Spam F1-Score')
        ax4.set_xlabel('Naive Bayes Variant')
        ax4.legend(title='Split Type')
        ax4.grid(True, alpha=0.3)
        ax4.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.viz_dir, "naive_bayes_analysis.png"), dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ Naive Bayes analysis created")
    
    def create_tree_ensemble_analysis(self):
        """Create Decision Tree and Random Forest analysis"""
        print("📊 Creating tree and ensemble analysis...")
        
        # Filter tree-based algorithms
        tree_data = self.df[self.df['Algorithm_Type'].isin(['Decision Tree', 'Random Forest'])].copy()
        
        fig, axes = plt.subplots(2, 2, figsize=(18, 14))
        fig.suptitle('DECISION TREE & RANDOM FOREST ANALYSIS\nTree-based Algorithm Comparison', 
                     fontsize=16, fontweight='bold')
        
        # 1. Accuracy by algorithm
        ax1 = axes[0, 0]
        tree_accuracy = tree_data.groupby(['Algorithm', 'Split_Type'])['Accuracy'].mean().unstack()
        tree_accuracy.plot(kind='bar', ax=ax1, width=0.8)
        ax1.set_title('Tree-based Algorithm Accuracy', fontweight='bold')
        ax1.set_ylabel('Accuracy')
        ax1.set_xlabel('Algorithm')
        ax1.legend(title='Split Type')
        ax1.grid(True, alpha=0.3)
        ax1.tick_params(axis='x', rotation=45)
        
        # 2. Training time comparison
        ax2 = axes[0, 1]
        tree_time = tree_data.groupby(['Algorithm', 'Split_Type'])['Training_Time'].mean().unstack()
        tree_time.plot(kind='bar', ax=ax2, width=0.8, color=['brown', 'olive'])
        ax2.set_title('Tree-based Algorithm Training Time', fontweight='bold')
        ax2.set_ylabel('Training Time (seconds)')
        ax2.set_xlabel('Algorithm')
        ax2.legend(title='Split Type')
        ax2.grid(True, alpha=0.3)
        ax2.tick_params(axis='x', rotation=45)
        
        # 3. Decision Tree criteria comparison
        ax3 = axes[1, 0]
        dt_data = tree_data[tree_data['Algorithm_Type'] == 'Decision Tree']
        dt_criteria = dt_data.groupby(['Algorithm', 'Split_Type'])['Accuracy'].mean().unstack()
        dt_criteria.plot(kind='bar', ax=ax3, width=0.8, color=['red', 'blue'])
        ax3.set_title('Decision Tree Criteria Comparison\n(Gini, Entropy, Log_Loss)', fontweight='bold')
        ax3.set_ylabel('Accuracy')
        ax3.set_xlabel('Decision Tree Variant')
        ax3.legend(title='Split Type')
        ax3.grid(True, alpha=0.3)
        ax3.tick_params(axis='x', rotation=45)
        
        # 4. Random Forest variants
        ax4 = axes[1, 1]
        rf_data = tree_data[tree_data['Algorithm_Type'] == 'Random Forest']
        rf_variants = rf_data.groupby(['Algorithm', 'Split_Type'])['Accuracy'].mean().unstack()
        rf_variants.plot(kind='bar', ax=ax4, width=0.8, color=['green', 'purple'])
        ax4.set_title('Random Forest Variants Comparison', fontweight='bold')
        ax4.set_ylabel('Accuracy')
        ax4.set_xlabel('Random Forest Variant')
        ax4.legend(title='Split Type')
        ax4.grid(True, alpha=0.3)
        ax4.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.viz_dir, "tree_ensemble_analysis.png"), dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ Tree and ensemble analysis created")
    
    def create_logistic_regression_analysis(self):
        """Create Logistic Regression variant analysis"""
        print("📊 Creating Logistic Regression analysis...")
        
        # Filter LR algorithms
        lr_data = self.df[self.df['Algorithm_Type'] == 'Logistic Regression'].copy()
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('LOGISTIC REGRESSION ANALYSIS\nSolver and Regularization Comparison', 
                     fontsize=16, fontweight='bold')
        
        # 1. Accuracy by solver
        ax1 = axes[0, 0]
        lr_accuracy = lr_data.groupby(['Algorithm', 'Split_Type'])['Accuracy'].mean().unstack()
        lr_accuracy.plot(kind='bar', ax=ax1, width=0.8, color=['teal', 'coral'])
        ax1.set_title('Logistic Regression Solver Comparison', fontweight='bold')
        ax1.set_ylabel('Accuracy')
        ax1.set_xlabel('LR Variant')
        ax1.legend(title='Split Type')
        ax1.grid(True, alpha=0.3)
        ax1.tick_params(axis='x', rotation=45)
        
        # 2. Training time
        ax2 = axes[0, 1]
        lr_time = lr_data.groupby(['Algorithm', 'Split_Type'])['Training_Time'].mean().unstack()
        lr_time.plot(kind='bar', ax=ax2, width=0.8, color=['gold', 'silver'])
        ax2.set_title('Logistic Regression Training Time', fontweight='bold')
        ax2.set_ylabel('Training Time (seconds)')
        ax2.set_xlabel('LR Variant')
        ax2.legend(title='Split Type')
        ax2.grid(True, alpha=0.3)
        ax2.tick_params(axis='x', rotation=45)
        
        # 3. Regularization impact
        ax3 = axes[1, 0]
        regularization_order = ['LR_L1', 'LR_L2', 'LR_ElasticNet', 'LR_LBFGS', 'LR_SAG', 'LR_SAGA']
        lr_reg = lr_data[lr_data['Algorithm'].isin(regularization_order)]
        lr_reg_pivot = lr_reg.groupby(['Algorithm', 'Split_Type'])['Accuracy'].mean().unstack()
        lr_reg_pivot.plot(kind='bar', ax=ax3, width=0.8, color=['navy', 'maroon'])
        ax3.set_title('Regularization Impact on Performance', fontweight='bold')
        ax3.set_ylabel('Accuracy')
        ax3.set_xlabel('Regularization Type')
        ax3.legend(title='Split Type')
        ax3.grid(True, alpha=0.3)
        ax3.tick_params(axis='x', rotation=45)
        
        # 4. Performance consistency
        ax4 = axes[1, 1]
        sns.boxplot(data=lr_data, x='Algorithm', y='Accuracy', hue='Split_Type', ax=ax4)
        ax4.set_title('Logistic Regression Performance Consistency', fontweight='bold')
        ax4.set_ylabel('Accuracy')
        ax4.set_xlabel('LR Variant')
        ax4.tick_params(axis='x', rotation=45)
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.viz_dir, "logistic_regression_analysis.png"), dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ Logistic Regression analysis created")
    
    def create_overall_performance_heatmap(self):
        """Create overall performance heatmap"""
        print("📊 Creating overall performance heatmap...")
        
        # Create pivot table for heatmap
        heatmap_data = self.df.pivot_table(
            values='Accuracy', 
            index='Algorithm', 
            columns=['Split_Type'], 
            aggfunc='mean'
        )
        
        # Create figure
        fig, ax = plt.subplots(figsize=(12, 20))
        
        # Create heatmap
        sns.heatmap(heatmap_data, 
                   annot=True, 
                   fmt='.3f', 
                   cmap='RdYlGn', 
                   center=0.85,
                   ax=ax,
                   cbar_kws={'label': 'Accuracy'})
        
        ax.set_title('COMPREHENSIVE ALGORITHM PERFORMANCE HEATMAP\nAccuracy Across All Variants and Split Types', 
                    fontsize=14, fontweight='bold', pad=20)
        ax.set_xlabel('Split Type', fontweight='bold')
        ax.set_ylabel('Algorithm Variant', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.viz_dir, "overall_performance_heatmap.png"), dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ Overall performance heatmap created")
    
    def create_training_time_analysis(self):
        """Create training time analysis"""
        print("📊 Creating training time analysis...")
        
        fig, axes = plt.subplots(2, 2, figsize=(18, 14))
        fig.suptitle('TRAINING TIME ANALYSIS\nEfficiency Across All Algorithms', 
                     fontsize=16, fontweight='bold')
        
        # 1. Training time by algorithm type
        ax1 = axes[0, 0]
        time_by_type = self.df.groupby('Algorithm_Type')['Training_Time'].mean().sort_values()
        time_by_type.plot(kind='bar', ax=ax1, color='skyblue')
        ax1.set_title('Average Training Time by Algorithm Type', fontweight='bold')
        ax1.set_ylabel('Training Time (seconds)')
        ax1.set_xlabel('Algorithm Type')
        ax1.tick_params(axis='x', rotation=45)
        ax1.grid(True, alpha=0.3)
        
        # 2. Accuracy vs Training Time scatter
        ax2 = axes[0, 1]
        colors = {'Original': 'blue', 'Reversed': 'red'}
        for split_type in ['Original', 'Reversed']:
            split_data = self.df[self.df['Split_Type'] == split_type]
            ax2.scatter(split_data['Training_Time'], split_data['Accuracy'], 
                       c=colors[split_type], label=split_type, alpha=0.6, s=30)
        
        ax2.set_title('Accuracy vs Training Time Trade-off', fontweight='bold')
        ax2.set_xlabel('Training Time (seconds)')
        ax2.set_ylabel('Accuracy')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. Fastest algorithms
        ax3 = axes[1, 0]
        fastest_10 = self.df.nsmallest(15, 'Training_Time')
        fastest_10.plot(x='Algorithm', y='Training_Time', kind='bar', ax=ax3, color='lightgreen')
        ax3.set_title('15 Fastest Training Algorithms', fontweight='bold')
        ax3.set_ylabel('Training Time (seconds)')
        ax3.set_xlabel('Algorithm')
        ax3.tick_params(axis='x', rotation=45)
        ax3.grid(True, alpha=0.3)
        
        # 4. Training time distribution
        ax4 = axes[1, 1]
        self.df.boxplot(column='Training_Time', by='Algorithm_Type', ax=ax4)
        ax4.set_title('Training Time Distribution by Algorithm Type', fontweight='bold')
        ax4.set_ylabel('Training Time (seconds)')
        ax4.set_xlabel('Algorithm Type')
        plt.setp(ax4.xaxis.get_majorticklabels(), rotation=45)
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.viz_dir, "training_time_analysis.png"), dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ Training time analysis created")
    
    def run_all_visualizations(self):
        """Run all visualization creation"""
        print("YOUTUBE SPAM CLASSIFICATION - COMPREHENSIVE VISUALIZATIONS")
        print("=" * 65)
        print("Creating detailed visualizations for all algorithm variants")
        print("=" * 65)
        
        self.create_algorithm_type_comparison()
        self.create_svm_kernel_analysis()
        self.create_naive_bayes_analysis()
        self.create_tree_ensemble_analysis()
        self.create_logistic_regression_analysis()
        self.create_overall_performance_heatmap()
        self.create_training_time_analysis()
        
        print(f"\n🎉 All visualizations complete!")
        print(f"📁 Visualizations saved to: {self.viz_dir}")
        print("=" * 65)

def main():
    """Main function"""
    viz = ComprehensiveVisualizations()
    viz.run_all_visualizations()

if __name__ == "__main__":
    main()
