split,algorithm,accuracy,training_time,status
train_75_test_25,<PERSON><PERSON>_Linear,0.9038854805725971,0.410989,success
train_75_test_25,<PERSON>M_Polynomial,0.8834355828220859,0.707755,success
train_75_test_25,<PERSON><PERSON>_RB<PERSON>,0.918200408997955,0.659157,success
train_75_test_25,<PERSON><PERSON>_<PERSON>g<PERSON><PERSON>,0.9120654396728016,0.392051,success
train_75_test_25,SVM_RBF_Tuned,0.918200408997955,0.700371,success
train_75_test_25,SVM_Linear_Tuned,0.9038854805725971,0.423068,success
train_75_test_25,NB_<PERSON><PERSON>sian,0.0,0.0,"error: Sparse data was passed for X, but dense data is required. Use '.toarray()' to convert to a dense numpy array."
train_75_test_25,NB_Multinomial,0.8997955010224948,0.025735,success
train_75_test_25,<PERSON><PERSON><PERSON><PERSON>,0.8445807770961146,0.022064,success
train_75_test_25,NB_Complement,0.8875255623721882,0.034594,success
train_75_test_25,NB_Multinomial_Tuned,0.8936605316973415,0.004936,success
train_75_test_25,NB_Bernoulli_Tuned,0.8752556237218814,0.011625,success
train_75_test_25,LR_L1,0.8773006134969326,0.037543,success
train_75_test_25,LR_L2,0.9059304703476483,0.035401,success
train_75_test_25,LR_ElasticNet,0.885480572597137,0.062065,success
train_75_test_25,LR_LBFGS,0.9059304703476483,0.023275,success
train_75_test_25,LR_SAG,0.9059304703476483,0.03352,success
train_75_test_25,LR_SAGA,0.9059304703476483,0.032705,success
train_75_test_25,DT_Gini,0.8752556237218814,0.085818,success
train_75_test_25,DT_Entropy,0.852760736196319,0.084084,success
train_75_test_25,DT_Log_Loss,0.852760736196319,0.093953,success
train_75_test_25,DT_Gini_Pruned,0.8875255623721882,0.051334,success
train_75_test_25,DT_Entropy_Pruned,0.8670756646216768,0.040649,success
train_75_test_25,DT_Best_First,0.8752556237218814,0.084186,success
train_75_test_25,DT_Random_Split,0.8752556237218814,0.077542,success
train_75_test_25,RF_Gini,0.8936605316973415,0.611209,success
train_75_test_25,RF_Entropy,0.8813905930470347,0.616691,success
train_75_test_25,RF_Log_Loss,0.8813905930470347,0.633383,success
train_75_test_25,RF_Large,0.8752556237218814,0.429908,success
train_75_test_25,RF_Small,0.8773006134969326,0.131241,success
train_75_test_25,ExtraTrees,0.8916155419222904,0.879046,success
train_75_test_25,ExtraTrees_Large,0.8732106339468303,0.340362,success
train_75_test_25,GradientBoosting,0.901840490797546,0.734363,success
train_75_test_25,AdaBoost,0.901840490797546,0.39252,success
train_75_test_25,AdaBoost_DT,0.8609406952965235,0.847828,success
train_70_test_30,SVM_Linear,0.9011925042589438,0.372387,success
train_70_test_30,SVM_Polynomial,0.879045996592845,0.531368,success
train_70_test_30,SVM_RBF,0.9063032367972743,0.55546,success
train_70_test_30,SVM_Sigmoid,0.9045996592844975,0.346543,success
train_70_test_30,SVM_RBF_Tuned,0.9028960817717206,0.576654,success
train_70_test_30,SVM_Linear_Tuned,0.9011925042589438,0.382698,success
train_70_test_30,NB_Gaussian,0.0,0.0,"error: Sparse data was passed for X, but dense data is required. Use '.toarray()' to convert to a dense numpy array."
train_70_test_30,NB_Multinomial,0.9028960817717206,0.021128,success
train_70_test_30,NB_Bernoulli,0.8432708688245315,0.028851,success
train_70_test_30,NB_Complement,0.8909710391822828,0.022617,success
train_70_test_30,NB_Multinomial_Tuned,0.8926746166950597,0.020655,success
train_70_test_30,NB_Bernoulli_Tuned,0.8756388415672913,0.004915,success
train_70_test_30,LR_L1,0.8773424190800682,0.015019,success
train_70_test_30,LR_L2,0.9045996592844975,0.021481,success
train_70_test_30,LR_ElasticNet,0.8807495741056218,0.064227,success
train_70_test_30,LR_LBFGS,0.9028960817717206,0.036061,success
train_70_test_30,LR_SAG,0.9045996592844975,0.022879,success
train_70_test_30,LR_SAGA,0.9045996592844975,0.029609,success
train_70_test_30,DT_Gini,0.868824531516184,0.077057,success
train_70_test_30,DT_Entropy,0.858603066439523,0.070655,success
train_70_test_30,DT_Log_Loss,0.858603066439523,0.085074,success
train_70_test_30,DT_Gini_Pruned,0.8824531516183987,0.036842,success
train_70_test_30,DT_Entropy_Pruned,0.8671209540034072,0.047361,success
train_70_test_30,DT_Best_First,0.868824531516184,0.083609,success
train_70_test_30,DT_Random_Split,0.8654173764906303,0.06621,success
train_70_test_30,RF_Gini,0.889267461669506,0.567947,success
train_70_test_30,RF_Entropy,0.8807495741056218,0.617227,success
train_70_test_30,RF_Log_Loss,0.8807495741056218,0.655725,success
train_70_test_30,RF_Large,0.8739352640545145,0.495378,success
train_70_test_30,RF_Small,0.868824531516184,0.107471,success
train_70_test_30,ExtraTrees,0.9011925042589438,0.794376,success
train_70_test_30,ExtraTrees_Large,0.879045996592845,0.350631,success
train_70_test_30,GradientBoosting,0.8977853492333902,0.732246,success
train_70_test_30,AdaBoost,0.8841567291311755,0.431842,success
train_70_test_30,AdaBoost_DT,0.8654173764906303,0.885119,success
train_65_test_35,SVM_Linear,0.9007299270072993,0.304597,success
train_65_test_35,SVM_Polynomial,0.872992700729927,0.474958,success
train_65_test_35,SVM_RBF,0.9065693430656935,0.506284,success
train_65_test_35,SVM_Sigmoid,0.9007299270072993,0.315534,success
train_65_test_35,SVM_RBF_Tuned,0.9036496350364963,0.499647,success
train_65_test_35,SVM_Linear_Tuned,0.9007299270072993,0.343782,success
train_65_test_35,NB_Gaussian,0.0,0.0,"error: Sparse data was passed for X, but dense data is required. Use '.toarray()' to convert to a dense numpy array."
train_65_test_35,NB_Multinomial,0.8934306569343066,0.023143,success
train_65_test_35,NB_Bernoulli,0.8467153284671532,0.01596,success
train_65_test_35,NB_Complement,0.8832116788321168,0.013929,success
train_65_test_35,NB_Multinomial_Tuned,0.8890510948905109,0.018869,success
train_65_test_35,NB_Bernoulli_Tuned,0.8744525547445255,0.019495,success
train_65_test_35,LR_L1,0.8773722627737226,0.022273,success
train_65_test_35,LR_L2,0.8978102189781022,0.016846,success
train_65_test_35,LR_ElasticNet,0.8846715328467153,0.029847,success
train_65_test_35,LR_LBFGS,0.8978102189781022,0.042764,success
train_65_test_35,LR_SAG,0.8978102189781022,0.02187,success
train_65_test_35,LR_SAGA,0.8978102189781022,0.022795,success
train_65_test_35,DT_Gini,0.8613138686131386,0.062686,success
train_65_test_35,DT_Entropy,0.8613138686131386,0.072757,success
train_65_test_35,DT_Log_Loss,0.8613138686131386,0.079877,success
train_65_test_35,DT_Gini_Pruned,0.8817518248175182,0.04306,success
train_65_test_35,DT_Entropy_Pruned,0.8656934306569343,0.035989,success
train_65_test_35,DT_Best_First,0.8613138686131386,0.057627,success
train_65_test_35,DT_Random_Split,0.8744525547445255,0.083369,success
train_65_test_35,RF_Gini,0.8890510948905109,0.504389,success
train_65_test_35,RF_Entropy,0.8905109489051095,0.51252,success
train_65_test_35,RF_Log_Loss,0.8905109489051095,0.493064,success
train_65_test_35,RF_Large,0.8802919708029197,0.40096,success
train_65_test_35,RF_Small,0.8759124087591241,0.112528,success
train_65_test_35,ExtraTrees,0.8963503649635036,0.676624,success
train_65_test_35,ExtraTrees_Large,0.8686131386861314,0.329771,success
train_65_test_35,GradientBoosting,0.9007299270072993,0.622562,success
train_65_test_35,AdaBoost,0.8934306569343066,0.415062,success
train_65_test_35,AdaBoost_DT,0.8671532846715329,0.761879,success
