TRAIN_65_TEST_35 - DETAILED SPLIT ANALYSIS
============================================================
Analysis Date: 2025-06-30 00:39:29

SPLIT CONFIGURATION:
-------------------------
Split Type: Original
Training Size: 1271 samples
Test Size: 685 samples
Train/Test Ratio: 1.86

PERFORMANCE OVERVIEW:
-------------------------
Models Tested: 34
Best Accuracy: 0.9066 (90.66%)
Average Accuracy: 0.8837 (88.37%)
Worst Accuracy: 0.8467 (84.67%)
Standard Deviation: 0.0155

TOP 5 PERFORMERS:
--------------------
1. SVM_RBF
   Accuracy: 0.9066 (90.66%)
   Spam F1: 0.9062
   Training Time: 0.51s

2. SVM_RBF_Tuned
   Accuracy: 0.9036 (90.36%)
   Spam F1: 0.9041
   Training Time: 0.50s

3. SVM_Linear
   Accuracy: 0.9007 (90.07%)
   Spam F1: 0.8985
   Training Time: 0.30s

4. SVM_Sigmoid
   Accuracy: 0.9007 (90.07%)
   Spam F1: 0.8979
   Training Time: 0.32s

5. SVM_Linear_Tuned
   Accuracy: 0.9007 (90.07%)
   Spam F1: 0.8985
   Training Time: 0.34s

PERFORMANCE BY ALGORITHM TYPE:
-----------------------------------
Support Vector Machine:
  Models: 6
  Average Accuracy: 0.8976
  Best Accuracy: 0.9066
  Average Training Time: 0.41s

Naive Bayes:
  Models: 5
  Average Accuracy: 0.8774
  Best Accuracy: 0.8934
  Average Training Time: 0.02s

Logistic Regression:
  Models: 6
  Average Accuracy: 0.8922
  Best Accuracy: 0.8978
  Average Training Time: 0.03s

Decision Tree:
  Models: 8
  Average Accuracy: 0.8668
  Best Accuracy: 0.8818
  Average Training Time: 0.15s

Random Forest:
  Models: 7
  Average Accuracy: 0.8845
  Best Accuracy: 0.8964
  Average Training Time: 0.43s

Ensemble:
  Models: 2
  Average Accuracy: 0.8971
  Best Accuracy: 0.9007
  Average Training Time: 0.52s

TRAINING EFFICIENCY:
--------------------
Fastest Training:
  1. NB_Complement: 0.01s (Acc: 0.883)
  2. NB_Bernoulli: 0.02s (Acc: 0.847)
  3. LR_L2: 0.02s (Acc: 0.898)

Slowest Training:
  1. AdaBoost_DT: 0.76s (Acc: 0.867)
  2. ExtraTrees: 0.68s (Acc: 0.896)
  3. GradientBoosting: 0.62s (Acc: 0.901)

============================================================