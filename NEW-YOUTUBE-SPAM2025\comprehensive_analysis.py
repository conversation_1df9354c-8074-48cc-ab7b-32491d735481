#!/usr/bin/env python3
"""
YouTube Spam Classification - Comprehensive Algorithm Analysis
==============================================================

Analyzes performance across all algorithm variants and split scenarios:
- 35 algorithm variants across 6 split scenarios
- Detailed comparison by algorithm type and variant
- Performance analysis across original vs reversed splits
- Statistical analysis and rankings

Author: AI Assistant
Date: 2025-06-29
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import json
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class ComprehensiveAnalysis:
    """Comprehensive analysis of all algorithm variants and splits"""
    
    def __init__(self):
        self.results_dir = "NEW-YOUTUBE-SPAM2025/comprehensive_results"
        self.analysis_dir = "NEW-YOUTUBE-SPAM2025/comprehensive_analysis"
        os.makedirs(self.analysis_dir, exist_ok=True)
        
        self.load_all_results()
        self.create_master_dataframe()
    
    def load_all_results(self):
        """Load all comprehensive results"""
        print("📂 Loading comprehensive results...")
        
        # Load original results
        original_path = os.path.join(self.results_dir, "comprehensive_results_original.json")
        with open(original_path, 'r') as f:
            self.original_results = json.load(f)
        
        # Load reversed results
        reversed_path = os.path.join(self.results_dir, "comprehensive_results_reversed.json")
        with open(reversed_path, 'r') as f:
            self.reversed_results = json.load(f)
        
        print("✅ Results loaded successfully")
    
    def create_master_dataframe(self):
        """Create master dataframe with all results"""
        print("📊 Creating master dataframe...")
        
        all_data = []
        
        # Process original results
        for split_name, split_data in self.original_results.items():
            for algorithm, metrics in split_data.items():
                all_data.append({
                    'Split_Type': 'Original',
                    'Split_Name': split_name,
                    'Algorithm': algorithm,
                    'Algorithm_Type': metrics['algorithm_type'],
                    'Accuracy': metrics['accuracy'],
                    'Training_Time': metrics['training_time'],
                    'Train_Size': metrics['train_size'],
                    'Test_Size': metrics['test_size'],
                    'ROC_AUC': metrics.get('roc_auc', None),
                    'Ham_Precision': metrics['classification_report']['0']['precision'],
                    'Ham_Recall': metrics['classification_report']['0']['recall'],
                    'Ham_F1': metrics['classification_report']['0']['f1-score'],
                    'Spam_Precision': metrics['classification_report']['1']['precision'],
                    'Spam_Recall': metrics['classification_report']['1']['recall'],
                    'Spam_F1': metrics['classification_report']['1']['f1-score'],
                    'Macro_F1': metrics['classification_report']['macro avg']['f1-score'],
                    'Weighted_F1': metrics['classification_report']['weighted avg']['f1-score']
                })
        
        # Process reversed results
        for split_name, split_data in self.reversed_results.items():
            for algorithm, metrics in split_data.items():
                all_data.append({
                    'Split_Type': 'Reversed',
                    'Split_Name': split_name,
                    'Algorithm': algorithm,
                    'Algorithm_Type': metrics['algorithm_type'],
                    'Accuracy': metrics['accuracy'],
                    'Training_Time': metrics['training_time'],
                    'Train_Size': metrics['train_size'],
                    'Test_Size': metrics['test_size'],
                    'ROC_AUC': metrics.get('roc_auc', None),
                    'Ham_Precision': metrics['classification_report']['0']['precision'],
                    'Ham_Recall': metrics['classification_report']['0']['recall'],
                    'Ham_F1': metrics['classification_report']['0']['f1-score'],
                    'Spam_Precision': metrics['classification_report']['1']['precision'],
                    'Spam_Recall': metrics['classification_report']['1']['recall'],
                    'Spam_F1': metrics['classification_report']['1']['f1-score'],
                    'Macro_F1': metrics['classification_report']['macro avg']['f1-score'],
                    'Weighted_F1': metrics['classification_report']['weighted avg']['f1-score']
                })
        
        self.df = pd.DataFrame(all_data)
        print(f"✅ Master dataframe created: {len(self.df)} records")
        
        # Save master dataframe
        master_path = os.path.join(self.analysis_dir, "master_results.csv")
        self.df.to_csv(master_path, index=False)
    
    def analyze_algorithm_types(self):
        """Analyze performance by algorithm type"""
        print("🔍 Analyzing algorithm types...")
        
        # Group by algorithm type
        type_analysis = self.df.groupby(['Algorithm_Type', 'Split_Type']).agg({
            'Accuracy': ['mean', 'std', 'min', 'max', 'count'],
            'Training_Time': ['mean', 'std'],
            'Spam_F1': ['mean', 'std'],
            'ROC_AUC': ['mean', 'std']
        }).round(4)
        
        # Flatten column names
        type_analysis.columns = ['_'.join(col).strip() for col in type_analysis.columns]
        type_analysis = type_analysis.reset_index()
        
        # Save analysis
        type_path = os.path.join(self.analysis_dir, "algorithm_type_analysis.csv")
        type_analysis.to_csv(type_path, index=False)
        
        return type_analysis
    
    def analyze_algorithm_variants(self):
        """Analyze performance by specific algorithm variants"""
        print("🔍 Analyzing algorithm variants...")
        
        variant_analysis = []
        
        for algorithm in self.df['Algorithm'].unique():
            alg_data = self.df[self.df['Algorithm'] == algorithm]
            
            for split_type in ['Original', 'Reversed']:
                split_data = alg_data[alg_data['Split_Type'] == split_type]
                
                if len(split_data) > 0:
                    variant_analysis.append({
                        'Algorithm': algorithm,
                        'Algorithm_Type': split_data['Algorithm_Type'].iloc[0],
                        'Split_Type': split_type,
                        'Mean_Accuracy': split_data['Accuracy'].mean(),
                        'Std_Accuracy': split_data['Accuracy'].std(),
                        'Best_Accuracy': split_data['Accuracy'].max(),
                        'Worst_Accuracy': split_data['Accuracy'].min(),
                        'Mean_Training_Time': split_data['Training_Time'].mean(),
                        'Mean_Spam_F1': split_data['Spam_F1'].mean(),
                        'Mean_ROC_AUC': split_data['ROC_AUC'].mean() if split_data['ROC_AUC'].notna().any() else None,
                        'Count': len(split_data)
                    })
        
        variant_df = pd.DataFrame(variant_analysis)
        
        # Save analysis
        variant_path = os.path.join(self.analysis_dir, "algorithm_variant_analysis.csv")
        variant_df.to_csv(variant_path, index=False)
        
        return variant_df
    
    def create_performance_rankings(self):
        """Create comprehensive performance rankings"""
        print("🏆 Creating performance rankings...")
        
        rankings = {}
        
        # Overall best performers
        overall_best = self.df.nlargest(20, 'Accuracy')[['Algorithm', 'Split_Type', 'Split_Name', 'Accuracy', 'Spam_F1', 'Training_Time']]
        rankings['Overall_Best'] = overall_best
        
        # Best by algorithm type
        for alg_type in self.df['Algorithm_Type'].unique():
            type_data = self.df[self.df['Algorithm_Type'] == alg_type]
            best_of_type = type_data.nlargest(10, 'Accuracy')[['Algorithm', 'Split_Type', 'Split_Name', 'Accuracy', 'Spam_F1']]
            rankings[f'Best_{alg_type.replace(" ", "_")}'] = best_of_type
        
        # Best by split type
        for split_type in ['Original', 'Reversed']:
            split_data = self.df[self.df['Split_Type'] == split_type]
            best_of_split = split_data.nlargest(15, 'Accuracy')[['Algorithm', 'Split_Name', 'Accuracy', 'Spam_F1', 'Training_Time']]
            rankings[f'Best_{split_type}'] = best_of_split
        
        # Fastest algorithms
        fastest = self.df.nsmallest(20, 'Training_Time')[['Algorithm', 'Split_Type', 'Accuracy', 'Training_Time']]
        rankings['Fastest'] = fastest
        
        # Save rankings
        rankings_path = os.path.join(self.analysis_dir, "performance_rankings.xlsx")
        with pd.ExcelWriter(rankings_path, engine='openpyxl') as writer:
            for sheet_name, data in rankings.items():
                data.to_excel(writer, sheet_name=sheet_name, index=False)
        
        return rankings
    
    def analyze_split_impact(self):
        """Analyze impact of different split scenarios"""
        print("📊 Analyzing split impact...")
        
        split_impact = []
        
        for algorithm in self.df['Algorithm'].unique():
            alg_data = self.df[self.df['Algorithm'] == algorithm]
            
            # Get original and reversed performance
            original_data = alg_data[alg_data['Split_Type'] == 'Original']
            reversed_data = alg_data[alg_data['Split_Type'] == 'Reversed']
            
            if len(original_data) > 0 and len(reversed_data) > 0:
                orig_mean = original_data['Accuracy'].mean()
                rev_mean = reversed_data['Accuracy'].mean()
                performance_drop = orig_mean - rev_mean
                relative_drop = (performance_drop / orig_mean) * 100
                
                split_impact.append({
                    'Algorithm': algorithm,
                    'Algorithm_Type': alg_data['Algorithm_Type'].iloc[0],
                    'Original_Mean_Accuracy': orig_mean,
                    'Reversed_Mean_Accuracy': rev_mean,
                    'Performance_Drop': performance_drop,
                    'Relative_Drop_Percent': relative_drop,
                    'Robustness_Score': 100 - abs(relative_drop)  # Higher is more robust
                })
        
        impact_df = pd.DataFrame(split_impact)
        impact_df = impact_df.sort_values('Robustness_Score', ascending=False)
        
        # Save analysis
        impact_path = os.path.join(self.analysis_dir, "split_impact_analysis.csv")
        impact_df.to_csv(impact_path, index=False)
        
        return impact_df
    
    def create_summary_statistics(self):
        """Create comprehensive summary statistics"""
        print("📈 Creating summary statistics...")
        
        summary_stats = {}
        
        # Overall statistics
        summary_stats['Overall'] = {
            'Total_Models': len(self.df),
            'Total_Algorithms': self.df['Algorithm'].nunique(),
            'Total_Algorithm_Types': self.df['Algorithm_Type'].nunique(),
            'Best_Overall_Accuracy': self.df['Accuracy'].max(),
            'Worst_Overall_Accuracy': self.df['Accuracy'].min(),
            'Mean_Overall_Accuracy': self.df['Accuracy'].mean(),
            'Std_Overall_Accuracy': self.df['Accuracy'].std(),
            'Mean_Training_Time': self.df['Training_Time'].mean(),
            'Total_Training_Time': self.df['Training_Time'].sum()
        }
        
        # By split type
        for split_type in ['Original', 'Reversed']:
            split_data = self.df[self.df['Split_Type'] == split_type]
            summary_stats[split_type] = {
                'Models_Count': len(split_data),
                'Best_Accuracy': split_data['Accuracy'].max(),
                'Mean_Accuracy': split_data['Accuracy'].mean(),
                'Std_Accuracy': split_data['Accuracy'].std(),
                'Best_Algorithm': split_data.loc[split_data['Accuracy'].idxmax(), 'Algorithm'],
                'Mean_Training_Time': split_data['Training_Time'].mean(),
                'Mean_Spam_F1': split_data['Spam_F1'].mean()
            }
        
        # By algorithm type
        for alg_type in self.df['Algorithm_Type'].unique():
            type_data = self.df[self.df['Algorithm_Type'] == alg_type]
            summary_stats[alg_type] = {
                'Models_Count': len(type_data),
                'Variants_Count': type_data['Algorithm'].nunique(),
                'Best_Accuracy': type_data['Accuracy'].max(),
                'Mean_Accuracy': type_data['Accuracy'].mean(),
                'Best_Algorithm': type_data.loc[type_data['Accuracy'].idxmax(), 'Algorithm'],
                'Mean_Training_Time': type_data['Training_Time'].mean()
            }
        
        # Save summary
        summary_path = os.path.join(self.analysis_dir, "summary_statistics.json")
        with open(summary_path, 'w') as f:
            json.dump(summary_stats, f, indent=2, default=str)
        
        return summary_stats
    
    def create_detailed_report(self):
        """Create detailed analysis report"""
        print("📝 Creating detailed report...")
        
        report_lines = []
        report_lines.append("YOUTUBE SPAM CLASSIFICATION - COMPREHENSIVE ALGORITHM ANALYSIS")
        report_lines.append("=" * 80)
        report_lines.append(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"Total Models Analyzed: {len(self.df)}")
        report_lines.append(f"Algorithm Variants: {self.df['Algorithm'].nunique()}")
        report_lines.append(f"Algorithm Types: {self.df['Algorithm_Type'].nunique()}")
        report_lines.append("")
        
        # Overall performance
        report_lines.append("OVERALL PERFORMANCE SUMMARY:")
        report_lines.append("-" * 40)
        best_model = self.df.loc[self.df['Accuracy'].idxmax()]
        report_lines.append(f"Best Overall Model: {best_model['Algorithm']}")
        report_lines.append(f"Best Accuracy: {best_model['Accuracy']:.4f} ({best_model['Accuracy']*100:.2f}%)")
        report_lines.append(f"Split Type: {best_model['Split_Type']}")
        report_lines.append(f"Split Name: {best_model['Split_Name']}")
        report_lines.append("")
        
        # Algorithm type performance
        report_lines.append("PERFORMANCE BY ALGORITHM TYPE:")
        report_lines.append("-" * 40)
        type_performance = self.df.groupby('Algorithm_Type')['Accuracy'].agg(['mean', 'max', 'count']).sort_values('mean', ascending=False)
        
        for alg_type, stats in type_performance.iterrows():
            report_lines.append(f"{alg_type}:")
            report_lines.append(f"  Average Accuracy: {stats['mean']:.4f} ({stats['mean']*100:.2f}%)")
            report_lines.append(f"  Best Accuracy: {stats['max']:.4f} ({stats['max']*100:.2f}%)")
            report_lines.append(f"  Models Tested: {int(stats['count'])}")
            report_lines.append("")
        
        # Split type comparison
        report_lines.append("ORIGINAL vs REVERSED SPLIT COMPARISON:")
        report_lines.append("-" * 40)
        split_comparison = self.df.groupby('Split_Type')['Accuracy'].agg(['mean', 'max', 'min', 'std'])
        
        for split_type, stats in split_comparison.iterrows():
            report_lines.append(f"{split_type} Splits:")
            report_lines.append(f"  Average Accuracy: {stats['mean']:.4f} ({stats['mean']*100:.2f}%)")
            report_lines.append(f"  Best Accuracy: {stats['max']:.4f} ({stats['max']*100:.2f}%)")
            report_lines.append(f"  Worst Accuracy: {stats['min']:.4f} ({stats['min']*100:.2f}%)")
            report_lines.append(f"  Standard Deviation: {stats['std']:.4f}")
            report_lines.append("")
        
        # Top performers
        report_lines.append("TOP 10 PERFORMING MODELS:")
        report_lines.append("-" * 40)
        top_10 = self.df.nlargest(10, 'Accuracy')
        
        for i, (_, model) in enumerate(top_10.iterrows(), 1):
            report_lines.append(f"{i:2d}. {model['Algorithm']} ({model['Split_Type']})")
            report_lines.append(f"    Accuracy: {model['Accuracy']:.4f} ({model['Accuracy']*100:.2f}%)")
            report_lines.append(f"    Split: {model['Split_Name']}")
            report_lines.append(f"    Training Time: {model['Training_Time']:.2f}s")
            report_lines.append("")
        
        # Save report
        report_path = os.path.join(self.analysis_dir, "comprehensive_analysis_report.txt")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        print("✅ Detailed report created")
    
    def run_comprehensive_analysis(self):
        """Run complete comprehensive analysis"""
        print("YOUTUBE SPAM CLASSIFICATION - COMPREHENSIVE ALGORITHM ANALYSIS")
        print("=" * 70)
        print("Analyzing 35 algorithm variants across 6 split scenarios")
        print("=" * 70)
        
        # Run all analyses
        type_analysis = self.analyze_algorithm_types()
        variant_analysis = self.analyze_algorithm_variants()
        rankings = self.create_performance_rankings()
        split_impact = self.analyze_split_impact()
        summary_stats = self.create_summary_statistics()
        self.create_detailed_report()
        
        # Print quick summary
        print(f"\n📊 ANALYSIS SUMMARY:")
        print("-" * 40)
        print(f"Total Models Analyzed: {len(self.df)}")
        print(f"Algorithm Variants: {self.df['Algorithm'].nunique()}")
        print(f"Best Overall Accuracy: {self.df['Accuracy'].max():.4f} ({self.df['Accuracy'].max()*100:.2f}%)")
        
        best_model = self.df.loc[self.df['Accuracy'].idxmax()]
        print(f"Best Model: {best_model['Algorithm']}")
        print(f"Best Split: {best_model['Split_Type']} - {best_model['Split_Name']}")
        
        print(f"\n📁 All analysis files saved to: {self.analysis_dir}")
        print("=" * 70)
        
        return {
            'type_analysis': type_analysis,
            'variant_analysis': variant_analysis,
            'rankings': rankings,
            'split_impact': split_impact,
            'summary_stats': summary_stats
        }

def main():
    """Main function"""
    analysis = ComprehensiveAnalysis()
    results = analysis.run_comprehensive_analysis()

if __name__ == "__main__":
    main()
