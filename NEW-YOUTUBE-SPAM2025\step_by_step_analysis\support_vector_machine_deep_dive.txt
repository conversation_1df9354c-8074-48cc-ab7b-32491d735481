SUPPORT VECTOR MACHINE - DETAILED ANALYSIS
============================================================
Analysis Date: 2025-06-30 00:39:29

OVERVIEW:
--------------------
Total Variants Tested: 6
Total Models Trained: 36
Best Accuracy: 0.9182 (91.82%)
Average Accuracy: 0.8922 (89.22%)
Worst Accuracy: 0.8467 (84.67%)

BEST PERFORMER:
--------------------
Algorithm: SVM_RBF
Split Type: Original
Split Name: train_75_test_25
Accuracy: 0.9182 (91.82%)
Spam F1-Score: 0.9197
Training Time: 0.66 seconds

VARIANT COMPARISON:
--------------------
SVM_Linear:
  Average Accuracy: 0.8962
  Best Accuracy: 0.9039
  Consistency (Std): 0.0073
  Average Training Time: 0.23s
  Average Spam F1: 0.8934

SVM_Polynomial:
  Average Accuracy: 0.8653
  Best Accuracy: 0.8834
  Consistency (Std): 0.0154
  Average Training Time: 0.34s
  Average Spam F1: 0.8714

SVM_RBF:
  Average Accuracy: 0.8994
  Best Accuracy: 0.9182
  Consistency (Std): 0.0137
  Average Training Time: 0.34s
  Average Spam F1: 0.9000

SVM_Sigmoid:
  Average Accuracy: 0.8955
  Best Accuracy: 0.9121
  Consistency (Std): 0.0135
  Average Training Time: 0.22s
  Average Spam F1: 0.8915

SVM_RBF_Tuned:
  Average Accuracy: 0.9006
  Best Accuracy: 0.9182
  Consistency (Std): 0.0103
  Average Training Time: 0.36s
  Average Spam F1: 0.9017

SVM_Linear_Tuned:
  Average Accuracy: 0.8962
  Best Accuracy: 0.9039
  Consistency (Std): 0.0073
  Average Training Time: 0.24s
  Average Spam F1: 0.8934

PERFORMANCE BY SPLIT TYPE:
------------------------------
Original Splits:
  Models: 18
  Average Accuracy: 0.9011
  Best Accuracy: 0.9182
  Average Training Time: 0.47s

Reversed Splits:
  Models: 18
  Average Accuracy: 0.8832
  Best Accuracy: 0.8970
  Average Training Time: 0.11s

RECOMMENDATIONS:
--------------------
• RBF kernel generally provides best performance
• Linear kernel offers good speed-accuracy balance
• Polynomial kernel may overfit with small datasets
• Consider tuning C and gamma parameters

============================================================