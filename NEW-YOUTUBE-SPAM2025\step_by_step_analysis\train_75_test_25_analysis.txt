TRAIN_75_TEST_25 - DE<PERSON>ILED SPLIT ANALYSIS
============================================================
Analysis Date: 2025-06-30 00:39:29

SPLIT CONFIGURATION:
-------------------------
Split Type: Original
Training Size: 1467 samples
Test Size: 489 samples
Train/Test Ratio: 3.00

PERFORMANCE OVERVIEW:
-------------------------
Models Tested: 34
Best Accuracy: 0.9182 (91.82%)
Average Accuracy: 0.8868 (88.68%)
Worst Accuracy: 0.8446 (84.46%)
Standard Deviation: 0.0188

TOP 5 PERFORMERS:
--------------------
1. SVM_RBF
   Accuracy: 0.9182 (91.82%)
   Spam F1: 0.9197
   Training Time: 0.66s

2. SVM_RBF_Tuned
   Accuracy: 0.9182 (91.82%)
   Spam F1: 0.9200
   Training Time: 0.70s

3. SVM_Sigmoid
   Accuracy: 0.9121 (91.21%)
   Spam F1: 0.9102
   Training Time: 0.39s

4. LR_L2
   Accuracy: 0.9059 (90.59%)
   Spam F1: 0.9042
   Training Time: 0.04s

5. LR_LBFGS
   Accuracy: 0.9059 (90.59%)
   Spam F1: 0.9042
   Training Time: 0.02s

PERFORMANCE BY ALGORITHM TYPE:
-----------------------------------
Support Vector Machine:
  Models: 6
  Average Accuracy: 0.9066
  Best Accuracy: 0.9182
  Average Training Time: 0.55s

Naive Bayes:
  Models: 5
  Average Accuracy: 0.8802
  Best Accuracy: 0.8998
  Average Training Time: 0.02s

Logistic Regression:
  Models: 6
  Average Accuracy: 0.8978
  Best Accuracy: 0.9059
  Average Training Time: 0.04s

Decision Tree:
  Models: 8
  Average Accuracy: 0.8684
  Best Accuracy: 0.8875
  Average Training Time: 0.17s

Random Forest:
  Models: 7
  Average Accuracy: 0.8820
  Best Accuracy: 0.8937
  Average Training Time: 0.52s

Ensemble:
  Models: 2
  Average Accuracy: 0.9018
  Best Accuracy: 0.9018
  Average Training Time: 0.56s

TRAINING EFFICIENCY:
--------------------
Fastest Training:
  1. NB_Multinomial_Tuned: 0.00s (Acc: 0.894)
  2. NB_Bernoulli_Tuned: 0.01s (Acc: 0.875)
  3. NB_Bernoulli: 0.02s (Acc: 0.845)

Slowest Training:
  1. ExtraTrees: 0.88s (Acc: 0.892)
  2. AdaBoost_DT: 0.85s (Acc: 0.861)
  3. GradientBoosting: 0.73s (Acc: 0.902)

============================================================