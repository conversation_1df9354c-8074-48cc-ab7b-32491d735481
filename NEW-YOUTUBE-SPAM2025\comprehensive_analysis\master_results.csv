Split_Type,Split_Name,Algorithm,Algorithm_Type,Accuracy,Training_Time,Train_Size,Test_Size,ROC_AUC,Ham_Precision,<PERSON>_Recall,<PERSON>_<PERSON>,<PERSON>m_Precision,Spam_Recall,Spam_F1,<PERSON><PERSON>_F1,Weighted_F1
Original,train_75_test_25,<PERSON><PERSON>_Linear,Support Vector Machine,0.9038854805725971,0.410989,1467,489,0.9646288794402224,0.8715953307392996,0.9411764705882353,0.9050505050505051,0.9396551724137931,0.8685258964143426,0.9026915113871635,0.9038710082188344,0.9038396514523482
Original,train_75_test_25,SVM_Polynomial,Support Vector Machine,0.8834355828220859,0.707755,1467,489,0.9406491680337474,0.8851063829787233,0.8739495798319328,0.879492600422833,0.8818897637795275,0.8924302788844621,0.8871287128712871,0.8833106566470601,0.8834121591642685
Original,train_75_test_25,SVM_RBF,Support Vector Machine,0.918200408997955,0.659157,1467,489,0.9517643710870803,0.9090909090909091,0.9243697478991597,0.9166666666666666,0.9271255060728745,0.9123505976095617,0.9196787148594378,0.9181726907630522,0.9182127282134673
Original,train_75_test_25,SVM_Sigmoid,Support Vector Machine,0.9120654396728016,0.392051,1467,489,0.9652315109310656,0.8735632183908046,0.957983193277311,0.9138276553106213,0.956140350877193,0.8685258964143426,0.9102296450939458,0.9120286502022835,0.91198082389061
Original,train_75_test_25,SVM_RBF_Tuned,Support Vector Machine,0.918200408997955,0.700371,1467,489,0.9509608624326225,0.9125,0.9201680672268907,0.9163179916317992,0.9236947791164659,0.9163346613545816,0.92,0.9181589958158995,0.9182079386674197
Original,train_75_test_25,SVM_Linear_Tuned,Support Vector Machine,0.9038854805725971,0.423068,1467,489,0.9646288794402224,0.8715953307392996,0.9411764705882353,0.9050505050505051,0.9396551724137931,0.8685258964143426,0.9026915113871635,0.9038710082188344,0.9038396514523482
Original,train_75_test_25,NB_Multinomial,Naive Bayes,0.8997955010224948,0.025735,1467,489,0.9589792092135659,0.9276018099547512,0.8613445378151261,0.8932461873638344,0.8768656716417911,0.9362549800796812,0.905587668593449,0.8994169279786417,0.8995809762976448
Original,train_75_test_25,NB_Bernoulli,Naive Bayes,0.8445807770961146,0.022064,1467,489,0.9440891894606448,0.7682119205298014,0.9747899159663865,0.8592592592592593,0.9679144385026738,0.7211155378486056,0.8264840182648402,0.8428716387620497,0.842435976049445
Original,train_75_test_25,NB_Complement,Naive Bayes,0.8875255623721882,0.034594,1467,489,0.9589792092135659,0.8674698795180723,0.907563025210084,0.8870636550308009,0.9083333333333333,0.8685258964143426,0.8879837067209776,0.8875236808758893,0.8875359106018322
Original,train_75_test_25,NB_Multinomial_Tuned,Naive Bayes,0.8936605316973415,0.004936,1467,489,0.9531872509960159,0.9151785714285714,0.8613445378151261,0.8874458874458875,0.8754716981132076,0.9243027888446215,0.8992248062015504,0.893335346823719,0.893491917318426
Original,train_75_test_25,NB_Bernoulli_Tuned,Naive Bayes,0.8752556237218814,0.011625,1467,489,0.9344805651344203,0.8218181818181818,0.9495798319327731,0.8810916179337231,0.9439252336448598,0.8047808764940239,0.8688172043010752,0.8749544111173992,0.8747912542899714
Original,train_75_test_25,LR_L1,Logistic Regression,0.8773006134969326,0.037543,1467,489,0.9432187217516489,0.8201438848920863,0.957983193277311,0.8837209302325582,0.95260663507109,0.8007968127490039,0.8701298701298701,0.8769254001812141,0.8767447419180905
Original,train_75_test_25,LR_L2,Logistic Regression,0.9059304703476483,0.035401,1467,489,0.9571043556864977,0.8692307692307693,0.9495798319327731,0.9076305220883534,0.9475982532751092,0.8645418326693227,0.9041666666666667,0.90589859437751,0.9058525513095326
Original,train_75_test_25,LR_ElasticNet,Logistic Regression,0.885480572597137,0.062065,1467,489,0.9490357896146506,0.8395522388059702,0.9453781512605042,0.8893280632411067,0.9411764705882353,0.8286852589641435,0.8813559322033898,0.8853419977222483,0.8852360287002745
Original,train_75_test_25,LR_LBFGS,Logistic Regression,0.9059304703476483,0.023275,1467,489,0.957271753322843,0.8692307692307693,0.9495798319327731,0.9076305220883534,0.9475982532751092,0.8645418326693227,0.9041666666666667,0.90589859437751,0.9058525513095326
Original,train_75_test_25,LR_SAG,Logistic Regression,0.9059304703476483,0.03352,1467,489,0.9571378352137667,0.8692307692307693,0.9495798319327731,0.9076305220883534,0.9475982532751092,0.8645418326693227,0.9041666666666667,0.90589859437751,0.9058525513095326
Original,train_75_test_25,LR_SAGA,Logistic Regression,0.9059304703476483,0.032705,1467,489,0.9571210954501322,0.8692307692307693,0.9495798319327731,0.9076305220883534,0.9475982532751092,0.8645418326693227,0.9041666666666667,0.90589859437751,0.9058525513095326
Original,train_75_test_25,DT_Gini,Decision Tree,0.8752556237218814,0.085818,1467,489,0.9408918946064482,0.9359605911330049,0.7983193277310925,0.8616780045351474,0.8321678321678322,0.9482071713147411,0.8864059590316573,0.8740419817834024,0.8743706764750738
Original,train_75_test_25,DT_Entropy,Decision Tree,0.852760736196319,0.084084,1467,489,0.9155227828183066,0.8990384615384616,0.7857142857142857,0.8385650224215246,0.8185053380782918,0.9163346613545816,0.8646616541353384,0.8516133382784314,0.8519602260210487
Original,train_75_test_25,DT_Log_Loss,Decision Tree,0.852760736196319,0.093953,1467,489,0.9155227828183066,0.8990384615384616,0.7857142857142857,0.8385650224215246,0.8185053380782918,0.9163346613545816,0.8646616541353384,0.8516133382784314,0.8519602260210487
Original,train_75_test_25,DT_Gini_Pruned,Decision Tree,0.8875255623721882,0.051334,1467,489,0.8881532692758377,0.818815331010453,0.9873949579831933,0.8952380952380953,0.9851485148514851,0.7928286852589641,0.8785871964679912,0.8869126458530432,0.8866913148877964
Original,train_75_test_25,DT_Entropy_Pruned,Decision Tree,0.8670756646216768,0.040649,1467,489,0.8742843751046235,0.7912457912457912,0.9873949579831933,0.8785046728971962,0.984375,0.7529880478087649,0.8532731376975169,0.8658889052973566,0.8655535167926574
Original,train_75_test_25,DT_Best_First,Decision Tree,0.8752556237218814,0.084186,1467,489,0.9408918946064482,0.9359605911330049,0.7983193277310925,0.8616780045351474,0.8321678321678322,0.9482071713147411,0.8864059590316573,0.8740419817834024,0.8743706764750738
Original,train_75_test_25,DT_Random_Split,Decision Tree,0.8752556237218814,0.077542,1467,489,0.9377448190431552,0.9402985074626866,0.7941176470588235,0.8610478359908884,0.8298611111111112,0.952191235059761,0.8868274582560297,0.873937647123459,0.8742803210390488
Original,train_75_test_25,RF_Gini,Random Forest,0.8936605316973415,0.611209,1467,489,0.9604857879406741,0.9428571428571428,0.8319327731092437,0.8839285714285714,0.8566308243727598,0.952191235059761,0.9018867924528302,0.8929076819407008,0.8931463904001234
Original,train_75_test_25,RF_Entropy,Random Forest,0.8813905930470347,0.616691,1467,489,0.9620007365495999,0.9368932038834952,0.8109243697478992,0.8693693693693694,0.8409893992932862,0.9482071713147411,0.8913857677902621,0.8803775685798158,0.8806702200925679
Original,train_75_test_25,RF_Log_Loss,Random Forest,0.8813905930470347,0.633383,1467,489,0.9620007365495999,0.9368932038834952,0.8109243697478992,0.8693693693693694,0.8409893992932862,0.9482071713147411,0.8913857677902621,0.8803775685798158,0.8806702200925679
Original,train_75_test_25,RF_Large,Random Forest,0.8752556237218814,0.429908,1467,489,0.9502243128327028,0.8083623693379791,0.9747899159663865,0.8838095238095238,0.9702970297029703,0.7808764940239044,0.8653421633554084,0.8745758435824661,0.8743303674210104
Original,train_75_test_25,RF_Small,Random Forest,0.8773006134969326,0.131241,1467,489,0.9426411999062574,0.8156028368794326,0.9663865546218487,0.8846153846153846,0.961352657004831,0.7928286852589641,0.868995633187773,0.8768055089015787,0.8765978843938498
Original,train_75_test_25,ExtraTrees,Random Forest,0.8916155419222904,0.879046,1467,489,0.9634236164585356,0.9302325581395349,0.8403361344537815,0.8830022075055187,0.8613138686131386,0.9402390438247012,0.8990476190476191,0.8910249132765689,0.8912381958430795
Original,train_75_test_25,ExtraTrees_Large,Random Forest,0.8732106339468303,0.340362,1467,489,0.9458050152331848,0.8165467625899281,0.9537815126050421,0.8798449612403101,0.9478672985781991,0.796812749003984,0.8658008658008658,0.8728229135205879,0.8726362333153601
Original,train_75_test_25,GradientBoosting,Ensemble,0.901840490797546,0.734363,1467,489,0.9520154675415983,0.841726618705036,0.9831932773109243,0.9069767441860465,0.981042654028436,0.8247011952191236,0.8961038961038961,0.9015403201449712,0.9013957935344723
Original,train_75_test_25,AdaBoost,Ensemble,0.901840490797546,0.39252,1467,489,0.9406993873246509,0.8492647058823529,0.9705882352941176,0.9058823529411765,0.967741935483871,0.8366533864541833,0.8974358974358975,0.901659125188537,0.9015468512401028
Original,train_75_test_25,AdaBoost_DT,Decision Tree,0.8609406952965235,0.847828,1467,489,0.9501322441327127,0.9166666666666666,0.7857142857142857,0.8461538461538461,0.8210526315789474,0.9322709163346613,0.8731343283582089,0.8596440872560276,0.8600027235225478
Original,train_70_test_30,SVM_Linear,Support Vector Machine,0.9011925042589438,0.372387,1369,587,0.9655280585569884,0.8603174603174604,0.9508771929824561,0.9033333333333333,0.9485294117647058,0.8543046357615894,0.8989547038327527,0.901144018583043,0.9010806142376341
Original,train_70_test_30,SVM_Polynomial,Support Vector Machine,0.879045996592845,0.531368,1369,587,0.9404786801440689,0.8794326241134752,0.8701754385964913,0.8747795414462081,0.8786885245901639,0.8874172185430463,0.8830313014827018,0.8789054214644549,0.8790249103235864
Original,train_70_test_30,SVM_RBF,Support Vector Machine,0.9063032367972743,0.55546,1369,587,0.9511560357848262,0.8833333333333333,0.9298245614035088,0.905982905982906,0.9303135888501742,0.8841059602649006,0.9066213921901528,0.9063021490865294,0.906311394627861
Original,train_70_test_30,SVM_Sigmoid,Support Vector Machine,0.9045996592844975,0.346543,1369,587,0.9642035552457302,0.8589341692789969,0.9614035087719298,0.9072847682119205,0.9589552238805971,0.8509933774834437,0.9017543859649123,0.9045195770884165,0.9044394948923353
Original,train_70_test_30,SVM_RBF_Tuned,Support Vector Machine,0.9028960817717206,0.576654,1369,587,0.9510049959335425,0.88,0.9263157894736842,0.9025641025641026,0.926829268292683,0.8807947019867549,0.9032258064516129,0.9028949545078577,0.9029045362506922
Original,train_70_test_30,SVM_Linear_Tuned,Support Vector Machine,0.9011925042589438,0.382698,1369,587,0.9655280585569884,0.8603174603174604,0.9508771929824561,0.9033333333333333,0.9485294117647058,0.8543046357615894,0.8989547038327527,0.901144018583043,0.9010806142376341
Original,train_70_test_30,NB_Multinomial,Naive Bayes,0.9028960817717206,0.021128,1369,587,0.9590798187521784,0.9253731343283582,0.8701754385964913,0.8969258589511754,0.8840125391849529,0.9337748344370861,0.9082125603864735,0.9025692096688245,0.9027326457202725
Original,train_70_test_30,NB_Bernoulli,Naive Bayes,0.8432708688245315,0.028851,1369,587,0.9444347624026955,0.7643835616438356,0.9789473684210527,0.8584615384615385,0.972972972972973,0.7152317880794702,0.8244274809160306,0.8414445096887846,0.8409516826204083
Original,train_70_test_30,NB_Complement,Naive Bayes,0.8909710391822828,0.022617,1369,587,0.9590798187521784,0.8695652173913043,0.9122807017543859,0.8904109589041096,0.9131944444444444,0.8708609271523179,0.8915254237288136,0.8909681913164615,0.8909843292227819
Original,train_70_test_30,NB_Multinomial_Tuned,Naive Bayes,0.8926746166950597,0.020655,1369,587,0.9542697804112932,0.9111111111111111,0.8631578947368421,0.8864864864864865,0.8769716088328076,0.9205298013245033,0.8982229402261712,0.8923547133563289,0.8925246620050297
Original,train_70_test_30,NB_Bernoulli_Tuned,Naive Bayes,0.8756388415672913,0.004915,1369,587,0.9344312768676658,0.8173652694610778,0.9578947368421052,0.8820678513731826,0.9525691699604744,0.7980132450331126,0.8684684684684685,0.8752681599208255,0.8750712352961405
Original,train_70_test_30,LR_L1,Logistic Regression,0.8773424190800682,0.015019,1369,587,0.941129313349599,0.8160237388724035,0.9649122807017544,0.8842443729903537,0.96,0.7947019867549668,0.8695652173913043,0.8769047951908291,0.8766922350160558
Original,train_70_test_30,LR_L2,Logistic Regression,0.9045996592844975,0.021481,1369,587,0.9576275124898338,0.861198738170347,0.9578947368421052,0.9069767441860465,0.9555555555555556,0.8543046357615894,0.9020979020979021,0.9045373231419742,0.9044666755137813
Original,train_70_test_30,LR_ElasticNet,Logistic Regression,0.8807495741056218,0.064227,1369,587,0.9497153479725804,0.8267477203647416,0.9543859649122807,0.8859934853420195,0.9496124031007752,0.8112582781456954,0.875,0.8804967426710097,0.8803375525084762
Original,train_70_test_30,LR_LBFGS,Logistic Regression,0.9028960817717206,0.036061,1369,587,0.9578714999419077,0.8607594936708861,0.9543859649122807,0.9051580698835274,0.9520295202952029,0.8543046357615894,0.900523560209424,0.9028408150464757,0.9027737054515353
Original,train_70_test_30,LR_SAG,Logistic Regression,0.9045996592844975,0.022879,1369,587,0.9576158940397352,0.861198738170347,0.9578947368421052,0.9069767441860465,0.9555555555555556,0.8543046357615894,0.9020979020979021,0.9045373231419742,0.9044666755137813
Original,train_70_test_30,LR_SAGA,Logistic Regression,0.9045996592844975,0.029609,1369,587,0.9577553154409201,0.861198738170347,0.9578947368421052,0.9069767441860465,0.9555555555555556,0.8543046357615894,0.9020979020979021,0.9045373231419742,0.9044666755137813
Original,train_70_test_30,DT_Gini,Decision Tree,0.868824531516184,0.077057,1369,587,0.9320959683978157,0.9227642276422764,0.7964912280701755,0.8549905838041432,0.8299120234604106,0.9370860927152318,0.880248833592535,0.867619708698339,0.8679854584823277
Original,train_70_test_30,DT_Entropy,Decision Tree,0.858603066439523,0.070655,1369,587,0.9209829208783548,0.907258064516129,0.7894736842105263,0.8442776735459663,0.8230088495575221,0.9238410596026491,0.8705148205928237,0.857396247069395,0.8577761716859167
Original,train_70_test_30,DT_Log_Loss,Decision Tree,0.858603066439523,0.085074,1369,587,0.9209829208783548,0.907258064516129,0.7894736842105263,0.8442776735459663,0.8230088495575221,0.9238410596026491,0.8705148205928237,0.857396247069395,0.8577761716859167
Original,train_70_test_30,DT_Gini_Pruned,Decision Tree,0.8824531516183987,0.036842,1369,587,0.8841117694899501,0.8121387283236994,0.9859649122807017,0.8906497622820919,0.983402489626556,0.7847682119205298,0.8729281767955801,0.881788969538836,0.8815323537353686
Original,train_70_test_30,DT_Entropy_Pruned,Decision Tree,0.8671209540034072,0.047361,1369,587,0.8784768211920528,0.7899159663865546,0.9894736842105263,0.8785046728971962,0.9869565217391304,0.7516556291390728,0.8533834586466166,0.8659440657719064,0.8655803003185334
Original,train_70_test_30,DT_Best_First,Decision Tree,0.868824531516184,0.083609,1369,587,0.9320959683978157,0.9227642276422764,0.7964912280701755,0.8549905838041432,0.8299120234604106,0.9370860927152318,0.880248833592535,0.867619708698339,0.8679854584823277
Original,train_70_test_30,DT_Random_Split,Decision Tree,0.8654173764906303,0.06621,1369,587,0.9275763913093994,0.912,0.8,0.8523364485981308,0.8308605341246291,0.9271523178807947,0.8763693270735524,0.8643528878358416,0.8647008937422148
Original,train_70_test_30,RF_Gini,Random Forest,0.889267461669506,0.567947,1369,587,0.9572731497618218,0.9296875,0.8350877192982457,0.8798521256931608,0.8580060422960725,0.9403973509933775,0.8973143759873617,0.8885832508402614,0.8888361113641126
Original,train_70_test_30,RF_Entropy,Random Forest,0.8807495741056218,0.617227,1369,587,0.9574648541884513,0.9282868525896414,0.8175438596491228,0.8694029850746269,0.8452380952380952,0.9403973509933775,0.890282131661442,0.8798425583680345,0.8801448969472302
Original,train_70_test_30,RF_Log_Loss,Random Forest,0.8807495741056218,0.655725,1369,587,0.9574648541884513,0.9282868525896414,0.8175438596491228,0.8694029850746269,0.8452380952380952,0.9403973509933775,0.890282131661442,0.8798425583680345,0.8801448969472302
Original,train_70_test_30,RF_Large,Random Forest,0.8739352640545145,0.495378,1369,587,0.9511792726850237,0.8075801749271136,0.9719298245614035,0.8821656050955414,0.9672131147540983,0.7814569536423841,0.8644688644688645,0.8733172347822029,0.873060978742464
Original,train_70_test_30,RF_Small,Random Forest,0.868824531516184,0.107471,1369,587,0.9438654583478563,0.804093567251462,0.9649122807017544,0.8771929824561403,0.9591836734693877,0.7781456953642384,0.8592321755027422,0.8682125789794413,0.8679524991513256
Original,train_70_test_30,ExtraTrees,Random Forest,0.9011925042589438,0.794376,1369,587,0.9631927500871384,0.9315589353612167,0.8596491228070176,0.8941605839416058,0.8765432098765432,0.9403973509933775,0.9073482428115016,0.9007544133765537,0.9009453760688776
Original,train_70_test_30,ExtraTrees_Large,Random Forest,0.879045996592845,0.350631,1369,587,0.9474381317532241,0.8147058823529412,0.9719298245614035,0.8864,0.9676113360323887,0.7913907284768212,0.8706739526411658,0.8785369763205828,0.8783092567250972
Original,train_70_test_30,GradientBoosting,Ensemble,0.8977853492333902,0.732246,1369,587,0.9470837690252119,0.8299120234604106,0.9929824561403509,0.9041533546325878,0.991869918699187,0.8079470198675497,0.8905109489051095,0.8973321517688486,0.8971346041560998
Original,train_70_test_30,AdaBoost,Ensemble,0.8841567291311755,0.431842,1369,587,0.9373242709422562,0.8359133126934984,0.9473684210526315,0.8881578947368421,0.9431818181818182,0.8245033112582781,0.8798586572438163,0.8840082759903292,0.8838880996382155
Original,train_70_test_30,AdaBoost_DT,Decision Tree,0.8654173764906303,0.885119,1369,587,0.9522597885442082,0.90234375,0.8105263157894737,0.8539741219963032,0.8368580060422961,0.9172185430463576,0.8751974723538705,0.8645857971750868,0.8648931199656138
Original,train_65_test_35,SVM_Linear,Support Vector Machine,0.9007299270072993,0.304597,1271,685,0.9637762762762764,0.8610354223433242,0.948948948948949,0.9028571428571428,0.9465408805031447,0.8551136363636364,0.8985074626865671,0.9006823027718549,0.9006219787402922
Original,train_65_test_35,SVM_Polynomial,Support Vector Machine,0.872992700729927,0.474958,1271,685,0.9374488124488124,0.875,0.8618618618618619,0.8683812405446294,0.8711484593837535,0.8835227272727273,0.8772919605077574,0.8728366005261934,0.8729601798541492
Original,train_65_test_35,SVM_RBF,Support Vector Machine,0.9065693430656935,0.506284,1271,685,0.9487015424515424,0.8788732394366198,0.9369369369369369,0.9069767441860465,0.9363636363636364,0.8778409090909091,0.906158357771261,0.9065675509786537,0.9065562010940692
Original,train_65_test_35,SVM_Sigmoid,Support Vector Machine,0.9007299270072993,0.315534,1271,685,0.9634435571935572,0.8571428571428571,0.954954954954955,0.9034090909090909,0.9522292993630573,0.8494318181818182,0.8978978978978979,0.9006534944034944,0.9005770617996895
Original,train_65_test_35,SVM_RBF_Tuned,Support Vector Machine,0.9036496350364963,0.499647,1271,685,0.9476777914277913,0.8825214899713467,0.924924924924925,0.9032258064516129,0.9255952380952381,0.8835227272727273,0.9040697674418605,0.9036477869467368,0.9036594915152144
Original,train_65_test_35,SVM_Linear_Tuned,Support Vector Machine,0.9007299270072993,0.343782,1271,685,0.9637762762762764,0.8610354223433242,0.948948948948949,0.9028571428571428,0.9465408805031447,0.8551136363636364,0.8985074626865671,0.9006823027718549,0.9006219787402922
Original,train_65_test_35,NB_Multinomial,Naive Bayes,0.8934306569343066,0.023143,1271,685,0.9567550505050505,0.9193548387096774,0.8558558558558559,0.8864696734059098,0.872,0.9289772727272727,0.8995873452544704,0.8930285093301901,0.893210433246338
Original,train_65_test_35,NB_Bernoulli,Naive Bayes,0.8467153284671532,0.01596,1271,685,0.9460611179361178,0.7663551401869159,0.984984984984985,0.8620236530880421,0.980544747081712,0.7159090909090909,0.8275862068965517,0.8448049299922968,0.8443273303735829
Original,train_65_test_35,NB_Complement,Naive Bayes,0.8832116788321168,0.013929,1271,685,0.9567550505050505,0.8624641833810889,0.9039039039039038,0.8826979472140762,0.9047619047619048,0.8636363636363636,0.8837209302325582,0.8832094387233171,0.883223626079048
Original,train_65_test_35,NB_Multinomial_Tuned,Naive Bayes,0.8890510948905109,0.018869,1271,685,0.9515339202839203,0.9105431309904153,0.8558558558558559,0.8823529411764706,0.8709677419354839,0.9204545454545454,0.8950276243093923,0.8886902827429315,0.8888660630199573
Original,train_65_test_35,NB_Bernoulli_Tuned,Naive Bayes,0.8744525547445255,0.019495,1271,685,0.9384597665847665,0.8126582278481013,0.963963963963964,0.8818681318681318,0.9586206896551724,0.7897727272727273,0.8660436137071651,0.8739558727876484,0.873736408667168
Original,train_65_test_35,LR_L1,Logistic Regression,0.8773722627737226,0.022273,1271,685,0.9438216625716626,0.8151898734177215,0.9669669669669669,0.8846153846153846,0.9620689655172414,0.7926136363636364,0.8691588785046729,0.8768871315600287,0.8766727712563035
Original,train_65_test_35,LR_L2,Logistic Regression,0.8978102189781022,0.016846,1271,685,0.957591113841114,0.8583106267029973,0.9459459459459459,0.9,0.9433962264150944,0.8522727272727273,0.8955223880597015,0.8977611940298508,0.8976990957620655
Original,train_65_test_35,LR_ElasticNet,Logistic Regression,0.8846715328467153,0.029847,1271,685,0.9497679497679498,0.8342105263157895,0.9519519519519519,0.8892005610098177,0.9475409836065574,0.8210227272727273,0.8797564687975646,0.8844785149036911,0.8843475384423534
Original,train_65_test_35,LR_LBFGS,Logistic Regression,0.8978102189781022,0.042764,1271,685,0.9575911138411138,0.8583106267029973,0.9459459459459459,0.9,0.9433962264150944,0.8522727272727273,0.8955223880597015,0.8977611940298508,0.8976990957620655
Original,train_65_test_35,LR_SAG,Logistic Regression,0.8978102189781022,0.02187,1271,685,0.9575911138411138,0.8583106267029973,0.9459459459459459,0.9,0.9433962264150944,0.8522727272727273,0.8955223880597015,0.8977611940298508,0.8976990957620655
Original,train_65_test_35,LR_SAGA,Logistic Regression,0.8978102189781022,0.022795,1271,685,0.9575996450996451,0.8583106267029973,0.9459459459459459,0.9,0.9433962264150944,0.8522727272727273,0.8955223880597015,0.8977611940298508,0.8976990957620655
Original,train_65_test_35,DT_Gini,Decision Tree,0.8613138686131386,0.062686,1271,685,0.9262984575484576,0.916083916083916,0.7867867867867868,0.8465266558966075,0.8220551378446115,0.9318181818181818,0.8735019973368842,0.8600143266167458,0.8603884371914651
Original,train_65_test_35,DT_Entropy,Decision Tree,0.8613138686131386,0.072757,1271,685,0.9227963759213759,0.9131944444444444,0.7897897897897898,0.8470209339774557,0.8236775818639799,0.9289772727272727,0.8731642189586115,0.8600925764680336,0.8604551475736116
Original,train_65_test_35,DT_Log_Loss,Decision Tree,0.8613138686131386,0.079877,1271,685,0.9227963759213759,0.9131944444444444,0.7897897897897898,0.8470209339774557,0.8236775818639799,0.9289772727272727,0.8731642189586115,0.8600925764680336,0.8604551475736116
Original,train_65_test_35,DT_Gini_Pruned,Decision Tree,0.8817518248175182,0.04306,1271,685,0.8907615001365002,0.8073170731707318,0.993993993993994,0.8909825033647375,0.9927272727272727,0.7755681818181818,0.8708133971291866,0.880897950246962,0.8806182327152282
Original,train_65_test_35,DT_Entropy_Pruned,Decision Tree,0.8656934306569343,0.035989,1271,685,0.8875025593775595,0.7917675544794189,0.9819819819819819,0.8766756032171582,0.9779411764705882,0.7556818181818182,0.8525641025641025,0.8646198528906304,0.86428545981588
Original,train_65_test_35,DT_Best_First,Decision Tree,0.8613138686131386,0.057627,1271,685,0.9262984575484576,0.916083916083916,0.7867867867867868,0.8465266558966075,0.8220551378446115,0.9318181818181818,0.8735019973368842,0.8600143266167458,0.8603884371914651
Original,train_65_test_35,DT_Random_Split,Decision Tree,0.8744525547445255,0.083369,1271,685,0.9347529347529346,0.9303135888501742,0.8018018018018018,0.8612903225806452,0.8341708542713567,0.9431818181818182,0.8853333333333333,0.8733118279569893,0.8736452711718075
Original,train_65_test_35,RF_Gini,Random Forest,0.8890510948905109,0.504389,1271,685,0.9572925197925197,0.9240924092409241,0.8408408408408409,0.8805031446540881,0.8612565445026178,0.9346590909090909,0.896457765667575,0.8884804551608315,0.888701723627442
Original,train_65_test_35,RF_Entropy,Random Forest,0.8905109489051095,0.51252,1271,685,0.9551426426426427,0.9328859060402684,0.8348348348348348,0.8811410459587956,0.8578811369509044,0.9431818181818182,0.8985115020297699,0.8898262739942828,0.8900671781295737
Original,train_65_test_35,RF_Log_Loss,Random Forest,0.8905109489051095,0.493064,1271,685,0.9551426426426427,0.9328859060402684,0.8348348348348348,0.8811410459587956,0.8578811369509044,0.9431818181818182,0.8985115020297699,0.8898262739942828,0.8900671781295737
Original,train_65_test_35,RF_Large,Random Forest,0.8802919708029197,0.40096,1271,685,0.9546947515697516,0.8129675810473815,0.978978978978979,0.888283378746594,0.9753521126760564,0.7869318181818182,0.8710691823899371,0.8796762805682656,0.8794375435385017
Original,train_65_test_35,RF_Small,Random Forest,0.8759124087591241,0.112528,1271,685,0.9472299003549004,0.8212435233160622,0.9519519519519519,0.8817802503477051,0.9464882943143813,0.8039772727272727,0.869431643625192,0.8756059469864486,0.8754346889370123
Original,train_65_test_35,ExtraTrees,Random Forest,0.8963503649635036,0.676624,1271,685,0.9615709459459458,0.9253246753246753,0.8558558558558559,0.8892355694227769,0.8726790450928382,0.9346590909090909,0.9026063100137174,0.8959209397182472,0.8961063733468806
Original,train_65_test_35,ExtraTrees_Large,Random Forest,0.8686131386861314,0.329771,1271,685,0.9462146805896806,0.8075949367088607,0.9579579579579579,0.8763736263736264,0.9517241379310345,0.7840909090909091,0.8598130841121495,0.868093355242888,0.8678636834888966
Original,train_65_test_35,GradientBoosting,Ensemble,0.9007299270072993,0.622562,1271,685,0.948603432978433,0.8406169665809768,0.9819819819819819,0.9058171745152355,0.9797297297297297,0.8238636363636364,0.8950617283950617,0.9004394514551486,0.9002902883337739
Original,train_65_test_35,AdaBoost,Ensemble,0.8934306569343066,0.415062,1271,685,0.9350600600600601,0.8421052631578947,0.960960960960961,0.8976157082748948,0.9573770491803278,0.8295454545454546,0.8888888888888888,0.8932522985818918,0.8931312696998961
Original,train_65_test_35,AdaBoost_DT,Decision Tree,0.8671532846715329,0.761879,1271,685,0.9442055692055692,0.8980263157894737,0.8198198198198198,0.8571428571428571,0.84251968503937,0.9119318181818182,0.8758526603001364,0.8664977587214968,0.8667572377433861
Reversed,train_25_test_75_REVERSED,SVM_Linear,Support Vector Machine,0.8847989093387867,0.070128,489,1467,0.953125174385512,0.8358024691358025,0.9495091164095372,0.8890347997373604,0.9452054794520548,0.8236074270557029,0.8802267895109851,0.8846307946241727,0.8845077106366875
Reversed,train_25_test_75_REVERSED,SVM_Polynomial,Support Vector Machine,0.8500340831629175,0.08412,489,1467,0.9221915097042049,0.8619676945668135,0.82328190743338,0.8421807747489239,0.8396946564885496,0.8753315649867374,0.8571428571428571,0.8496618159458905,0.8498708975335358
Reversed,train_25_test_75_REVERSED,SVM_RBF,Support Vector Machine,0.880027266530334,0.072141,489,1467,0.9276723300880577,0.8584779706275033,0.9018232819074333,0.8796169630642955,0.9025069637883009,0.8594164456233422,0.8804347826086957,0.8800258728364956,0.8800373011259708
Reversed,train_25_test_75_REVERSED,SVM_Sigmoid,Support Vector Machine,0.874573960463531,0.064856,489,1467,0.9523588081889577,0.8190591073582629,0.9523141654978962,0.880674448767834,0.9467084639498433,0.8010610079575596,0.867816091954023,0.8742452703609285,0.8740655864381723
Reversed,train_25_test_75_REVERSED,SVM_RBF_Tuned,Support Vector Machine,0.8895705521472392,0.092383,489,1467,0.9285484429001379,0.8748299319727891,0.9018232819074333,0.888121546961326,0.9043715846994536,0.8779840848806366,0.8909825033647375,0.8895520251630318,0.8895920044447425
Reversed,train_25_test_75_REVERSED,SVM_Linear_Tuned,Support Vector Machine,0.8847989093387867,0.077098,489,1467,0.953125174385512,0.8358024691358025,0.9495091164095372,0.8890347997373604,0.9452054794520548,0.8236074270557029,0.8802267895109851,0.8846307946241727,0.8845077106366875
Reversed,train_25_test_75_REVERSED,NB_Multinomial,Naive Bayes,0.8623040218132243,0.02135,489,1467,0.9455554480824104,0.8924731182795699,0.814866760168303,0.8519061583577713,0.8382352941176471,0.9071618037135278,0.8713375796178344,0.8616218689878028,0.8618934055493783
Reversed,train_25_test_75_REVERSED,NB_Bernoulli,Naive Bayes,0.8057259713701431,0.020103,489,1467,0.9421635336178065,0.7165991902834008,0.9929873772791024,0.8324514991181657,0.9895615866388309,0.6286472148541115,0.7688564476885644,0.8006539734033651,0.7997652899989296
Reversed,train_25_test_75_REVERSED,NB_Complement,Naive Bayes,0.8609406952965235,0.0,489,1467,0.9455554480824104,0.8344283837056504,0.8906030855539971,0.8616010854816825,0.8895184135977338,0.8328912466843501,0.8602739726027397,0.8609375290422111,0.8609189838383813
Reversed,train_25_test_75_REVERSED,NB_Multinomial_Tuned,Naive Bayes,0.869120654396728,0.007986,489,1467,0.9438478651493112,0.8859259259259259,0.8387096774193549,0.861671469740634,0.8547979797979798,0.8978779840848806,0.8758085381630013,0.8687400039518176,0.8689375567143661
Reversed,train_25_test_75_REVERSED,NB_Bernoulli_Tuned,Naive Bayes,0.8377641445126107,0.004937,489,1467,0.9361116588107932,0.7624309392265194,0.967741935483871,0.8529048207663782,0.9590747330960854,0.7148541114058355,0.8191489361702128,0.8360268784682955,0.8355551704695079
Reversed,train_25_test_75_REVERSED,LR_L1,Logistic Regression,0.8425357873210634,0.008984,489,1467,0.9007034944066428,0.7660044150110376,0.9733520336605891,0.8573193329215565,0.966131907308378,0.7188328912466844,0.8243346007604563,0.8408269668410064,0.8403660350009909
Reversed,train_25_test_75_REVERSED,LR_L2,Logistic Regression,0.8779822767552828,0.010949,489,1467,0.946468763137042,0.8396946564885496,0.9256661991584852,0.8805870580386924,0.922173274596182,0.8328912466843501,0.8752613240418119,0.8779241910402522,0.877849768717869
Reversed,train_25_test_75_REVERSED,LR_ElasticNet,Logistic Regression,0.8725289706884799,0.009992,489,1467,0.9273728520355208,0.8145933014354066,0.9551192145862553,0.8792769528728211,0.9492868462757528,0.7944297082228117,0.8649819494584837,0.8721294511656524,0.8719296914042387
Reversed,train_25_test_75_REVERSED,LR_LBFGS,Logistic Regression,0.8779822767552828,0.017698,489,1467,0.9465599086312924,0.8396946564885496,0.9256661991584852,0.8805870580386924,0.922173274596182,0.8328912466843501,0.8752613240418119,0.8779241910402522,0.877849768717869
Reversed,train_25_test_75_REVERSED,LR_SAG,Logistic Regression,0.8773006134969326,0.0,489,1467,0.9465096856038483,0.8386277001270648,0.9256661991584852,0.88,0.9220588235294118,0.8315649867374005,0.8744769874476988,0.8772384937238493,0.877161314611837
Reversed,train_25_test_75_REVERSED,LR_SAGA,Logistic Regression,0.8779822767552828,0.011267,489,1467,0.9462939125970514,0.8396946564885496,0.9256661991584852,0.8805870580386924,0.922173274596182,0.8328912466843501,0.8752613240418119,0.8779241910402522,0.877849768717869
Reversed,train_25_test_75_REVERSED,DT_Gini,Decision Tree,0.8214042263122018,0.022977,489,1467,0.9141846570511272,0.8921739130434783,0.7194950911640954,0.796583850931677,0.7757847533632287,0.9177718832891246,0.8408262454434994,0.8187050481875882,0.8193232956909915
Reversed,train_25_test_75_REVERSED,DT_Entropy,Decision Tree,0.8173142467620995,0.02528,489,1467,0.9051761340173586,0.8856152512998267,0.7166900420757363,0.7922480620155039,0.7730337078651686,0.9124668435013262,0.8369829683698297,0.8146155151926668,0.8152406451042303
Reversed,train_25_test_75_REVERSED,DT_Log_Loss,Decision Tree,0.8173142467620995,0.027984,489,1467,0.9051761340173586,0.8856152512998267,0.7166900420757363,0.7922480620155039,0.7730337078651686,0.9124668435013262,0.8369829683698297,0.8146155151926668,0.8152406451042303
Reversed,train_25_test_75_REVERSED,DT_Gini_Pruned,Decision Tree,0.858214042263122,0.018774,489,1467,0.8579032444075728,0.7840269966254219,0.9775596072931276,0.8701622971285893,0.972318339100346,0.7453580901856764,0.8438438438438438,0.8570030704862166,0.8566352938724896
Reversed,train_25_test_75_REVERSED,DT_Entropy_Pruned,Decision Tree,0.8289025221540559,0.018963,489,1467,0.8560012797571438,0.746268656716418,0.9817671809256662,0.8479709267110842,0.9754253308128544,0.6843501326259946,0.8043647700701481,0.8261678483906161,0.8255584917368062
Reversed,train_25_test_75_REVERSED,DT_Best_First,Decision Tree,0.8214042263122018,0.0235,489,1467,0.9141846570511272,0.8921739130434783,0.7194950911640954,0.796583850931677,0.7757847533632287,0.9177718832891246,0.8408262454434994,0.8187050481875882,0.8193232956909915
Reversed,train_25_test_75_REVERSED,DT_Random_Split,Decision Tree,0.8159509202453987,0.017964,489,1467,0.9029663208098184,0.8773424190800682,0.7223001402524544,0.7923076923076923,0.775,0.9045092838196287,0.8347613219094248,0.8135345071085585,0.8141277582379624
Reversed,train_25_test_75_REVERSED,RF_Gini,Random Forest,0.8084526244035446,0.314842,489,1467,0.9001249995349719,0.8985239852398524,0.6830294530154277,0.7760956175298804,0.7556756756756757,0.9270557029177718,0.8326384752829065,0.8043670464063934,0.805157181773767
Reversed,train_25_test_75_REVERSED,RF_Entropy,Random Forest,0.8084526244035446,0.237189,489,1467,0.9033932165430933,0.9060150375939849,0.6760168302945302,0.7742971887550201,0.7529411764705882,0.9336870026525199,0.8336293664890467,0.8039632776220333,0.804792391216817
Reversed,train_25_test_75_REVERSED,RF_Log_Loss,Random Forest,0.8084526244035446,0.243431,489,1467,0.9033932165430933,0.9060150375939849,0.6760168302945302,0.7742971887550201,0.7529411764705882,0.9336870026525199,0.8336293664890467,0.8039632776220333,0.804792391216817
Reversed,train_25_test_75_REVERSED,RF_Large,Random Forest,0.8616223585548739,0.293418,489,1467,0.9428331739837277,0.791095890410959,0.9719495091164095,0.8722466960352423,0.9661590524534687,0.7572944297082228,0.8490706319702602,0.8606586640027513,0.8603347994401528
Reversed,train_25_test_75_REVERSED,RF_Small,Random Forest,0.8568507157464212,0.071284,489,1467,0.9302495154407907,0.7848244620611552,0.9719495091164095,0.868421052631579,0.9657534246575342,0.7480106100795756,0.8430493273542601,0.8557351899929195,0.8553806430480081
Reversed,train_25_test_75_REVERSED,ExtraTrees,Random Forest,0.8486707566462167,0.255983,489,1467,0.9211172949505397,0.9139966273187183,0.7601683029453016,0.8300153139356815,0.8043478260869565,0.9323607427055703,0.8636363636363636,0.8468258387860226,0.8472956625889292
Reversed,train_25_test_75_REVERSED,ExtraTrees_Large,Random Forest,0.8629856850715747,0.233306,489,1467,0.9451601742553041,0.7976744186046512,0.9621318373071529,0.8722186904005086,0.9555189456342669,0.7692307692307693,0.852314474650992,0.8622665825257503,0.8619884390882145
Reversed,train_25_test_75_REVERSED,GradientBoosting,Ensemble,0.8813905930470347,0.295825,489,1467,0.9325049013954559,0.8122827346465817,0.9831697054698457,0.8895939086294417,0.9801324503311258,0.7851458885941645,0.8718703976435935,0.8807321531365175,0.8804844830784332
Reversed,train_25_test_75_REVERSED,AdaBoost,Ensemble,0.8575323790047716,0.278965,489,1467,0.9162484514566539,0.7916666666666666,0.9593267882187938,0.8674698795180723,0.9519071310116086,0.7612732095490716,0.8459837877671333,0.8567268336426028,0.8564265849167035
Reversed,train_25_test_75_REVERSED,AdaBoost_DT,Decision Tree,0.8548057259713702,0.47017,489,1467,0.9409749219682962,0.8918495297805643,0.7980364656381487,0.8423390081421169,0.8262967430639324,0.9084880636604774,0.8654453569172458,0.8538921825296814,0.8542150728840714
Reversed,train_30_test_70_REVERSED,SVM_Linear,Support Vector Machine,0.8905109489051095,0.105607,586,1370,0.9574396840021839,0.844,0.9504504504504504,0.8940677966101694,0.9467741935483871,0.8338068181818182,0.8867069486404834,0.8903873726253264,0.8902852878724621
Reversed,train_30_test_70_REVERSED,SVM_Polynomial,Support Vector Machine,0.8467153284671532,0.114819,586,1370,0.9277988926426426,0.8713355048859935,0.8033033033033034,0.8359375,0.8267195767195767,0.8877840909090909,0.8561643835616438,0.846050941780822,0.8463314606039397
Reversed,train_30_test_70_REVERSED,SVM_RBF,Support Vector Machine,0.8897810218978102,0.102122,586,1370,0.9347998566748567,0.8694404591104734,0.9099099099099099,0.8892149669845928,0.9108469539375929,0.8707386363636364,0.8903413217138707,0.8897781443492317,0.889793765327229
Reversed,train_30_test_70_REVERSED,SVM_Sigmoid,Support Vector Machine,0.8861313868613139,0.083393,586,1370,0.9574599457411958,0.8337696335078534,0.9564564564564565,0.8909090909090909,0.9521452145214522,0.8196022727272727,0.8809160305343512,0.8859125607217211,0.8857739708333123
Reversed,train_30_test_70_REVERSED,SVM_RBF_Tuned,Support Vector Machine,0.891970802919708,0.125672,586,1370,0.9354162401037401,0.8775510204081632,0.9039039039039038,0.8905325443786982,0.9064327485380117,0.8806818181818182,0.8933717579250721,0.8919521511518851,0.8919915271061779
Reversed,train_30_test_70_REVERSED,SVM_Linear_Tuned,Support Vector Machine,0.8905109489051095,0.101092,586,1370,0.9574396840021839,0.844,0.9504504504504504,0.8940677966101694,0.9467741935483871,0.8338068181818182,0.8867069486404834,0.8903873726253264,0.8902852878724621
Reversed,train_30_test_70_REVERSED,NB_Multinomial,Naive Bayes,0.872992700729927,0.010873,586,1370,0.9488103159978158,0.9006514657980456,0.8303303303303303,0.8640625,0.8505291005291006,0.9133522727272727,0.8808219178082192,0.8724422089041095,0.8726746387861214
Reversed,train_30_test_70_REVERSED,NB_Bernoulli,Naive Bayes,0.8262773722627738,0.014616,586,1370,0.9429557398307398,0.7388392857142857,0.993993993993994,0.8476312419974392,0.9915611814345991,0.6676136363636364,0.797962648556876,0.8227969452771576,0.8221081107695878
Reversed,train_30_test_70_REVERSED,NB_Complement,Naive Bayes,0.8693430656934307,0.0,586,1370,0.9488103159978158,0.8463726884779517,0.8933933933933934,0.8692476260043828,0.8935532233883059,0.8465909090909091,0.8694383661560905,0.8693429960802366,0.8693456413816106
Reversed,train_30_test_70_REVERSED,NB_Multinomial_Tuned,Naive Bayes,0.8788321167883212,0.009315,586,1370,0.9476415335790336,0.8943217665615142,0.8513513513513513,0.8723076923076923,0.8654891304347826,0.9048295454545454,0.8847222222222222,0.8785149572649573,0.8786871295776404
Reversed,train_30_test_70_REVERSED,NB_Bernoulli_Tuned,Naive Bayes,0.8481751824817518,0.002869,586,1370,0.9371971403221403,0.7752403846153846,0.9684684684684685,0.8611481975967957,0.9609665427509294,0.734375,0.8325281803542673,0.8468381889755315,0.8464412690283725
Reversed,train_30_test_70_REVERSED,LR_L1,Logistic Regression,0.8496350364963504,0.00866,586,1370,0.9153005988943489,0.7744630071599046,0.9744744744744744,0.863031914893617,0.9680451127819549,0.7315340909090909,0.8333333333333334,0.8481826241134751,0.8477707459750479
Reversed,train_30_test_70_REVERSED,LR_L2,Logistic Regression,0.8846715328467153,0.0,586,1370,0.9488828316953316,0.8441734417344173,0.9354354354354354,0.8874643874643875,0.9319620253164557,0.8366477272727273,0.8817365269461078,0.8846004572052477,0.8845210197236073
Reversed,train_30_test_70_REVERSED,LR_ElasticNet,Logistic Regression,0.8686131386861314,0.025944,586,1370,0.9312817789380289,0.8099489795918368,0.9534534534534534,0.8758620689655172,0.947098976109215,0.7883522727272727,0.8604651162790697,0.8681635926222935,0.8679500582419705
Reversed,train_30_test_70_REVERSED,LR_LBFGS,Logistic Regression,0.8846715328467153,0.013647,586,1370,0.9489105582855583,0.8432432432432433,0.9369369369369369,0.887624466571835,0.9333333333333333,0.8352272727272727,0.881559220389805,0.88459184348082,0.8845077269279306
Reversed,train_30_test_70_REVERSED,LR_SAG,Logistic Regression,0.8854014598540146,0.004015,586,1370,0.9489105582855583,0.8434547908232118,0.9384384384384384,0.8884150675195451,0.9348171701112877,0.8352272727272727,0.8822205551387847,0.8853178113291649,0.8852319020333734
Reversed,train_30_test_70_REVERSED,LR_SAGA,Logistic Regression,0.8854014598540146,0.011063,586,1370,0.9489873396123396,0.8434547908232118,0.9384384384384384,0.8884150675195451,0.9348171701112877,0.8352272727272727,0.8822205551387847,0.8853178113291649,0.8852319020333734
Reversed,train_30_test_70_REVERSED,DT_Gini,Decision Tree,0.8306569343065694,0.029084,586,1370,0.9205355924105925,0.8959854014598541,0.7372372372372372,0.8088962108731467,0.7871046228710462,0.9190340909090909,0.8479685452162516,0.8284323780446992,0.8289742571341291
Reversed,train_30_test_70_REVERSED,DT_Entropy,Decision Tree,0.8262773722627738,0.029088,586,1370,0.9043134043134042,0.8614864864864865,0.7657657657657657,0.8108108108108109,0.7994858611825193,0.8835227272727273,0.8394062078272605,0.8251085093190357,0.8255050878178039
Reversed,train_30_test_70_REVERSED,DT_Log_Loss,Decision Tree,0.8262773722627738,0.018223,586,1370,0.9043134043134042,0.8614864864864865,0.7657657657657657,0.8108108108108109,0.7994858611825193,0.8835227272727273,0.8394062078272605,0.8251085093190357,0.8255050878178039
Reversed,train_30_test_70_REVERSED,DT_Gini_Pruned,Decision Tree,0.8759124087591241,0.025902,586,1370,0.8730548730548731,0.803921568627451,0.984984984984985,0.8852901484480432,0.9819494584837545,0.7727272727272727,0.8648648648648649,0.875077506656454,0.8747942363001909
Reversed,train_30_test_70_REVERSED,DT_Entropy_Pruned,Decision Tree,0.8423357664233576,0.022502,586,1370,0.8744337377149878,0.7616279069767442,0.9834834834834835,0.8584534731323722,0.9784313725490196,0.7088068181818182,0.8220757825370676,0.8402646278347199,0.8397601197169747
Reversed,train_30_test_70_REVERSED,DT_Best_First,Decision Tree,0.8306569343065694,0.024967,586,1370,0.9205355924105925,0.8959854014598541,0.7372372372372372,0.8088962108731467,0.7871046228710462,0.9190340909090909,0.8479685452162516,0.8284323780446992,0.8289742571341291
Reversed,train_30_test_70_REVERSED,DT_Random_Split,Decision Tree,0.8218978102189781,0.022355,586,1370,0.9082943028255529,0.8936567164179104,0.7192192192192193,0.7970049916805324,0.7757793764988009,0.9190340909090909,0.8413524057217165,0.8191786987011245,0.8197937358301629
Reversed,train_30_test_70_REVERSED,RF_Gini,Random Forest,0.8335766423357664,0.332043,586,1370,0.9191993840431341,0.9025735294117647,0.7372372372372372,0.8115702479338843,0.788135593220339,0.9247159090909091,0.8509803921568627,0.8312753200453735,0.8318218840893419
Reversed,train_30_test_70_REVERSED,RF_Entropy,Random Forest,0.8328467153284671,0.256431,586,1370,0.9189818369505869,0.9038817005545287,0.7342342342342343,0.8102734051367025,0.7864897466827503,0.9261363636363636,0.8506196999347684,0.8304465525357354,0.8310060996898693
Reversed,train_30_test_70_REVERSED,RF_Log_Loss,Random Forest,0.8328467153284671,0.301991,586,1370,0.9189818369505869,0.9038817005545287,0.7342342342342343,0.8102734051367025,0.7864897466827503,0.9261363636363636,0.8506196999347684,0.8304465525357354,0.8310060996898693
Reversed,train_30_test_70_REVERSED,RF_Large,Random Forest,0.8678832116788321,0.289289,586,1370,0.9431828845891347,0.8019925280199253,0.9669669669669669,0.876786929884275,0.9611992945326279,0.7741477272727273,0.8575924468922108,0.8671896883882428,0.8669234875292289
Reversed,train_30_test_70_REVERSED,RF_Small,Random Forest,0.8583941605839416,0.064888,586,1370,0.9330573471198471,0.7920792079207921,0.960960960960961,0.8683853459972863,0.9537366548042705,0.7613636363636364,0.8467614533965245,0.8575733996969055,0.857273506295873
Reversed,train_30_test_70_REVERSED,ExtraTrees,Random Forest,0.845985401459854,0.332318,586,1370,0.9288951593639094,0.8956521739130435,0.7732732732732732,0.8299758259468171,0.810062893081761,0.9147727272727273,0.8592394929953302,0.8446076594710736,0.8450135059483888
Reversed,train_30_test_70_REVERSED,ExtraTrees_Large,Random Forest,0.8722627737226277,0.226227,586,1370,0.9411225856538357,0.8080301129234629,0.9669669669669669,0.8803827751196173,0.9616055846422339,0.7826704545454546,0.8629600626468285,0.8716714188832229,0.8714297900241113
Reversed,train_30_test_70_REVERSED,GradientBoosting,Ensemble,0.8824817518248175,0.370832,586,1370,0.9383339305214305,0.8136645962732919,0.9834834834834835,0.8905506458191706,0.9805309734513274,0.7869318181818182,0.8731284475965327,0.8818395467078517,0.8815979249806764
Reversed,train_30_test_70_REVERSED,AdaBoost,Ensemble,0.8737226277372263,0.24281,586,1370,0.9254357340294841,0.8116308470290771,0.963963963963964,0.8812628689087165,0.9585492227979274,0.7883522727272727,0.8651597817614964,0.8732113253351065,0.8729879978491233
Reversed,train_30_test_70_REVERSED,AdaBoost_DT,Decision Tree,0.8452554744525548,0.431648,586,1370,0.9453082343707343,0.8913793103448275,0.7762762762762763,0.8298555377207063,0.8113924050632911,0.9105113636363636,0.85809906291834,0.8439773003195232,0.8443689988441617
Reversed,train_35_test_65_REVERSED,SVM_Linear,Support Vector Machine,0.8962264150943396,0.113852,684,1272,0.9617638035291907,0.8521739130434782,0.9514563106796117,0.8990825688073395,0.9484536082474226,0.8440366972477065,0.8932038834951457,0.8961432261512425,0.8960600372081455
Reversed,train_35_test_65_REVERSED,SVM_Polynomial,Support Vector Machine,0.8592767295597484,0.137816,684,1272,0.9358824955711924,0.8830715532286213,0.8187702265372169,0.8497061293031066,0.8397711015736766,0.8975535168195719,0.8677014042867701,0.8587037667949384,0.8589584169126319
Reversed,train_35_test_65_REVERSED,SVM_RBF,Support Vector Machine,0.8954402515723271,0.146745,684,1272,0.9396618271428996,0.8783151326053042,0.9110032362459547,0.8943606036536934,0.9128367670364501,0.8807339449541285,0.8964980544747082,0.8954293290642008,0.8954595760097813
Reversed,train_35_test_65_REVERSED,SVM_Sigmoid,Support Vector Machine,0.8946540880503144,0.123403,684,1272,0.9616871035103867,0.8447293447293447,0.959546925566343,0.8984848484848484,0.956140350877193,0.8333333333333334,0.8905228758169934,0.8945038621509209,0.8943911927263757
Reversed,train_35_test_65_REVERSED,SVM_RBF_Tuned,Support Vector Machine,0.8970125786163522,0.171334,684,1272,0.9389344140613403,0.8822605965463108,0.9093851132686084,0.895617529880478,0.9118110236220472,0.8853211009174312,0.8983708301008534,0.8969941799906658,0.897033141786237
Reversed,train_35_test_65_REVERSED,SVM_Linear_Tuned,Support Vector Machine,0.8962264150943396,0.124466,684,1272,0.9617638035291907,0.8521739130434782,0.9514563106796117,0.8990825688073395,0.9484536082474226,0.8440366972477065,0.8932038834951457,0.8961432261512425,0.8960600372081455
Reversed,train_35_test_65_REVERSED,NB_Multinomial,Naive Bayes,0.8797169811320755,0.02435,684,1272,0.9552393535425512,0.91005291005291,0.8349514563106796,0.8708860759493671,0.8553191489361702,0.9220183486238532,0.8874172185430463,0.8791516472462066,0.8793855785093248
Reversed,train_35_test_65_REVERSED,NB_Bernoulli,Naive Bayes,0.8301886792452831,0.011567,684,1272,0.9495239650445849,0.7439320388349514,0.9919093851132686,0.8502080443828016,0.9888392857142857,0.6773700305810397,0.8039927404718693,0.8271003924273355,0.8264464022776525
Reversed,train_35_test_65_REVERSED,NB_Complement,Naive Bayes,0.8781446540880503,0.012167,684,1272,0.9552393535425512,0.8545176110260337,0.9029126213592233,0.8780487804878049,0.9030694668820679,0.8547400611620795,0.8782403770620582,0.8781445787749316,0.8781472900472086
Reversed,train_35_test_65_REVERSED,NB_Multinomial_Tuned,Naive Bayes,0.8773584905660378,0.002376,684,1272,0.9533564917906238,0.9010416666666666,0.8398058252427184,0.8693467336683417,0.8577586206896551,0.9128440366972477,0.8844444444444445,0.8768955890563931,0.8771092359069984
Reversed,train_35_test_65_REVERSED,NB_Bernoulli_Tuned,Naive Bayes,0.8529874213836478,0.002589,684,1272,0.9440559959621151,0.7816993464052288,0.9676375404530745,0.8647866955892987,0.960552268244576,0.7446483180428135,0.8389319552110249,0.8518593254001618,0.8514934564325448
Reversed,train_35_test_65_REVERSED,LR_L1,Logistic Regression,0.8498427672955975,0.020663,684,1272,0.9206575418386231,0.7726692209450831,0.9789644012944984,0.8636688079942898,0.9734151329243353,0.72782874617737,0.8328958880139983,0.848282348004144,0.8478468821553663
Reversed,train_35_test_65_REVERSED,LR_L2,Logistic Regression,0.8922955974842768,0.0,684,1272,0.9534678305275972,0.8531571218795888,0.9401294498381877,0.8945342571208622,0.937394247038917,0.8470948012232415,0.8899598393574297,0.892247048239146,0.8921823159123049
Reversed,train_35_test_65_REVERSED,LR_ElasticNet,Logistic Regression,0.8765723270440252,0.058822,684,1272,0.9397979078214225,0.8196948682385575,0.9563106796116505,0.8827483196415236,0.9509981851179673,0.8012232415902141,0.8697095435684647,0.8762289316049942,0.8760444206228282
Reversed,train_35_test_65_REVERSED,LR_LBFGS,Logistic Regression,0.8930817610062893,0.022874,684,1272,0.9535148402165414,0.8533724340175953,0.941747572815534,0.8953846153846153,0.9389830508474576,0.8470948012232415,0.8906752411575563,0.8930299282710858,0.8929632861829669
Reversed,train_35_test_65_REVERSED,LR_SAG,Logistic Regression,0.8922955974842768,0.017925,684,1272,0.9534925724691468,0.8521229868228404,0.941747572815534,0.8946963873943121,0.9388794567062818,0.845565749235474,0.8897827835880934,0.8922395854912027,0.8921700533618696
Reversed,train_35_test_65_REVERSED,LR_SAGA,Logistic Regression,0.8922955974842768,0.001811,684,1272,0.953547004740556,0.8531571218795888,0.9401294498381877,0.8945342571208622,0.937394247038917,0.8470948012232415,0.8899598393574297,0.892247048239146,0.8921823159123049
Reversed,train_35_test_65_REVERSED,DT_Gini,Decision Tree,0.8459119496855346,0.032715,684,1272,0.9242624427224053,0.8892988929889298,0.7799352750809061,0.8310344827586207,0.8136986301369863,0.908256880733945,0.8583815028901735,0.844707992824397,0.8450949789583342
Reversed,train_35_test_65_REVERSED,DT_Entropy,Decision Tree,0.8325471698113207,0.033966,684,1272,0.9078894629019328,0.867513611615245,0.7734627831715211,0.8177929854576561,0.8058252427184466,0.8883792048929664,0.8450909090909091,0.8314419472742827,0.8318282386464513
Reversed,train_35_test_65_REVERSED,DT_Log_Loss,Decision Tree,0.8325471698113207,0.02322,684,1272,0.9078894629019328,0.867513611615245,0.7734627831715211,0.8177929854576561,0.8058252427184466,0.8883792048929664,0.8450909090909091,0.8314419472742827,0.8318282386464513
Reversed,train_35_test_65_REVERSED,DT_Gini_Pruned,Decision Tree,0.8537735849056604,0.018067,684,1272,0.8734251754203656,0.7755102040816326,0.9838187702265372,0.8673323823109843,0.9795081967213115,0.7308868501529052,0.8371278458844134,0.8522301140976989,0.8518026914124173
Reversed,train_35_test_65_REVERSED,DT_Entropy_Pruned,Decision Tree,0.845125786163522,0.023296,684,1272,0.874453203091753,0.762796504369538,0.988673139158576,0.8611698379140239,0.9851380042462845,0.709480122324159,0.8248888888888889,0.8430293634014564,0.8425159537454403
Reversed,train_35_test_65_REVERSED,DT_Best_First,Decision Tree,0.8459119496855346,0.022969,684,1272,0.9242624427224053,0.8892988929889298,0.7799352750809061,0.8310344827586207,0.8136986301369863,0.908256880733945,0.8583815028901735,0.844707992824397,0.8450949789583342
Reversed,train_35_test_65_REVERSED,DT_Random_Split,Decision Tree,0.8482704402515723,0.033225,684,1272,0.9242104846451511,0.9001883239171374,0.7734627831715211,0.8320278503046127,0.8110661268556005,0.918960244648318,0.8616487455197133,0.8468382979121629,0.8472574615236974
Reversed,train_35_test_65_REVERSED,RF_Gini,Random Forest,0.85062893081761,0.295358,684,1272,0.9272042595726573,0.9147286821705426,0.7637540453074434,0.8324514991181657,0.8068783068783069,0.9327217125382263,0.8652482269503546,0.8488498630342602,0.8493139676733948
Reversed,train_35_test_65_REVERSED,RF_Entropy,Random Forest,0.8427672955974843,0.321762,684,1272,0.9250814009876982,0.908203125,0.7524271844660194,0.8230088495575221,0.7986842105263158,0.9281345565749235,0.8585572842998586,0.8407830669286903,0.8412861108165537
Reversed,train_35_test_65_REVERSED,RF_Log_Loss,Random Forest,0.8427672955974843,0.28244,684,1272,0.9250814009876982,0.908203125,0.7524271844660194,0.8230088495575221,0.7986842105263158,0.9281345565749235,0.8585572842998586,0.8407830669286903,0.8412861108165537
Reversed,train_35_test_65_REVERSED,RF_Large,Random Forest,0.875,0.290423,684,1272,0.9499606603129361,0.8113975576662144,0.9676375404530745,0.8826568265682657,0.9626168224299065,0.7874617737003058,0.8662741799831791,0.8744655032757225,0.8742336733712164
Reversed,train_35_test_65_REVERSED,RF_Small,Random Forest,0.8742138364779874,0.079271,684,1272,0.9452275268944904,0.8061497326203209,0.9757281553398058,0.8828696925329429,0.9713740458015268,0.7782874617737003,0.8641765704584041,0.8735231314956735,0.8732586061832979
Reversed,train_35_test_65_REVERSED,ExtraTrees,Random Forest,0.8694968553459119,0.346005,684,1272,0.9334639707847153,0.9124087591240876,0.8090614886731392,0.8576329331046312,0.8370165745856354,0.926605504587156,0.8795355587808418,0.8685842459427364,0.8688941887589094
Reversed,train_35_test_65_REVERSED,ExtraTrees_Large,Random Forest,0.8765723270440252,0.249743,684,1272,0.9459858674029867,0.8188105117565698,0.9579288025889967,0.8829231916480239,0.9526411657559198,0.7996941896024465,0.8694929343308395,0.8762080629894318,0.8760180121783395
Reversed,train_35_test_65_REVERSED,GradientBoosting,Ensemble,0.8828616352201258,0.3896,684,1272,0.9350944647328366,0.8105960264900662,0.9902912621359223,0.8914785142024764,0.988394584139265,0.7813455657492355,0.8727583262169086,0.8821184202096926,0.881853511889142
Reversed,train_35_test_65_REVERSED,AdaBoost,Ensemble,0.8781446540880503,0.260594,684,1272,0.9218451550330057,0.8141112618724559,0.970873786407767,0.8856088560885609,0.9663551401869159,0.790519877675841,0.8696383515559294,0.8776236038222451,0.8773976061165948
Reversed,train_35_test_65_REVERSED,AdaBoost_DT,Decision Tree,0.8608490566037735,0.48064,684,1272,0.9512311590115101,0.8795180722891566,0.8268608414239482,0.8523769808173478,0.8451519536903039,0.8929663608562691,0.8684014869888476,0.8603892339030976,0.8606159957828831
