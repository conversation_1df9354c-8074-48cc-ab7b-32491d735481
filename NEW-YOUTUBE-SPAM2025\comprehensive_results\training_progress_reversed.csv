split,algorithm,accuracy,training_time,status
train_25_test_75_REVERSED,SVM_Linear,0.8847989093387867,0.070128,success
train_25_test_75_REVERSED,SVM_Polynomial,0.8500340831629175,0.08412,success
train_25_test_75_REVERSED,SVM_RBF,0.880027266530334,0.072141,success
train_25_test_75_REVERSED,SVM_<PERSON><PERSON><PERSON><PERSON>,0.874573960463531,0.064856,success
train_25_test_75_REVERSED,SVM_RBF_Tuned,0.8895705521472392,0.092383,success
train_25_test_75_REVERSED,SVM_Linear_Tuned,0.8847989093387867,0.077098,success
train_25_test_75_REVERSED,NB_Gaussian,0.0,0.0,"error: Sparse data was passed for X, but dense data is required. Use '.toarray()' to convert to a dense numpy array."
train_25_test_75_REVERSED,NB_Multinomial,0.8623040218132243,0.02135,success
train_25_test_75_REVERSED,NB_<PERSON>oulli,0.8057259713701431,0.020103,success
train_25_test_75_REVERSED,NB_Complement,0.8609406952965235,0.0,success
train_25_test_75_REVERSED,NB_Multinomial_Tuned,0.869120654396728,0.007986,success
train_25_test_75_REVERSED,NB_Bernoulli_Tuned,0.8377641445126107,0.004937,success
train_25_test_75_REVERSED,LR_L1,0.8425357873210634,0.008984,success
train_25_test_75_REVERSED,LR_L2,0.8779822767552828,0.010949,success
train_25_test_75_REVERSED,LR_ElasticNet,0.8725289706884799,0.009992,success
train_25_test_75_REVERSED,LR_LBFGS,0.8779822767552828,0.017698,success
train_25_test_75_REVERSED,LR_SAG,0.8773006134969326,0.0,success
train_25_test_75_REVERSED,LR_SAGA,0.8779822767552828,0.011267,success
train_25_test_75_REVERSED,DT_Gini,0.8214042263122018,0.022977,success
train_25_test_75_REVERSED,DT_Entropy,0.8173142467620995,0.02528,success
train_25_test_75_REVERSED,DT_Log_Loss,0.8173142467620995,0.027984,success
train_25_test_75_REVERSED,DT_Gini_Pruned,0.858214042263122,0.018774,success
train_25_test_75_REVERSED,DT_Entropy_Pruned,0.8289025221540559,0.018963,success
train_25_test_75_REVERSED,DT_Best_First,0.8214042263122018,0.0235,success
train_25_test_75_REVERSED,DT_Random_Split,0.8159509202453987,0.017964,success
train_25_test_75_REVERSED,RF_Gini,0.8084526244035446,0.314842,success
train_25_test_75_REVERSED,RF_Entropy,0.8084526244035446,0.237189,success
train_25_test_75_REVERSED,RF_Log_Loss,0.8084526244035446,0.243431,success
train_25_test_75_REVERSED,RF_Large,0.8616223585548739,0.293418,success
train_25_test_75_REVERSED,RF_Small,0.8568507157464212,0.071284,success
train_25_test_75_REVERSED,ExtraTrees,0.8486707566462167,0.255983,success
train_25_test_75_REVERSED,ExtraTrees_Large,0.8629856850715747,0.233306,success
train_25_test_75_REVERSED,GradientBoosting,0.8813905930470347,0.295825,success
train_25_test_75_REVERSED,AdaBoost,0.8575323790047716,0.278965,success
train_25_test_75_REVERSED,AdaBoost_DT,0.8548057259713702,0.47017,success
train_30_test_70_REVERSED,SVM_Linear,0.8905109489051095,0.105607,success
train_30_test_70_REVERSED,SVM_Polynomial,0.8467153284671532,0.114819,success
train_30_test_70_REVERSED,SVM_RBF,0.8897810218978102,0.102122,success
train_30_test_70_REVERSED,SVM_Sigmoid,0.8861313868613139,0.083393,success
train_30_test_70_REVERSED,SVM_RBF_Tuned,0.891970802919708,0.125672,success
train_30_test_70_REVERSED,SVM_Linear_Tuned,0.8905109489051095,0.101092,success
train_30_test_70_REVERSED,NB_Gaussian,0.0,0.0,"error: Sparse data was passed for X, but dense data is required. Use '.toarray()' to convert to a dense numpy array."
train_30_test_70_REVERSED,NB_Multinomial,0.872992700729927,0.010873,success
train_30_test_70_REVERSED,NB_Bernoulli,0.8262773722627738,0.014616,success
train_30_test_70_REVERSED,NB_Complement,0.8693430656934307,0.0,success
train_30_test_70_REVERSED,NB_Multinomial_Tuned,0.8788321167883212,0.009315,success
train_30_test_70_REVERSED,NB_Bernoulli_Tuned,0.8481751824817518,0.002869,success
train_30_test_70_REVERSED,LR_L1,0.8496350364963504,0.00866,success
train_30_test_70_REVERSED,LR_L2,0.8846715328467153,0.0,success
train_30_test_70_REVERSED,LR_ElasticNet,0.8686131386861314,0.025944,success
train_30_test_70_REVERSED,LR_LBFGS,0.8846715328467153,0.013647,success
train_30_test_70_REVERSED,LR_SAG,0.8854014598540146,0.004015,success
train_30_test_70_REVERSED,LR_SAGA,0.8854014598540146,0.011063,success
train_30_test_70_REVERSED,DT_Gini,0.8306569343065694,0.029084,success
train_30_test_70_REVERSED,DT_Entropy,0.8262773722627738,0.029088,success
train_30_test_70_REVERSED,DT_Log_Loss,0.8262773722627738,0.018223,success
train_30_test_70_REVERSED,DT_Gini_Pruned,0.8759124087591241,0.025902,success
train_30_test_70_REVERSED,DT_Entropy_Pruned,0.8423357664233576,0.022502,success
train_30_test_70_REVERSED,DT_Best_First,0.8306569343065694,0.024967,success
train_30_test_70_REVERSED,DT_Random_Split,0.8218978102189781,0.022355,success
train_30_test_70_REVERSED,RF_Gini,0.8335766423357664,0.332043,success
train_30_test_70_REVERSED,RF_Entropy,0.8328467153284671,0.256431,success
train_30_test_70_REVERSED,RF_Log_Loss,0.8328467153284671,0.301991,success
train_30_test_70_REVERSED,RF_Large,0.8678832116788321,0.289289,success
train_30_test_70_REVERSED,RF_Small,0.8583941605839416,0.064888,success
train_30_test_70_REVERSED,ExtraTrees,0.845985401459854,0.332318,success
train_30_test_70_REVERSED,ExtraTrees_Large,0.8722627737226277,0.226227,success
train_30_test_70_REVERSED,GradientBoosting,0.8824817518248175,0.370832,success
train_30_test_70_REVERSED,AdaBoost,0.8737226277372263,0.24281,success
train_30_test_70_REVERSED,AdaBoost_DT,0.8452554744525548,0.431648,success
train_35_test_65_REVERSED,SVM_Linear,0.8962264150943396,0.113852,success
train_35_test_65_REVERSED,SVM_Polynomial,0.8592767295597484,0.137816,success
train_35_test_65_REVERSED,SVM_RBF,0.8954402515723271,0.146745,success
train_35_test_65_REVERSED,SVM_Sigmoid,0.8946540880503144,0.123403,success
train_35_test_65_REVERSED,SVM_RBF_Tuned,0.8970125786163522,0.171334,success
train_35_test_65_REVERSED,SVM_Linear_Tuned,0.8962264150943396,0.124466,success
train_35_test_65_REVERSED,NB_Gaussian,0.0,0.0,"error: Sparse data was passed for X, but dense data is required. Use '.toarray()' to convert to a dense numpy array."
train_35_test_65_REVERSED,NB_Multinomial,0.8797169811320755,0.02435,success
train_35_test_65_REVERSED,NB_Bernoulli,0.8301886792452831,0.011567,success
train_35_test_65_REVERSED,NB_Complement,0.8781446540880503,0.012167,success
train_35_test_65_REVERSED,NB_Multinomial_Tuned,0.8773584905660378,0.002376,success
train_35_test_65_REVERSED,NB_Bernoulli_Tuned,0.8529874213836478,0.002589,success
train_35_test_65_REVERSED,LR_L1,0.8498427672955975,0.020663,success
train_35_test_65_REVERSED,LR_L2,0.8922955974842768,0.0,success
train_35_test_65_REVERSED,LR_ElasticNet,0.8765723270440252,0.058822,success
train_35_test_65_REVERSED,LR_LBFGS,0.8930817610062893,0.022874,success
train_35_test_65_REVERSED,LR_SAG,0.8922955974842768,0.017925,success
train_35_test_65_REVERSED,LR_SAGA,0.8922955974842768,0.001811,success
train_35_test_65_REVERSED,DT_Gini,0.8459119496855346,0.032715,success
train_35_test_65_REVERSED,DT_Entropy,0.8325471698113207,0.033966,success
train_35_test_65_REVERSED,DT_Log_Loss,0.8325471698113207,0.02322,success
train_35_test_65_REVERSED,DT_Gini_Pruned,0.8537735849056604,0.018067,success
train_35_test_65_REVERSED,DT_Entropy_Pruned,0.845125786163522,0.023296,success
train_35_test_65_REVERSED,DT_Best_First,0.8459119496855346,0.022969,success
train_35_test_65_REVERSED,DT_Random_Split,0.8482704402515723,0.033225,success
train_35_test_65_REVERSED,RF_Gini,0.85062893081761,0.295358,success
train_35_test_65_REVERSED,RF_Entropy,0.8427672955974843,0.321762,success
train_35_test_65_REVERSED,RF_Log_Loss,0.8427672955974843,0.28244,success
train_35_test_65_REVERSED,RF_Large,0.875,0.290423,success
train_35_test_65_REVERSED,RF_Small,0.8742138364779874,0.079271,success
train_35_test_65_REVERSED,ExtraTrees,0.8694968553459119,0.346005,success
train_35_test_65_REVERSED,ExtraTrees_Large,0.8765723270440252,0.249743,success
train_35_test_65_REVERSED,GradientBoosting,0.8828616352201258,0.3896,success
train_35_test_65_REVERSED,AdaBoost,0.8781446540880503,0.260594,success
train_35_test_65_REVERSED,AdaBoost_DT,0.8608490566037735,0.48064,success
