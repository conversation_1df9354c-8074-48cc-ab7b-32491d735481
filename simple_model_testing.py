#!/usr/bin/env python3
"""
Simple Model Testing Script
==========================

This script tests a few selected models on the test dataset to verify everything works
before running the comprehensive testing.

Author: AI Assistant
Date: 2025-06-29
"""

import os
import pandas as pd
import numpy as np
import joblib
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def test_sample_models():
    """Test a few sample models to verify the pipeline works"""
    
    print("🚀 SIMPLE MODEL TESTING")
    print("=" * 40)
    
    # Load test data
    print("\n📊 Loading test dataset...")
    test_data_path = "dataset/youtube_comments_i6IOiUi6IYY.xlsx"
    
    try:
        test_data = pd.read_excel(test_data_path)
        print(f"✅ Dataset loaded: {test_data.shape}")
        print(f"   Columns: {test_data.columns.tolist()}")
        
        # Get comments
        comments = test_data['Comment'].dropna().tolist()
        print(f"   Comments to test: {len(comments)}")
        
    except Exception as e:
        print(f"❌ Error loading dataset: {e}")
        return
    
    # Find available models
    models_dir = "NEW-YOUTUBE-SPAM2025/comprehensive_models"
    print(f"\n🤖 Looking for models in: {models_dir}")
    
    if not os.path.exists(models_dir):
        print(f"❌ Models directory not found: {models_dir}")
        return
    
    model_files = [f for f in os.listdir(models_dir) if f.endswith('.pkl')]
    print(f"   Found {len(model_files)} model files")
    
    # Test first 5 models
    test_models = model_files[:5]
    print(f"   Testing first {len(test_models)} models:")
    
    results = {}
    
    for i, model_file in enumerate(test_models):
        model_path = os.path.join(models_dir, model_file)
        model_name = model_file.replace('.pkl', '')
        
        print(f"\n   {i+1}. Testing: {model_name}")
        
        try:
            # Load model
            model = joblib.load(model_path)
            print(f"      ✅ Model loaded successfully")
            
            # Make predictions
            predictions = model.predict(comments)
            print(f"      ✅ Predictions generated: {len(predictions)}")
            
            # Calculate basic stats
            spam_count = np.sum(predictions == 1)
            ham_count = np.sum(predictions == 0)
            spam_pct = spam_count / len(predictions) * 100
            
            print(f"      📊 Results: {spam_count} spam ({spam_pct:.1f}%), {ham_count} ham ({100-spam_pct:.1f}%)")
            
            # Try to get confidence scores
            try:
                probabilities = model.predict_proba(comments)
                confidences = np.max(probabilities, axis=1)
                avg_confidence = np.mean(confidences)
                print(f"      🎯 Average confidence: {avg_confidence:.3f}")
            except:
                print(f"      ⚠️  Confidence scores not available")
            
            results[model_name] = {
                'spam_count': spam_count,
                'ham_count': ham_count,
                'spam_percentage': spam_pct,
                'predictions': predictions.tolist()
            }
            
        except Exception as e:
            print(f"      ❌ Error: {e}")
            continue
    
    # Summary
    print(f"\n📈 TESTING SUMMARY")
    print("=" * 30)
    print(f"   Total comments tested: {len(comments)}")
    print(f"   Models successfully tested: {len(results)}")
    
    if results:
        print(f"\n📊 SPAM DETECTION COMPARISON:")
        for model_name, result in results.items():
            print(f"   {model_name[:50]:<50} | {result['spam_count']:>3} spam ({result['spam_percentage']:>5.1f}%)")
        
        # Calculate ensemble prediction (majority vote)
        print(f"\n🗳️  ENSEMBLE PREDICTION (Majority Vote):")
        ensemble_predictions = []
        
        for i in range(len(comments)):
            comment_predictions = []
            for model_name, result in results.items():
                comment_predictions.append(result['predictions'][i])
            
            # Majority vote
            spam_votes = sum(comment_predictions)
            total_votes = len(comment_predictions)
            ensemble_pred = 1 if spam_votes > total_votes / 2 else 0
            ensemble_predictions.append(ensemble_pred)
        
        ensemble_spam = sum(ensemble_predictions)
        ensemble_spam_pct = ensemble_spam / len(ensemble_predictions) * 100
        
        print(f"   Ensemble Result: {ensemble_spam} spam ({ensemble_spam_pct:.1f}%), {len(ensemble_predictions) - ensemble_spam} ham ({100 - ensemble_spam_pct:.1f}%)")
        
        # Show some example predictions
        print(f"\n📝 SAMPLE PREDICTIONS:")
        for i in range(min(5, len(comments))):
            comment = comments[i]
            ensemble_pred = ensemble_predictions[i]
            label = "SPAM" if ensemble_pred == 1 else "HAM"
            
            print(f"   {i+1}. [{label}] {comment[:80]}{'...' if len(comment) > 80 else ''}")
        
        # Save simple results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"simple_test_results_{timestamp}.csv"
        
        # Create results dataframe
        df_results = test_data.copy()
        df_results = df_results.dropna(subset=['Comment'])
        df_results['Ensemble_Prediction'] = ensemble_predictions
        df_results['Ensemble_Label'] = ['SPAM' if p == 1 else 'HAM' for p in ensemble_predictions]
        
        # Add individual model predictions
        for model_name, result in results.items():
            short_name = model_name.split('_')[-2] + '_' + model_name.split('_')[-1]  # Get last two parts
            df_results[f'{short_name}_Pred'] = result['predictions']
            df_results[f'{short_name}_Label'] = ['SPAM' if p == 1 else 'HAM' for p in result['predictions']]
        
        df_results.to_csv(results_file, index=False)
        print(f"\n💾 Results saved to: {results_file}")
        
    else:
        print("   ❌ No models were successfully tested")
    
    print(f"\n✅ Simple testing completed!")

if __name__ == "__main__":
    test_sample_models()
