YOUTUBE SPAM CLASSIFICATION - COMPREHENSIVE ALGORITHM ANALYSIS
================================================================================
Analysis Date: 2025-06-30 00:33:52
Total Models Analyzed: 204
Algorithm Variants: 34
Algorithm Types: 6

OVERALL PERFORMANCE SUMMARY:
----------------------------------------
Best Overall Model: SVM_RBF
Best Accuracy: 0.9182 (91.82%)
Split Type: Original
Split Name: train_75_test_25

PERFORMANCE BY ALGORITHM TYPE:
----------------------------------------
Support Vector Machine:
  Average Accuracy: 0.8922 (89.22%)
  Best Accuracy: 0.9182 (91.82%)
  Models Tested: 36

Ensemble:
  Average Accuracy: 0.8863 (88.63%)
  Best Accuracy: 0.9018 (90.18%)
  Models Tested: 12

Logistic Regression:
  Average Accuracy: 0.8860 (88.60%)
  Best Accuracy: 0.9059 (90.59%)
  Models Tested: 36

Naive Bayes:
  Average Accuracy: 0.8681 (86.81%)
  Best Accuracy: 0.9029 (90.29%)
  Models Tested: 30

Random Forest:
  Average Accuracy: 0.8659 (86.59%)
  Best Accuracy: 0.9012 (90.12%)
  Models Tested: 42

Decision Tree:
  Average Accuracy: 0.8524 (85.24%)
  Best Accuracy: 0.8875 (88.75%)
  Models Tested: 48

ORIGINAL vs REVERSED SPLIT COMPARISON:
----------------------------------------
Original Splits:
  Average Accuracy: 0.8849 (88.49%)
  Best Accuracy: 0.9182 (91.82%)
  Worst Accuracy: 0.8433 (84.33%)
  Standard Deviation: 0.0170

Reversed Splits:
  Average Accuracy: 0.8600 (86.00%)
  Best Accuracy: 0.8970 (89.70%)
  Worst Accuracy: 0.8057 (80.57%)
  Standard Deviation: 0.0249

TOP 10 PERFORMING MODELS:
----------------------------------------
 1. SVM_RBF (Original)
    Accuracy: 0.9182 (91.82%)
    Split: train_75_test_25
    Training Time: 0.66s

 2. SVM_RBF_Tuned (Original)
    Accuracy: 0.9182 (91.82%)
    Split: train_75_test_25
    Training Time: 0.70s

 3. SVM_Sigmoid (Original)
    Accuracy: 0.9121 (91.21%)
    Split: train_75_test_25
    Training Time: 0.39s

 4. SVM_RBF (Original)
    Accuracy: 0.9066 (90.66%)
    Split: train_65_test_35
    Training Time: 0.51s

 5. SVM_RBF (Original)
    Accuracy: 0.9063 (90.63%)
    Split: train_70_test_30
    Training Time: 0.56s

 6. LR_L2 (Original)
    Accuracy: 0.9059 (90.59%)
    Split: train_75_test_25
    Training Time: 0.04s

 7. LR_LBFGS (Original)
    Accuracy: 0.9059 (90.59%)
    Split: train_75_test_25
    Training Time: 0.02s

 8. LR_SAG (Original)
    Accuracy: 0.9059 (90.59%)
    Split: train_75_test_25
    Training Time: 0.03s

 9. LR_SAGA (Original)
    Accuracy: 0.9059 (90.59%)
    Split: train_75_test_25
    Training Time: 0.03s

10. SVM_Sigmoid (Original)
    Accuracy: 0.9046 (90.46%)
    Split: train_70_test_30
    Training Time: 0.35s
