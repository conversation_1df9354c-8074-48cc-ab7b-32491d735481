{"SVM_Linear": {"accuracy": 0.9007299270072993, "precision": [0.8610354223433242, 0.9465408805031447], "recall": [0.948948948948949, 0.8551136363636364], "f1_score": [0.9028571428571428, 0.8985074626865671], "support": [333, 352], "classification_report": {"0": {"precision": 0.8610354223433242, "recall": 0.948948948948949, "f1-score": 0.9028571428571428, "support": 333.0}, "1": {"precision": 0.9465408805031447, "recall": 0.8551136363636364, "f1-score": 0.8985074626865671, "support": 352.0}, "accuracy": 0.9007299270072993, "macro avg": {"precision": 0.9037881514232344, "recall": 0.9020312926562927, "f1-score": 0.9006823027718549, "support": 685.0}, "weighted avg": {"precision": 0.9049739935436991, "recall": 0.9007299270072993, "f1-score": 0.9006219787402922, "support": 685.0}}, "confusion_matrix": [[316, 17], [51, 301]], "roc_auc": 0.9637762762762764, "training_time": 0.304597, "train_size": 1271, "test_size": 685, "algorithm_type": "Support Vector Machine"}, "SVM_Polynomial": {"accuracy": 0.872992700729927, "precision": [0.875, 0.8711484593837535], "recall": [0.8618618618618619, 0.8835227272727273], "f1_score": [0.8683812405446294, 0.8772919605077574], "support": [333, 352], "classification_report": {"0": {"precision": 0.875, "recall": 0.8618618618618619, "f1-score": 0.8683812405446294, "support": 333.0}, "1": {"precision": 0.8711484593837535, "recall": 0.8835227272727273, "f1-score": 0.8772919605077574, "support": 352.0}, "accuracy": 0.872992700729927, "macro avg": {"precision": 0.8730742296918768, "recall": 0.8726922945672946, "f1-score": 0.8728366005261934, "support": 685.0}, "weighted avg": {"precision": 0.873020814165082, "recall": 0.872992700729927, "f1-score": 0.8729601798541492, "support": 685.0}}, "confusion_matrix": [[287, 46], [41, 311]], "roc_auc": 0.9374488124488124, "training_time": 0.474958, "train_size": 1271, "test_size": 685, "algorithm_type": "Support Vector Machine"}, "SVM_RBF": {"accuracy": 0.9065693430656935, "precision": [0.8788732394366198, 0.9363636363636364], "recall": [0.9369369369369369, 0.8778409090909091], "f1_score": [0.9069767441860465, 0.906158357771261], "support": [333, 352], "classification_report": {"0": {"precision": 0.8788732394366198, "recall": 0.9369369369369369, "f1-score": 0.9069767441860465, "support": 333.0}, "1": {"precision": 0.9363636363636364, "recall": 0.8778409090909091, "f1-score": 0.906158357771261, "support": 352.0}, "accuracy": 0.9065693430656935, "macro avg": {"precision": 0.9076184379001281, "recall": 0.9073889230139229, "f1-score": 0.9065675509786537, "support": 685.0}, "weighted avg": {"precision": 0.9084157499742984, "recall": 0.9065693430656935, "f1-score": 0.9065562010940692, "support": 685.0}}, "confusion_matrix": [[312, 21], [43, 309]], "roc_auc": 0.9487015424515424, "training_time": 0.506284, "train_size": 1271, "test_size": 685, "algorithm_type": "Support Vector Machine"}, "SVM_Sigmoid": {"accuracy": 0.9007299270072993, "precision": [0.8571428571428571, 0.9522292993630573], "recall": [0.954954954954955, 0.8494318181818182], "f1_score": [0.9034090909090909, 0.8978978978978979], "support": [333, 352], "classification_report": {"0": {"precision": 0.8571428571428571, "recall": 0.954954954954955, "f1-score": 0.9034090909090909, "support": 333.0}, "1": {"precision": 0.9522292993630573, "recall": 0.8494318181818182, "f1-score": 0.8978978978978979, "support": 352.0}, "accuracy": 0.9007299270072993, "macro avg": {"precision": 0.9046860782529572, "recall": 0.9021933865683867, "f1-score": 0.9006534944034944, "support": 685.0}, "weighted avg": {"precision": 0.9060047953348432, "recall": 0.9007299270072993, "f1-score": 0.9005770617996895, "support": 685.0}}, "confusion_matrix": [[318, 15], [53, 299]], "roc_auc": 0.9634435571935572, "training_time": 0.315534, "train_size": 1271, "test_size": 685, "algorithm_type": "Support Vector Machine"}, "SVM_RBF_Tuned": {"accuracy": 0.9036496350364963, "precision": [0.8825214899713467, 0.9255952380952381], "recall": [0.924924924924925, 0.8835227272727273], "f1_score": [0.9032258064516129, 0.9040697674418605], "support": [333, 352], "classification_report": {"0": {"precision": 0.8825214899713467, "recall": 0.924924924924925, "f1-score": 0.9032258064516129, "support": 333.0}, "1": {"precision": 0.9255952380952381, "recall": 0.8835227272727273, "f1-score": 0.9040697674418605, "support": 352.0}, "accuracy": 0.9036496350364963, "macro avg": {"precision": 0.9040583640332924, "recall": 0.9042238260988261, "f1-score": 0.9036477869467368, "support": 685.0}, "weighted avg": {"precision": 0.904655737182456, "recall": 0.9036496350364963, "f1-score": 0.9036594915152144, "support": 685.0}}, "confusion_matrix": [[308, 25], [41, 311]], "roc_auc": 0.9476777914277913, "training_time": 0.499647, "train_size": 1271, "test_size": 685, "algorithm_type": "Support Vector Machine"}, "SVM_Linear_Tuned": {"accuracy": 0.9007299270072993, "precision": [0.8610354223433242, 0.9465408805031447], "recall": [0.948948948948949, 0.8551136363636364], "f1_score": [0.9028571428571428, 0.8985074626865671], "support": [333, 352], "classification_report": {"0": {"precision": 0.8610354223433242, "recall": 0.948948948948949, "f1-score": 0.9028571428571428, "support": 333.0}, "1": {"precision": 0.9465408805031447, "recall": 0.8551136363636364, "f1-score": 0.8985074626865671, "support": 352.0}, "accuracy": 0.9007299270072993, "macro avg": {"precision": 0.9037881514232344, "recall": 0.9020312926562927, "f1-score": 0.9006823027718549, "support": 685.0}, "weighted avg": {"precision": 0.9049739935436991, "recall": 0.9007299270072993, "f1-score": 0.9006219787402922, "support": 685.0}}, "confusion_matrix": [[316, 17], [51, 301]], "roc_auc": 0.9637762762762764, "training_time": 0.343782, "train_size": 1271, "test_size": 685, "algorithm_type": "Support Vector Machine"}, "NB_Multinomial": {"accuracy": 0.8934306569343066, "precision": [0.9193548387096774, 0.872], "recall": [0.8558558558558559, 0.9289772727272727], "f1_score": [0.8864696734059098, 0.8995873452544704], "support": [333, 352], "classification_report": {"0": {"precision": 0.9193548387096774, "recall": 0.8558558558558559, "f1-score": 0.8864696734059098, "support": 333.0}, "1": {"precision": 0.872, "recall": 0.9289772727272727, "f1-score": 0.8995873452544704, "support": 352.0}, "accuracy": 0.8934306569343066, "macro avg": {"precision": 0.8956774193548387, "recall": 0.8924165642915642, "f1-score": 0.8930285093301901, "support": 685.0}, "weighted avg": {"precision": 0.8950206734165294, "recall": 0.8934306569343066, "f1-score": 0.893210433246338, "support": 685.0}}, "confusion_matrix": [[285, 48], [25, 327]], "roc_auc": 0.9567550505050505, "training_time": 0.023143, "train_size": 1271, "test_size": 685, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli": {"accuracy": 0.8467153284671532, "precision": [0.7663551401869159, 0.980544747081712], "recall": [0.984984984984985, 0.7159090909090909], "f1_score": [0.8620236530880421, 0.8275862068965517], "support": [333, 352], "classification_report": {"0": {"precision": 0.7663551401869159, "recall": 0.984984984984985, "f1-score": 0.8620236530880421, "support": 333.0}, "1": {"precision": 0.980544747081712, "recall": 0.7159090909090909, "f1-score": 0.8275862068965517, "support": 352.0}, "accuracy": 0.8467153284671532, "macro avg": {"precision": 0.8734499436343139, "recall": 0.8504470379470379, "f1-score": 0.8448049299922968, "support": 685.0}, "weighted avg": {"precision": 0.8764204564306651, "recall": 0.8467153284671532, "f1-score": 0.8443273303735829, "support": 685.0}}, "confusion_matrix": [[328, 5], [100, 252]], "roc_auc": 0.9460611179361178, "training_time": 0.01596, "train_size": 1271, "test_size": 685, "algorithm_type": "<PERSON><PERSON>"}, "NB_Complement": {"accuracy": 0.8832116788321168, "precision": [0.8624641833810889, 0.9047619047619048], "recall": [0.9039039039039038, 0.8636363636363636], "f1_score": [0.8826979472140762, 0.8837209302325582], "support": [333, 352], "classification_report": {"0": {"precision": 0.8624641833810889, "recall": 0.9039039039039038, "f1-score": 0.8826979472140762, "support": 333.0}, "1": {"precision": 0.9047619047619048, "recall": 0.8636363636363636, "f1-score": 0.8837209302325582, "support": 352.0}, "accuracy": 0.8832116788321168, "macro avg": {"precision": 0.8836130440714969, "recall": 0.8837701337701338, "f1-score": 0.8832094387233171, "support": 685.0}, "weighted avg": {"precision": 0.8841996548059754, "recall": 0.8832116788321168, "f1-score": 0.883223626079048, "support": 685.0}}, "confusion_matrix": [[301, 32], [48, 304]], "roc_auc": 0.9567550505050505, "training_time": 0.013929, "train_size": 1271, "test_size": 685, "algorithm_type": "<PERSON><PERSON>"}, "NB_Multinomial_Tuned": {"accuracy": 0.8890510948905109, "precision": [0.9105431309904153, 0.8709677419354839], "recall": [0.8558558558558559, 0.9204545454545454], "f1_score": [0.8823529411764706, 0.8950276243093923], "support": [333, 352], "classification_report": {"0": {"precision": 0.9105431309904153, "recall": 0.8558558558558559, "f1-score": 0.8823529411764706, "support": 333.0}, "1": {"precision": 0.8709677419354839, "recall": 0.9204545454545454, "f1-score": 0.8950276243093923, "support": 352.0}, "accuracy": 0.8890510948905109, "macro avg": {"precision": 0.8907554364629495, "recall": 0.8881552006552007, "f1-score": 0.8886902827429315, "support": 685.0}, "weighted avg": {"precision": 0.8902065807023336, "recall": 0.8890510948905109, "f1-score": 0.8888660630199573, "support": 685.0}}, "confusion_matrix": [[285, 48], [28, 324]], "roc_auc": 0.9515339202839203, "training_time": 0.018869, "train_size": 1271, "test_size": 685, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli_Tuned": {"accuracy": 0.8744525547445255, "precision": [0.8126582278481013, 0.9586206896551724], "recall": [0.963963963963964, 0.7897727272727273], "f1_score": [0.8818681318681318, 0.8660436137071651], "support": [333, 352], "classification_report": {"0": {"precision": 0.8126582278481013, "recall": 0.963963963963964, "f1-score": 0.8818681318681318, "support": 333.0}, "1": {"precision": 0.9586206896551724, "recall": 0.7897727272727273, "f1-score": 0.8660436137071651, "support": 352.0}, "accuracy": 0.8744525547445255, "macro avg": {"precision": 0.8856394587516369, "recall": 0.8768683456183457, "f1-score": 0.8739558727876484, "support": 685.0}, "weighted avg": {"precision": 0.8876637556672093, "recall": 0.8744525547445255, "f1-score": 0.873736408667168, "support": 685.0}}, "confusion_matrix": [[321, 12], [74, 278]], "roc_auc": 0.9384597665847665, "training_time": 0.019495, "train_size": 1271, "test_size": 685, "algorithm_type": "<PERSON><PERSON>"}, "LR_L1": {"accuracy": 0.8773722627737226, "precision": [0.8151898734177215, 0.9620689655172414], "recall": [0.9669669669669669, 0.7926136363636364], "f1_score": [0.8846153846153846, 0.8691588785046729], "support": [333, 352], "classification_report": {"0": {"precision": 0.8151898734177215, "recall": 0.9669669669669669, "f1-score": 0.8846153846153846, "support": 333.0}, "1": {"precision": 0.9620689655172414, "recall": 0.7926136363636364, "f1-score": 0.8691588785046729, "support": 352.0}, "accuracy": 0.8773722627737226, "macro avg": {"precision": 0.8886294194674815, "recall": 0.8797903016653017, "f1-score": 0.8768871315600287, "support": 685.0}, "weighted avg": {"precision": 0.8906664287739712, "recall": 0.8773722627737226, "f1-score": 0.8766727712563035, "support": 685.0}}, "confusion_matrix": [[322, 11], [73, 279]], "roc_auc": 0.9438216625716626, "training_time": 0.022273, "train_size": 1271, "test_size": 685, "algorithm_type": "Logistic Regression"}, "LR_L2": {"accuracy": 0.8978102189781022, "precision": [0.8583106267029973, 0.9433962264150944], "recall": [0.9459459459459459, 0.8522727272727273], "f1_score": [0.9, 0.8955223880597015], "support": [333, 352], "classification_report": {"0": {"precision": 0.8583106267029973, "recall": 0.9459459459459459, "f1-score": 0.9, "support": 333.0}, "1": {"precision": 0.9433962264150944, "recall": 0.8522727272727273, "f1-score": 0.8955223880597015, "support": 352.0}, "accuracy": 0.8978102189781022, "macro avg": {"precision": 0.9008534265590458, "recall": 0.8991093366093366, "f1-score": 0.8977611940298508, "support": 685.0}, "weighted avg": {"precision": 0.902033445825126, "recall": 0.8978102189781022, "f1-score": 0.8976990957620655, "support": 685.0}}, "confusion_matrix": [[315, 18], [52, 300]], "roc_auc": 0.957591113841114, "training_time": 0.016846, "train_size": 1271, "test_size": 685, "algorithm_type": "Logistic Regression"}, "LR_ElasticNet": {"accuracy": 0.8846715328467153, "precision": [0.8342105263157895, 0.9475409836065574], "recall": [0.9519519519519519, 0.8210227272727273], "f1_score": [0.8892005610098177, 0.8797564687975646], "support": [333, 352], "classification_report": {"0": {"precision": 0.8342105263157895, "recall": 0.9519519519519519, "f1-score": 0.8892005610098177, "support": 333.0}, "1": {"precision": 0.9475409836065574, "recall": 0.8210227272727273, "f1-score": 0.8797564687975646, "support": 352.0}, "accuracy": 0.8846715328467153, "macro avg": {"precision": 0.8908757549611734, "recall": 0.8864873396123396, "f1-score": 0.8844785149036911, "support": 685.0}, "weighted avg": {"precision": 0.8924474912301695, "recall": 0.8846715328467153, "f1-score": 0.8843475384423534, "support": 685.0}}, "confusion_matrix": [[317, 16], [63, 289]], "roc_auc": 0.9497679497679498, "training_time": 0.029847, "train_size": 1271, "test_size": 685, "algorithm_type": "Logistic Regression"}, "LR_LBFGS": {"accuracy": 0.8978102189781022, "precision": [0.8583106267029973, 0.9433962264150944], "recall": [0.9459459459459459, 0.8522727272727273], "f1_score": [0.9, 0.8955223880597015], "support": [333, 352], "classification_report": {"0": {"precision": 0.8583106267029973, "recall": 0.9459459459459459, "f1-score": 0.9, "support": 333.0}, "1": {"precision": 0.9433962264150944, "recall": 0.8522727272727273, "f1-score": 0.8955223880597015, "support": 352.0}, "accuracy": 0.8978102189781022, "macro avg": {"precision": 0.9008534265590458, "recall": 0.8991093366093366, "f1-score": 0.8977611940298508, "support": 685.0}, "weighted avg": {"precision": 0.902033445825126, "recall": 0.8978102189781022, "f1-score": 0.8976990957620655, "support": 685.0}}, "confusion_matrix": [[315, 18], [52, 300]], "roc_auc": 0.9575911138411138, "training_time": 0.042764, "train_size": 1271, "test_size": 685, "algorithm_type": "Logistic Regression"}, "LR_SAG": {"accuracy": 0.8978102189781022, "precision": [0.8583106267029973, 0.9433962264150944], "recall": [0.9459459459459459, 0.8522727272727273], "f1_score": [0.9, 0.8955223880597015], "support": [333, 352], "classification_report": {"0": {"precision": 0.8583106267029973, "recall": 0.9459459459459459, "f1-score": 0.9, "support": 333.0}, "1": {"precision": 0.9433962264150944, "recall": 0.8522727272727273, "f1-score": 0.8955223880597015, "support": 352.0}, "accuracy": 0.8978102189781022, "macro avg": {"precision": 0.9008534265590458, "recall": 0.8991093366093366, "f1-score": 0.8977611940298508, "support": 685.0}, "weighted avg": {"precision": 0.902033445825126, "recall": 0.8978102189781022, "f1-score": 0.8976990957620655, "support": 685.0}}, "confusion_matrix": [[315, 18], [52, 300]], "roc_auc": 0.9575911138411138, "training_time": 0.02187, "train_size": 1271, "test_size": 685, "algorithm_type": "Logistic Regression"}, "LR_SAGA": {"accuracy": 0.8978102189781022, "precision": [0.8583106267029973, 0.9433962264150944], "recall": [0.9459459459459459, 0.8522727272727273], "f1_score": [0.9, 0.8955223880597015], "support": [333, 352], "classification_report": {"0": {"precision": 0.8583106267029973, "recall": 0.9459459459459459, "f1-score": 0.9, "support": 333.0}, "1": {"precision": 0.9433962264150944, "recall": 0.8522727272727273, "f1-score": 0.8955223880597015, "support": 352.0}, "accuracy": 0.8978102189781022, "macro avg": {"precision": 0.9008534265590458, "recall": 0.8991093366093366, "f1-score": 0.8977611940298508, "support": 685.0}, "weighted avg": {"precision": 0.902033445825126, "recall": 0.8978102189781022, "f1-score": 0.8976990957620655, "support": 685.0}}, "confusion_matrix": [[315, 18], [52, 300]], "roc_auc": 0.9575996450996451, "training_time": 0.022795, "train_size": 1271, "test_size": 685, "algorithm_type": "Logistic Regression"}, "DT_Gini": {"accuracy": 0.8613138686131386, "precision": [0.916083916083916, 0.8220551378446115], "recall": [0.7867867867867868, 0.9318181818181818], "f1_score": [0.8465266558966075, 0.8735019973368842], "support": [333, 352], "classification_report": {"0": {"precision": 0.916083916083916, "recall": 0.7867867867867868, "f1-score": 0.8465266558966075, "support": 333.0}, "1": {"precision": 0.8220551378446115, "recall": 0.9318181818181818, "f1-score": 0.8735019973368842, "support": 352.0}, "accuracy": 0.8613138686131386, "macro avg": {"precision": 0.8690695269642638, "recall": 0.8593024843024843, "f1-score": 0.8600143266167458, "support": 685.0}, "weighted avg": {"precision": 0.8677654782149595, "recall": 0.8613138686131386, "f1-score": 0.8603884371914651, "support": 685.0}}, "confusion_matrix": [[262, 71], [24, 328]], "roc_auc": 0.9262984575484576, "training_time": 0.062686, "train_size": 1271, "test_size": 685, "algorithm_type": "Decision Tree"}, "DT_Entropy": {"accuracy": 0.8613138686131386, "precision": [0.9131944444444444, 0.8236775818639799], "recall": [0.7897897897897898, 0.9289772727272727], "f1_score": [0.8470209339774557, 0.8731642189586115], "support": [333, 352], "classification_report": {"0": {"precision": 0.9131944444444444, "recall": 0.7897897897897898, "f1-score": 0.8470209339774557, "support": 333.0}, "1": {"precision": 0.8236775818639799, "recall": 0.9289772727272727, "f1-score": 0.8731642189586115, "support": 352.0}, "accuracy": 0.8613138686131386, "macro avg": {"precision": 0.8684360131542121, "recall": 0.8593835312585312, "f1-score": 0.8600925764680336, "support": 685.0}, "weighted avg": {"precision": 0.8671945384176948, "recall": 0.8613138686131386, "f1-score": 0.8604551475736116, "support": 685.0}}, "confusion_matrix": [[263, 70], [25, 327]], "roc_auc": 0.9227963759213759, "training_time": 0.072757, "train_size": 1271, "test_size": 685, "algorithm_type": "Decision Tree"}, "DT_Log_Loss": {"accuracy": 0.8613138686131386, "precision": [0.9131944444444444, 0.8236775818639799], "recall": [0.7897897897897898, 0.9289772727272727], "f1_score": [0.8470209339774557, 0.8731642189586115], "support": [333, 352], "classification_report": {"0": {"precision": 0.9131944444444444, "recall": 0.7897897897897898, "f1-score": 0.8470209339774557, "support": 333.0}, "1": {"precision": 0.8236775818639799, "recall": 0.9289772727272727, "f1-score": 0.8731642189586115, "support": 352.0}, "accuracy": 0.8613138686131386, "macro avg": {"precision": 0.8684360131542121, "recall": 0.8593835312585312, "f1-score": 0.8600925764680336, "support": 685.0}, "weighted avg": {"precision": 0.8671945384176948, "recall": 0.8613138686131386, "f1-score": 0.8604551475736116, "support": 685.0}}, "confusion_matrix": [[263, 70], [25, 327]], "roc_auc": 0.9227963759213759, "training_time": 0.079877, "train_size": 1271, "test_size": 685, "algorithm_type": "Decision Tree"}, "DT_Gini_Pruned": {"accuracy": 0.8817518248175182, "precision": [0.8073170731707318, 0.9927272727272727], "recall": [0.993993993993994, 0.7755681818181818], "f1_score": [0.8909825033647375, 0.8708133971291866], "support": [333, 352], "classification_report": {"0": {"precision": 0.8073170731707318, "recall": 0.993993993993994, "f1-score": 0.8909825033647375, "support": 333.0}, "1": {"precision": 0.9927272727272727, "recall": 0.7755681818181818, "f1-score": 0.8708133971291866, "support": 352.0}, "accuracy": 0.8817518248175182, "macro avg": {"precision": 0.9000221729490022, "recall": 0.8847810879060879, "f1-score": 0.880897950246962, "support": 685.0}, "weighted avg": {"precision": 0.9025935552786184, "recall": 0.8817518248175182, "f1-score": 0.8806182327152282, "support": 685.0}}, "confusion_matrix": [[331, 2], [79, 273]], "roc_auc": 0.8907615001365002, "training_time": 0.04306, "train_size": 1271, "test_size": 685, "algorithm_type": "Decision Tree"}, "DT_Entropy_Pruned": {"accuracy": 0.8656934306569343, "precision": [0.7917675544794189, 0.9779411764705882], "recall": [0.9819819819819819, 0.7556818181818182], "f1_score": [0.8766756032171582, 0.8525641025641025], "support": [333, 352], "classification_report": {"0": {"precision": 0.7917675544794189, "recall": 0.9819819819819819, "f1-score": 0.8766756032171582, "support": 333.0}, "1": {"precision": 0.9779411764705882, "recall": 0.7556818181818182, "f1-score": 0.8525641025641025, "support": 352.0}, "accuracy": 0.8656934306569343, "macro avg": {"precision": 0.8848543654750036, "recall": 0.8688319000819, "f1-score": 0.8646198528906304, "support": 685.0}, "weighted avg": {"precision": 0.8874363354150272, "recall": 0.8656934306569343, "f1-score": 0.86428545981588, "support": 685.0}}, "confusion_matrix": [[327, 6], [86, 266]], "roc_auc": 0.8875025593775595, "training_time": 0.035989, "train_size": 1271, "test_size": 685, "algorithm_type": "Decision Tree"}, "DT_Best_First": {"accuracy": 0.8613138686131386, "precision": [0.916083916083916, 0.8220551378446115], "recall": [0.7867867867867868, 0.9318181818181818], "f1_score": [0.8465266558966075, 0.8735019973368842], "support": [333, 352], "classification_report": {"0": {"precision": 0.916083916083916, "recall": 0.7867867867867868, "f1-score": 0.8465266558966075, "support": 333.0}, "1": {"precision": 0.8220551378446115, "recall": 0.9318181818181818, "f1-score": 0.8735019973368842, "support": 352.0}, "accuracy": 0.8613138686131386, "macro avg": {"precision": 0.8690695269642638, "recall": 0.8593024843024843, "f1-score": 0.8600143266167458, "support": 685.0}, "weighted avg": {"precision": 0.8677654782149595, "recall": 0.8613138686131386, "f1-score": 0.8603884371914651, "support": 685.0}}, "confusion_matrix": [[262, 71], [24, 328]], "roc_auc": 0.9262984575484576, "training_time": 0.057627, "train_size": 1271, "test_size": 685, "algorithm_type": "Decision Tree"}, "DT_Random_Split": {"accuracy": 0.8744525547445255, "precision": [0.9303135888501742, 0.8341708542713567], "recall": [0.8018018018018018, 0.9431818181818182], "f1_score": [0.8612903225806452, 0.8853333333333333], "support": [333, 352], "classification_report": {"0": {"precision": 0.9303135888501742, "recall": 0.8018018018018018, "f1-score": 0.8612903225806452, "support": 333.0}, "1": {"precision": 0.8341708542713567, "recall": 0.9431818181818182, "f1-score": 0.8853333333333333, "support": 352.0}, "accuracy": 0.8744525547445255, "macro avg": {"precision": 0.8822422215607655, "recall": 0.87249180999181, "f1-score": 0.8733118279569893, "support": 685.0}, "weighted avg": {"precision": 0.8809088551687966, "recall": 0.8744525547445255, "f1-score": 0.8736452711718075, "support": 685.0}}, "confusion_matrix": [[267, 66], [20, 332]], "roc_auc": 0.9347529347529346, "training_time": 0.083369, "train_size": 1271, "test_size": 685, "algorithm_type": "Decision Tree"}, "RF_Gini": {"accuracy": 0.8890510948905109, "precision": [0.9240924092409241, 0.8612565445026178], "recall": [0.8408408408408409, 0.9346590909090909], "f1_score": [0.8805031446540881, 0.896457765667575], "support": [333, 352], "classification_report": {"0": {"precision": 0.9240924092409241, "recall": 0.8408408408408409, "f1-score": 0.8805031446540881, "support": 333.0}, "1": {"precision": 0.8612565445026178, "recall": 0.9346590909090909, "f1-score": 0.896457765667575, "support": 352.0}, "accuracy": 0.8890510948905109, "macro avg": {"precision": 0.8926744768717709, "recall": 0.8877499658749659, "f1-score": 0.8884804551608315, "support": 685.0}, "weighted avg": {"precision": 0.8918030305724804, "recall": 0.8890510948905109, "f1-score": 0.888701723627442, "support": 685.0}}, "confusion_matrix": [[280, 53], [23, 329]], "roc_auc": 0.9572925197925197, "training_time": 0.504389, "train_size": 1271, "test_size": 685, "algorithm_type": "Random Forest"}, "RF_Entropy": {"accuracy": 0.8905109489051095, "precision": [0.9328859060402684, 0.8578811369509044], "recall": [0.8348348348348348, 0.9431818181818182], "f1_score": [0.8811410459587956, 0.8985115020297699], "support": [333, 352], "classification_report": {"0": {"precision": 0.9328859060402684, "recall": 0.8348348348348348, "f1-score": 0.8811410459587956, "support": 333.0}, "1": {"precision": 0.8578811369509044, "recall": 0.9431818181818182, "f1-score": 0.8985115020297699, "support": 352.0}, "accuracy": 0.8905109489051095, "macro avg": {"precision": 0.8953835214955864, "recall": 0.8890083265083265, "f1-score": 0.8898262739942828, "support": 685.0}, "weighted avg": {"precision": 0.8943433093695294, "recall": 0.8905109489051095, "f1-score": 0.8900671781295737, "support": 685.0}}, "confusion_matrix": [[278, 55], [20, 332]], "roc_auc": 0.9551426426426427, "training_time": 0.51252, "train_size": 1271, "test_size": 685, "algorithm_type": "Random Forest"}, "RF_Log_Loss": {"accuracy": 0.8905109489051095, "precision": [0.9328859060402684, 0.8578811369509044], "recall": [0.8348348348348348, 0.9431818181818182], "f1_score": [0.8811410459587956, 0.8985115020297699], "support": [333, 352], "classification_report": {"0": {"precision": 0.9328859060402684, "recall": 0.8348348348348348, "f1-score": 0.8811410459587956, "support": 333.0}, "1": {"precision": 0.8578811369509044, "recall": 0.9431818181818182, "f1-score": 0.8985115020297699, "support": 352.0}, "accuracy": 0.8905109489051095, "macro avg": {"precision": 0.8953835214955864, "recall": 0.8890083265083265, "f1-score": 0.8898262739942828, "support": 685.0}, "weighted avg": {"precision": 0.8943433093695294, "recall": 0.8905109489051095, "f1-score": 0.8900671781295737, "support": 685.0}}, "confusion_matrix": [[278, 55], [20, 332]], "roc_auc": 0.9551426426426427, "training_time": 0.493064, "train_size": 1271, "test_size": 685, "algorithm_type": "Random Forest"}, "RF_Large": {"accuracy": 0.8802919708029197, "precision": [0.8129675810473815, 0.9753521126760564], "recall": [0.978978978978979, 0.7869318181818182], "f1_score": [0.888283378746594, 0.8710691823899371], "support": [333, 352], "classification_report": {"0": {"precision": 0.8129675810473815, "recall": 0.978978978978979, "f1-score": 0.888283378746594, "support": 333.0}, "1": {"precision": 0.9753521126760564, "recall": 0.7869318181818182, "f1-score": 0.8710691823899371, "support": 352.0}, "accuracy": 0.8802919708029197, "macro avg": {"precision": 0.8941598468617189, "recall": 0.8829553985803986, "f1-score": 0.8796762805682656, "support": 685.0}, "weighted avg": {"precision": 0.8964118951105837, "recall": 0.8802919708029197, "f1-score": 0.8794375435385017, "support": 685.0}}, "confusion_matrix": [[326, 7], [75, 277]], "roc_auc": 0.9546947515697516, "training_time": 0.40096, "train_size": 1271, "test_size": 685, "algorithm_type": "Random Forest"}, "RF_Small": {"accuracy": 0.8759124087591241, "precision": [0.8212435233160622, 0.9464882943143813], "recall": [0.9519519519519519, 0.8039772727272727], "f1_score": [0.8817802503477051, 0.869431643625192], "support": [333, 352], "classification_report": {"0": {"precision": 0.8212435233160622, "recall": 0.9519519519519519, "f1-score": 0.8817802503477051, "support": 333.0}, "1": {"precision": 0.9464882943143813, "recall": 0.8039772727272727, "f1-score": 0.869431643625192, "support": 352.0}, "accuracy": 0.8759124087591241, "macro avg": {"precision": 0.8838659088152218, "recall": 0.8779646123396123, "f1-score": 0.8756059469864486, "support": 685.0}, "weighted avg": {"precision": 0.8856028800918407, "recall": 0.8759124087591241, "f1-score": 0.8754346889370123, "support": 685.0}}, "confusion_matrix": [[317, 16], [69, 283]], "roc_auc": 0.9472299003549004, "training_time": 0.112528, "train_size": 1271, "test_size": 685, "algorithm_type": "Random Forest"}, "ExtraTrees": {"accuracy": 0.8963503649635036, "precision": [0.9253246753246753, 0.8726790450928382], "recall": [0.8558558558558559, 0.9346590909090909], "f1_score": [0.8892355694227769, 0.9026063100137174], "support": [333, 352], "classification_report": {"0": {"precision": 0.9253246753246753, "recall": 0.8558558558558559, "f1-score": 0.8892355694227769, "support": 333.0}, "1": {"precision": 0.8726790450928382, "recall": 0.9346590909090909, "f1-score": 0.9026063100137174, "support": 352.0}, "accuracy": 0.8963503649635036, "macro avg": {"precision": 0.8990018602087568, "recall": 0.8952574733824734, "f1-score": 0.8959209397182472, "support": 685.0}, "weighted avg": {"precision": 0.898271738329629, "recall": 0.8963503649635036, "f1-score": 0.8961063733468806, "support": 685.0}}, "confusion_matrix": [[285, 48], [23, 329]], "roc_auc": 0.9615709459459458, "training_time": 0.676624, "train_size": 1271, "test_size": 685, "algorithm_type": "Random Forest"}, "ExtraTrees_Large": {"accuracy": 0.8686131386861314, "precision": [0.8075949367088607, 0.9517241379310345], "recall": [0.9579579579579579, 0.7840909090909091], "f1_score": [0.8763736263736264, 0.8598130841121495], "support": [333, 352], "classification_report": {"0": {"precision": 0.8075949367088607, "recall": 0.9579579579579579, "f1-score": 0.8763736263736264, "support": 333.0}, "1": {"precision": 0.9517241379310345, "recall": 0.7840909090909091, "f1-score": 0.8598130841121495, "support": 352.0}, "accuracy": 0.8686131386861314, "macro avg": {"precision": 0.8796595373199476, "recall": 0.8710244335244335, "f1-score": 0.868093355242888, "support": 685.0}, "weighted avg": {"precision": 0.8816584094536858, "recall": 0.8686131386861314, "f1-score": 0.8678636834888966, "support": 685.0}}, "confusion_matrix": [[319, 14], [76, 276]], "roc_auc": 0.9462146805896806, "training_time": 0.329771, "train_size": 1271, "test_size": 685, "algorithm_type": "Random Forest"}, "GradientBoosting": {"accuracy": 0.9007299270072993, "precision": [0.8406169665809768, 0.9797297297297297], "recall": [0.9819819819819819, 0.8238636363636364], "f1_score": [0.9058171745152355, 0.8950617283950617], "support": [333, 352], "classification_report": {"0": {"precision": 0.8406169665809768, "recall": 0.9819819819819819, "f1-score": 0.9058171745152355, "support": 333.0}, "1": {"precision": 0.9797297297297297, "recall": 0.8238636363636364, "f1-score": 0.8950617283950617, "support": 352.0}, "accuracy": 0.9007299270072993, "macro avg": {"precision": 0.9101733481553533, "recall": 0.9029228091728092, "f1-score": 0.9004394514551486, "support": 685.0}, "weighted avg": {"precision": 0.9121026492501171, "recall": 0.9007299270072993, "f1-score": 0.9002902883337739, "support": 685.0}}, "confusion_matrix": [[327, 6], [62, 290]], "roc_auc": 0.948603432978433, "training_time": 0.622562, "train_size": 1271, "test_size": 685, "algorithm_type": "Ensemble"}, "AdaBoost": {"accuracy": 0.8934306569343066, "precision": [0.8421052631578947, 0.9573770491803278], "recall": [0.960960960960961, 0.8295454545454546], "f1_score": [0.8976157082748948, 0.8888888888888888], "support": [333, 352], "classification_report": {"0": {"precision": 0.8421052631578947, "recall": 0.960960960960961, "f1-score": 0.8976157082748948, "support": 333.0}, "1": {"precision": 0.9573770491803278, "recall": 0.8295454545454546, "f1-score": 0.8888888888888888, "support": 352.0}, "accuracy": 0.8934306569343066, "macro avg": {"precision": 0.8997411561691113, "recall": 0.8952532077532078, "f1-score": 0.8932522985818918, "support": 685.0}, "weighted avg": {"precision": 0.9013398159752619, "recall": 0.8934306569343066, "f1-score": 0.8931312696998961, "support": 685.0}}, "confusion_matrix": [[320, 13], [60, 292]], "roc_auc": 0.9350600600600601, "training_time": 0.415062, "train_size": 1271, "test_size": 685, "algorithm_type": "Ensemble"}, "AdaBoost_DT": {"accuracy": 0.8671532846715329, "precision": [0.8980263157894737, 0.84251968503937], "recall": [0.8198198198198198, 0.9119318181818182], "f1_score": [0.8571428571428571, 0.8758526603001364], "support": [333, 352], "classification_report": {"0": {"precision": 0.8980263157894737, "recall": 0.8198198198198198, "f1-score": 0.8571428571428571, "support": 333.0}, "1": {"precision": 0.84251968503937, "recall": 0.9119318181818182, "f1-score": 0.8758526603001364, "support": 352.0}, "accuracy": 0.8671532846715329, "macro avg": {"precision": 0.8702730004144219, "recall": 0.865875819000819, "f1-score": 0.8664977587214968, "support": 685.0}, "weighted avg": {"precision": 0.8695032004259167, "recall": 0.8671532846715329, "f1-score": 0.8667572377433861, "support": 685.0}}, "confusion_matrix": [[273, 60], [31, 321]], "roc_auc": 0.9442055692055692, "training_time": 0.761879, "train_size": 1271, "test_size": 685, "algorithm_type": "Decision Tree"}}