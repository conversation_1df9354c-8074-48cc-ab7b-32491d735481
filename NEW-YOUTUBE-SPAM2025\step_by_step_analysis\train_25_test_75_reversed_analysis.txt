TRAIN_25_TEST_75_REVERSED - DE<PERSON>ILED SPLIT ANALYSIS
============================================================
Analysis Date: 2025-06-30 00:39:29

SPLIT CONFIGURATION:
-------------------------
Split Type: Reversed
Training Size: 489 samples
Test Size: 1467 samples
Train/Test Ratio: 0.33

PERFORMANCE OVERVIEW:
-------------------------
Models Tested: 34
Best Accuracy: 0.8896 (88.96%)
Average Accuracy: 0.8516 (85.16%)
Worst Accuracy: 0.8057 (80.57%)
Standard Deviation: 0.0269

TOP 5 PERFORMERS:
--------------------
1. SVM_RBF_Tuned
   Accuracy: 0.8896 (88.96%)
   Spam F1: 0.8910
   Training Time: 0.09s

2. SVM_Linear
   Accuracy: 0.8848 (88.48%)
   Spam F1: 0.8802
   Training Time: 0.07s

3. SVM_Linear_Tuned
   Accuracy: 0.8848 (88.48%)
   Spam F1: 0.8802
   Training Time: 0.08s

4. GradientBoosting
   Accuracy: 0.8814 (88.14%)
   Spam F1: 0.8719
   Training Time: 0.30s

5. SVM_RBF
   Accuracy: 0.8800 (88.00%)
   Spam F1: 0.8804
   Training Time: 0.07s

PERFORMANCE BY ALGORITHM TYPE:
-----------------------------------
Support Vector Machine:
  Models: 6
  Average Accuracy: 0.8773
  Best Accuracy: 0.8896
  Average Training Time: 0.08s

Naive Bayes:
  Models: 5
  Average Accuracy: 0.8472
  Best Accuracy: 0.8691
  Average Training Time: 0.01s

Logistic Regression:
  Models: 6
  Average Accuracy: 0.8711
  Best Accuracy: 0.8780
  Average Training Time: 0.01s

Decision Tree:
  Models: 8
  Average Accuracy: 0.8294
  Best Accuracy: 0.8582
  Average Training Time: 0.08s

Random Forest:
  Models: 7
  Average Accuracy: 0.8365
  Best Accuracy: 0.8630
  Average Training Time: 0.24s

Ensemble:
  Models: 2
  Average Accuracy: 0.8695
  Best Accuracy: 0.8814
  Average Training Time: 0.29s

TRAINING EFFICIENCY:
--------------------
Fastest Training:
  1. NB_Complement: 0.00s (Acc: 0.861)
  2. LR_SAG: 0.00s (Acc: 0.877)
  3. NB_Bernoulli_Tuned: 0.00s (Acc: 0.838)

Slowest Training:
  1. AdaBoost_DT: 0.47s (Acc: 0.855)
  2. RF_Gini: 0.31s (Acc: 0.808)
  3. GradientBoosting: 0.30s (Acc: 0.881)

============================================================