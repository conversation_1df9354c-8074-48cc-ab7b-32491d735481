NAIVE BAYES - DETAILED ANALYSIS
============================================================
Analysis Date: 2025-06-30 00:39:29

OVERVIEW:
--------------------
Total Variants Tested: 5
Total Models Trained: 30
Best Accuracy: 0.9029 (90.29%)
Average Accuracy: 0.8681 (86.81%)
Worst Accuracy: 0.8057 (80.57%)

BEST PERFORMER:
--------------------
Algorithm: NB_Multinomial
Split Type: Original
Split Name: train_70_test_30
Accuracy: 0.9029 (90.29%)
Spam F1-Score: 0.9082
Training Time: 0.02 seconds

VARIANT COMPARISON:
--------------------
NB_Multinomial:
  Average Accuracy: 0.8852
  Best Accuracy: 0.9029
  Consistency (Std): 0.0161
  Average Training Time: 0.02s
  Average Spam F1: 0.8922

NB_Bernoulli:
  Average Accuracy: 0.8328
  Best Accuracy: 0.8467
  Consistency (Std): 0.0156
  Average Training Time: 0.02s
  Average Spam F1: 0.8082

NB_Complement:
  Average Accuracy: 0.8784
  Best Accuracy: 0.8910
  Consistency (Std): 0.0114
  Average Training Time: 0.01s
  Average Spam F1: 0.8785

NB_Multinomial_Tuned:
  Average Accuracy: 0.8834
  Best Accuracy: 0.8937
  Consistency (Std): 0.0098
  Average Training Time: 0.01s
  Average Spam F1: 0.8896

NB_Bernoulli_Tuned:
  Average Accuracy: 0.8607
  Best Accuracy: 0.8756
  Consistency (Std): 0.0165
  Average Training Time: 0.01s
  Average Spam F1: 0.8490

PERFORMANCE BY SPLIT TYPE:
------------------------------
Original Splits:
  Models: 15
  Average Accuracy: 0.8795
  Best Accuracy: 0.9029
  Average Training Time: 0.02s

Reversed Splits:
  Models: 15
  Average Accuracy: 0.8567
  Best Accuracy: 0.8797
  Average Training Time: 0.01s

RECOMMENDATIONS:
--------------------
• Multinomial NB works best for text classification
• Very fast training, suitable for real-time applications
• Consider alpha parameter tuning
• Complement NB can handle imbalanced datasets well

============================================================