TRAIN_30_TEST_70_REVERSED - DETAILED SPLIT ANALYSIS
============================================================
Analysis Date: 2025-06-30 00:39:29

SPLIT CONFIGURATION:
-------------------------
Split Type: Reversed
Training Size: 586 samples
Test Size: 1370 samples
Train/Test Ratio: 0.43

PERFORMANCE OVERVIEW:
-------------------------
Models Tested: 34
Best Accuracy: 0.8920 (89.20%)
Average Accuracy: 0.8603 (86.03%)
Worst Accuracy: 0.8219 (82.19%)
Standard Deviation: 0.0238

TOP 5 PERFORMERS:
--------------------
1. SVM_RBF_Tuned
   Accuracy: 0.8920 (89.20%)
   Spam F1: 0.8934
   Training Time: 0.13s

2. SVM_Linear
   Accuracy: 0.8905 (89.05%)
   Spam F1: 0.8867
   Training Time: 0.11s

3. SVM_Linear_Tuned
   Accuracy: 0.8905 (89.05%)
   Spam F1: 0.8867
   Training Time: 0.10s

4. SVM_RBF
   Accuracy: 0.8898 (88.98%)
   Spam F1: 0.8903
   Training Time: 0.10s

5. SVM_Sigmoid
   Accuracy: 0.8861 (88.61%)
   Spam F1: 0.8809
   Training Time: 0.08s

PERFORMANCE BY ALGORITHM TYPE:
-----------------------------------
Support Vector Machine:
  Models: 6
  Average Accuracy: 0.8826
  Best Accuracy: 0.8920
  Average Training Time: 0.11s

Naive Bayes:
  Models: 5
  Average Accuracy: 0.8591
  Best Accuracy: 0.8788
  Average Training Time: 0.01s

Logistic Regression:
  Models: 6
  Average Accuracy: 0.8764
  Best Accuracy: 0.8854
  Average Training Time: 0.01s

Decision Tree:
  Models: 8
  Average Accuracy: 0.8374
  Best Accuracy: 0.8759
  Average Training Time: 0.08s

Random Forest:
  Models: 7
  Average Accuracy: 0.8491
  Best Accuracy: 0.8723
  Average Training Time: 0.26s

Ensemble:
  Models: 2
  Average Accuracy: 0.8781
  Best Accuracy: 0.8825
  Average Training Time: 0.31s

TRAINING EFFICIENCY:
--------------------
Fastest Training:
  1. NB_Complement: 0.00s (Acc: 0.869)
  2. LR_L2: 0.00s (Acc: 0.885)
  3. NB_Bernoulli_Tuned: 0.00s (Acc: 0.848)

Slowest Training:
  1. AdaBoost_DT: 0.43s (Acc: 0.845)
  2. GradientBoosting: 0.37s (Acc: 0.882)
  3. ExtraTrees: 0.33s (Acc: 0.846)

============================================================