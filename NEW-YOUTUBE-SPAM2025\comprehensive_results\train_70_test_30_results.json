{"SVM_Linear": {"accuracy": 0.9011925042589438, "precision": [0.8603174603174604, 0.9485294117647058], "recall": [0.9508771929824561, 0.8543046357615894], "f1_score": [0.9033333333333333, 0.8989547038327527], "support": [285, 302], "classification_report": {"0": {"precision": 0.8603174603174604, "recall": 0.9508771929824561, "f1-score": 0.9033333333333333, "support": 285.0}, "1": {"precision": 0.9485294117647058, "recall": 0.8543046357615894, "f1-score": 0.8989547038327527, "support": 302.0}, "accuracy": 0.9011925042589438, "macro avg": {"precision": 0.9044234360410831, "recall": 0.9025909143720228, "f1-score": 0.901144018583043, "support": 587.0}, "weighted avg": {"precision": 0.9057007811642545, "recall": 0.9011925042589438, "f1-score": 0.9010806142376341, "support": 587.0}}, "confusion_matrix": [[271, 14], [44, 258]], "roc_auc": 0.9655280585569884, "training_time": 0.372387, "train_size": 1369, "test_size": 587, "algorithm_type": "Support Vector Machine"}, "SVM_Polynomial": {"accuracy": 0.879045996592845, "precision": [0.8794326241134752, 0.8786885245901639], "recall": [0.8701754385964913, 0.8874172185430463], "f1_score": [0.8747795414462081, 0.8830313014827018], "support": [285, 302], "classification_report": {"0": {"precision": 0.8794326241134752, "recall": 0.8701754385964913, "f1-score": 0.8747795414462081, "support": 285.0}, "1": {"precision": 0.8786885245901639, "recall": 0.8874172185430463, "f1-score": 0.8830313014827018, "support": 302.0}, "accuracy": 0.879045996592845, "macro avg": {"precision": 0.8790605743518196, "recall": 0.8787963285697689, "f1-score": 0.8789054214644549, "support": 587.0}, "weighted avg": {"precision": 0.8790497994864904, "recall": 0.879045996592845, "f1-score": 0.8790249103235864, "support": 587.0}}, "confusion_matrix": [[248, 37], [34, 268]], "roc_auc": 0.9404786801440689, "training_time": 0.531368, "train_size": 1369, "test_size": 587, "algorithm_type": "Support Vector Machine"}, "SVM_RBF": {"accuracy": 0.9063032367972743, "precision": [0.8833333333333333, 0.9303135888501742], "recall": [0.9298245614035088, 0.8841059602649006], "f1_score": [0.905982905982906, 0.9066213921901528], "support": [285, 302], "classification_report": {"0": {"precision": 0.8833333333333333, "recall": 0.9298245614035088, "f1-score": 0.905982905982906, "support": 285.0}, "1": {"precision": 0.9303135888501742, "recall": 0.8841059602649006, "f1-score": 0.9066213921901528, "support": 302.0}, "accuracy": 0.9063032367972743, "macro avg": {"precision": 0.9068234610917538, "recall": 0.9069652608342047, "f1-score": 0.9063021490865294, "support": 587.0}, "weighted avg": {"precision": 0.9075037543999191, "recall": 0.9063032367972743, "f1-score": 0.906311394627861, "support": 587.0}}, "confusion_matrix": [[265, 20], [35, 267]], "roc_auc": 0.9511560357848262, "training_time": 0.55546, "train_size": 1369, "test_size": 587, "algorithm_type": "Support Vector Machine"}, "SVM_Sigmoid": {"accuracy": 0.9045996592844975, "precision": [0.8589341692789969, 0.9589552238805971], "recall": [0.9614035087719298, 0.8509933774834437], "f1_score": [0.9072847682119205, 0.9017543859649123], "support": [285, 302], "classification_report": {"0": {"precision": 0.8589341692789969, "recall": 0.9614035087719298, "f1-score": 0.9072847682119205, "support": 285.0}, "1": {"precision": 0.9589552238805971, "recall": 0.8509933774834437, "f1-score": 0.9017543859649123, "support": 302.0}, "accuracy": 0.9045996592844975, "macro avg": {"precision": 0.9089446965797969, "recall": 0.9061984431276868, "f1-score": 0.9045195770884165, "support": 587.0}, "weighted avg": {"precision": 0.9103930423448968, "recall": 0.9045996592844975, "f1-score": 0.9044394948923353, "support": 587.0}}, "confusion_matrix": [[274, 11], [45, 257]], "roc_auc": 0.9642035552457302, "training_time": 0.346543, "train_size": 1369, "test_size": 587, "algorithm_type": "Support Vector Machine"}, "SVM_RBF_Tuned": {"accuracy": 0.9028960817717206, "precision": [0.88, 0.926829268292683], "recall": [0.9263157894736842, 0.8807947019867549], "f1_score": [0.9025641025641026, 0.9032258064516129], "support": [285, 302], "classification_report": {"0": {"precision": 0.88, "recall": 0.9263157894736842, "f1-score": 0.9025641025641026, "support": 285.0}, "1": {"precision": 0.926829268292683, "recall": 0.8807947019867549, "f1-score": 0.9032258064516129, "support": 302.0}, "accuracy": 0.9028960817717206, "macro avg": {"precision": 0.9034146341463415, "recall": 0.9035552457302196, "f1-score": 0.9028949545078577, "support": 587.0}, "weighted avg": {"precision": 0.9040927410977688, "recall": 0.9028960817717206, "f1-score": 0.9029045362506922, "support": 587.0}}, "confusion_matrix": [[264, 21], [36, 266]], "roc_auc": 0.9510049959335425, "training_time": 0.576654, "train_size": 1369, "test_size": 587, "algorithm_type": "Support Vector Machine"}, "SVM_Linear_Tuned": {"accuracy": 0.9011925042589438, "precision": [0.8603174603174604, 0.9485294117647058], "recall": [0.9508771929824561, 0.8543046357615894], "f1_score": [0.9033333333333333, 0.8989547038327527], "support": [285, 302], "classification_report": {"0": {"precision": 0.8603174603174604, "recall": 0.9508771929824561, "f1-score": 0.9033333333333333, "support": 285.0}, "1": {"precision": 0.9485294117647058, "recall": 0.8543046357615894, "f1-score": 0.8989547038327527, "support": 302.0}, "accuracy": 0.9011925042589438, "macro avg": {"precision": 0.9044234360410831, "recall": 0.9025909143720228, "f1-score": 0.901144018583043, "support": 587.0}, "weighted avg": {"precision": 0.9057007811642545, "recall": 0.9011925042589438, "f1-score": 0.9010806142376341, "support": 587.0}}, "confusion_matrix": [[271, 14], [44, 258]], "roc_auc": 0.9655280585569884, "training_time": 0.382698, "train_size": 1369, "test_size": 587, "algorithm_type": "Support Vector Machine"}, "NB_Multinomial": {"accuracy": 0.9028960817717206, "precision": [0.9253731343283582, 0.8840125391849529], "recall": [0.8701754385964913, 0.9337748344370861], "f1_score": [0.8969258589511754, 0.9082125603864735], "support": [285, 302], "classification_report": {"0": {"precision": 0.9253731343283582, "recall": 0.8701754385964913, "f1-score": 0.8969258589511754, "support": 285.0}, "1": {"precision": 0.8840125391849529, "recall": 0.9337748344370861, "f1-score": 0.9082125603864735, "support": 302.0}, "accuracy": 0.9028960817717206, "macro avg": {"precision": 0.9046928367566556, "recall": 0.9019751365167887, "f1-score": 0.9025692096688245, "support": 587.0}, "weighted avg": {"precision": 0.904093918428344, "recall": 0.9028960817717206, "f1-score": 0.9027326457202725, "support": 587.0}}, "confusion_matrix": [[248, 37], [20, 282]], "roc_auc": 0.9590798187521784, "training_time": 0.021128, "train_size": 1369, "test_size": 587, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli": {"accuracy": 0.8432708688245315, "precision": [0.7643835616438356, 0.972972972972973], "recall": [0.9789473684210527, 0.7152317880794702], "f1_score": [0.8584615384615385, 0.8244274809160306], "support": [285, 302], "classification_report": {"0": {"precision": 0.7643835616438356, "recall": 0.9789473684210527, "f1-score": 0.8584615384615385, "support": 285.0}, "1": {"precision": 0.972972972972973, "recall": 0.7152317880794702, "f1-score": 0.8244274809160306, "support": 302.0}, "accuracy": 0.8432708688245315, "macro avg": {"precision": 0.8686782673084044, "recall": 0.8470895782502614, "f1-score": 0.8414445096887846, "support": 587.0}, "weighted avg": {"precision": 0.8716987272680256, "recall": 0.8432708688245315, "f1-score": 0.8409516826204083, "support": 587.0}}, "confusion_matrix": [[279, 6], [86, 216]], "roc_auc": 0.9444347624026955, "training_time": 0.028851, "train_size": 1369, "test_size": 587, "algorithm_type": "<PERSON><PERSON>"}, "NB_Complement": {"accuracy": 0.8909710391822828, "precision": [0.8695652173913043, 0.9131944444444444], "recall": [0.9122807017543859, 0.8708609271523179], "f1_score": [0.8904109589041096, 0.8915254237288136], "support": [285, 302], "classification_report": {"0": {"precision": 0.8695652173913043, "recall": 0.9122807017543859, "f1-score": 0.8904109589041096, "support": 285.0}, "1": {"precision": 0.9131944444444444, "recall": 0.8708609271523179, "f1-score": 0.8915254237288136, "support": 302.0}, "accuracy": 0.8909710391822828, "macro avg": {"precision": 0.8913798309178744, "recall": 0.8915708144533518, "f1-score": 0.8909681913164615, "support": 587.0}, "weighted avg": {"precision": 0.8920115999637886, "recall": 0.8909710391822828, "f1-score": 0.8909843292227819, "support": 587.0}}, "confusion_matrix": [[260, 25], [39, 263]], "roc_auc": 0.9590798187521784, "training_time": 0.022617, "train_size": 1369, "test_size": 587, "algorithm_type": "<PERSON><PERSON>"}, "NB_Multinomial_Tuned": {"accuracy": 0.8926746166950597, "precision": [0.9111111111111111, 0.8769716088328076], "recall": [0.8631578947368421, 0.9205298013245033], "f1_score": [0.8864864864864865, 0.8982229402261712], "support": [285, 302], "classification_report": {"0": {"precision": 0.9111111111111111, "recall": 0.8631578947368421, "f1-score": 0.8864864864864865, "support": 285.0}, "1": {"precision": 0.8769716088328076, "recall": 0.9205298013245033, "f1-score": 0.8982229402261712, "support": 302.0}, "accuracy": 0.8926746166950597, "macro avg": {"precision": 0.8940413599719593, "recall": 0.8918438480306727, "f1-score": 0.8923547133563289, "support": 587.0}, "weighted avg": {"precision": 0.8935470060207403, "recall": 0.8926746166950597, "f1-score": 0.8925246620050297, "support": 587.0}}, "confusion_matrix": [[246, 39], [24, 278]], "roc_auc": 0.9542697804112932, "training_time": 0.020655, "train_size": 1369, "test_size": 587, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli_Tuned": {"accuracy": 0.8756388415672913, "precision": [0.8173652694610778, 0.9525691699604744], "recall": [0.9578947368421052, 0.7980132450331126], "f1_score": [0.8820678513731826, 0.8684684684684685], "support": [285, 302], "classification_report": {"0": {"precision": 0.8173652694610778, "recall": 0.9578947368421052, "f1-score": 0.8820678513731826, "support": 285.0}, "1": {"precision": 0.9525691699604744, "recall": 0.7980132450331126, "f1-score": 0.8684684684684685, "support": 302.0}, "accuracy": 0.8756388415672913, "macro avg": {"precision": 0.8849672197107761, "recall": 0.8779539909376088, "f1-score": 0.8752681599208255, "support": 587.0}, "weighted avg": {"precision": 0.8869250274692851, "recall": 0.8756388415672913, "f1-score": 0.8750712352961405, "support": 587.0}}, "confusion_matrix": [[273, 12], [61, 241]], "roc_auc": 0.9344312768676658, "training_time": 0.004915, "train_size": 1369, "test_size": 587, "algorithm_type": "<PERSON><PERSON>"}, "LR_L1": {"accuracy": 0.8773424190800682, "precision": [0.8160237388724035, 0.96], "recall": [0.9649122807017544, 0.7947019867549668], "f1_score": [0.8842443729903537, 0.8695652173913043], "support": [285, 302], "classification_report": {"0": {"precision": 0.8160237388724035, "recall": 0.9649122807017544, "f1-score": 0.8842443729903537, "support": 285.0}, "1": {"precision": 0.96, "recall": 0.7947019867549668, "f1-score": 0.8695652173913043, "support": 302.0}, "accuracy": 0.8773424190800682, "macro avg": {"precision": 0.8880118694362018, "recall": 0.8798071337283606, "f1-score": 0.8769047951908291, "support": 587.0}, "weighted avg": {"precision": 0.8900967045632623, "recall": 0.8773424190800682, "f1-score": 0.8766922350160558, "support": 587.0}}, "confusion_matrix": [[275, 10], [62, 240]], "roc_auc": 0.941129313349599, "training_time": 0.015019, "train_size": 1369, "test_size": 587, "algorithm_type": "Logistic Regression"}, "LR_L2": {"accuracy": 0.9045996592844975, "precision": [0.861198738170347, 0.9555555555555556], "recall": [0.9578947368421052, 0.8543046357615894], "f1_score": [0.9069767441860465, 0.9020979020979021], "support": [285, 302], "classification_report": {"0": {"precision": 0.861198738170347, "recall": 0.9578947368421052, "f1-score": 0.9069767441860465, "support": 285.0}, "1": {"precision": 0.9555555555555556, "recall": 0.8543046357615894, "f1-score": 0.9020979020979021, "support": 302.0}, "accuracy": 0.9045996592844975, "macro avg": {"precision": 0.9083771468629513, "recall": 0.9060996863018473, "f1-score": 0.9045373231419742, "support": 587.0}, "weighted avg": {"precision": 0.9097434721572856, "recall": 0.9045996592844975, "f1-score": 0.9044666755137813, "support": 587.0}}, "confusion_matrix": [[273, 12], [44, 258]], "roc_auc": 0.9576275124898338, "training_time": 0.021481, "train_size": 1369, "test_size": 587, "algorithm_type": "Logistic Regression"}, "LR_ElasticNet": {"accuracy": 0.8807495741056218, "precision": [0.8267477203647416, 0.9496124031007752], "recall": [0.9543859649122807, 0.8112582781456954], "f1_score": [0.8859934853420195, 0.875], "support": [285, 302], "classification_report": {"0": {"precision": 0.8267477203647416, "recall": 0.9543859649122807, "f1-score": 0.8859934853420195, "support": 285.0}, "1": {"precision": 0.9496124031007752, "recall": 0.8112582781456954, "f1-score": 0.875, "support": 302.0}, "accuracy": 0.8807495741056218, "macro avg": {"precision": 0.8881800617327584, "recall": 0.882822121528988, "f1-score": 0.8804967426710097, "support": 587.0}, "weighted avg": {"precision": 0.8899591925730588, "recall": 0.8807495741056218, "f1-score": 0.8803375525084762, "support": 587.0}}, "confusion_matrix": [[272, 13], [57, 245]], "roc_auc": 0.9497153479725804, "training_time": 0.064227, "train_size": 1369, "test_size": 587, "algorithm_type": "Logistic Regression"}, "LR_LBFGS": {"accuracy": 0.9028960817717206, "precision": [0.8607594936708861, 0.9520295202952029], "recall": [0.9543859649122807, 0.8543046357615894], "f1_score": [0.9051580698835274, 0.900523560209424], "support": [285, 302], "classification_report": {"0": {"precision": 0.8607594936708861, "recall": 0.9543859649122807, "f1-score": 0.9051580698835274, "support": 285.0}, "1": {"precision": 0.9520295202952029, "recall": 0.8543046357615894, "f1-score": 0.900523560209424, "support": 302.0}, "accuracy": 0.9028960817717206, "macro avg": {"precision": 0.9063945069830446, "recall": 0.904345300336935, "f1-score": 0.9028408150464757, "support": 587.0}, "weighted avg": {"precision": 0.9077161342851002, "recall": 0.9028960817717206, "f1-score": 0.9027737054515353, "support": 587.0}}, "confusion_matrix": [[272, 13], [44, 258]], "roc_auc": 0.9578714999419077, "training_time": 0.036061, "train_size": 1369, "test_size": 587, "algorithm_type": "Logistic Regression"}, "LR_SAG": {"accuracy": 0.9045996592844975, "precision": [0.861198738170347, 0.9555555555555556], "recall": [0.9578947368421052, 0.8543046357615894], "f1_score": [0.9069767441860465, 0.9020979020979021], "support": [285, 302], "classification_report": {"0": {"precision": 0.861198738170347, "recall": 0.9578947368421052, "f1-score": 0.9069767441860465, "support": 285.0}, "1": {"precision": 0.9555555555555556, "recall": 0.8543046357615894, "f1-score": 0.9020979020979021, "support": 302.0}, "accuracy": 0.9045996592844975, "macro avg": {"precision": 0.9083771468629513, "recall": 0.9060996863018473, "f1-score": 0.9045373231419742, "support": 587.0}, "weighted avg": {"precision": 0.9097434721572856, "recall": 0.9045996592844975, "f1-score": 0.9044666755137813, "support": 587.0}}, "confusion_matrix": [[273, 12], [44, 258]], "roc_auc": 0.9576158940397352, "training_time": 0.022879, "train_size": 1369, "test_size": 587, "algorithm_type": "Logistic Regression"}, "LR_SAGA": {"accuracy": 0.9045996592844975, "precision": [0.861198738170347, 0.9555555555555556], "recall": [0.9578947368421052, 0.8543046357615894], "f1_score": [0.9069767441860465, 0.9020979020979021], "support": [285, 302], "classification_report": {"0": {"precision": 0.861198738170347, "recall": 0.9578947368421052, "f1-score": 0.9069767441860465, "support": 285.0}, "1": {"precision": 0.9555555555555556, "recall": 0.8543046357615894, "f1-score": 0.9020979020979021, "support": 302.0}, "accuracy": 0.9045996592844975, "macro avg": {"precision": 0.9083771468629513, "recall": 0.9060996863018473, "f1-score": 0.9045373231419742, "support": 587.0}, "weighted avg": {"precision": 0.9097434721572856, "recall": 0.9045996592844975, "f1-score": 0.9044666755137813, "support": 587.0}}, "confusion_matrix": [[273, 12], [44, 258]], "roc_auc": 0.9577553154409201, "training_time": 0.029609, "train_size": 1369, "test_size": 587, "algorithm_type": "Logistic Regression"}, "DT_Gini": {"accuracy": 0.868824531516184, "precision": [0.9227642276422764, 0.8299120234604106], "recall": [0.7964912280701755, 0.9370860927152318], "f1_score": [0.8549905838041432, 0.880248833592535], "support": [285, 302], "classification_report": {"0": {"precision": 0.9227642276422764, "recall": 0.7964912280701755, "f1-score": 0.8549905838041432, "support": 285.0}, "1": {"precision": 0.8299120234604106, "recall": 0.9370860927152318, "f1-score": 0.880248833592535, "support": 302.0}, "accuracy": 0.868824531516184, "macro avg": {"precision": 0.8763381255513435, "recall": 0.8667886603927036, "f1-score": 0.867619708698339, "support": 587.0}, "weighted avg": {"precision": 0.8749935876713676, "recall": 0.868824531516184, "f1-score": 0.8679854584823277, "support": 587.0}}, "confusion_matrix": [[227, 58], [19, 283]], "roc_auc": 0.9320959683978157, "training_time": 0.077057, "train_size": 1369, "test_size": 587, "algorithm_type": "Decision Tree"}, "DT_Entropy": {"accuracy": 0.858603066439523, "precision": [0.907258064516129, 0.8230088495575221], "recall": [0.7894736842105263, 0.9238410596026491], "f1_score": [0.8442776735459663, 0.8705148205928237], "support": [285, 302], "classification_report": {"0": {"precision": 0.907258064516129, "recall": 0.7894736842105263, "f1-score": 0.8442776735459663, "support": 285.0}, "1": {"precision": 0.8230088495575221, "recall": 0.9238410596026491, "f1-score": 0.8705148205928237, "support": 302.0}, "accuracy": 0.858603066439523, "macro avg": {"precision": 0.8651334570368255, "recall": 0.8566573719065878, "f1-score": 0.857396247069395, "support": 587.0}, "weighted avg": {"precision": 0.8639134939582085, "recall": 0.858603066439523, "f1-score": 0.8577761716859167, "support": 587.0}}, "confusion_matrix": [[225, 60], [23, 279]], "roc_auc": 0.9209829208783548, "training_time": 0.070655, "train_size": 1369, "test_size": 587, "algorithm_type": "Decision Tree"}, "DT_Log_Loss": {"accuracy": 0.858603066439523, "precision": [0.907258064516129, 0.8230088495575221], "recall": [0.7894736842105263, 0.9238410596026491], "f1_score": [0.8442776735459663, 0.8705148205928237], "support": [285, 302], "classification_report": {"0": {"precision": 0.907258064516129, "recall": 0.7894736842105263, "f1-score": 0.8442776735459663, "support": 285.0}, "1": {"precision": 0.8230088495575221, "recall": 0.9238410596026491, "f1-score": 0.8705148205928237, "support": 302.0}, "accuracy": 0.858603066439523, "macro avg": {"precision": 0.8651334570368255, "recall": 0.8566573719065878, "f1-score": 0.857396247069395, "support": 587.0}, "weighted avg": {"precision": 0.8639134939582085, "recall": 0.858603066439523, "f1-score": 0.8577761716859167, "support": 587.0}}, "confusion_matrix": [[225, 60], [23, 279]], "roc_auc": 0.9209829208783548, "training_time": 0.085074, "train_size": 1369, "test_size": 587, "algorithm_type": "Decision Tree"}, "DT_Gini_Pruned": {"accuracy": 0.8824531516183987, "precision": [0.8121387283236994, 0.983402489626556], "recall": [0.9859649122807017, 0.7847682119205298], "f1_score": [0.8906497622820919, 0.8729281767955801], "support": [285, 302], "classification_report": {"0": {"precision": 0.8121387283236994, "recall": 0.9859649122807017, "f1-score": 0.8906497622820919, "support": 285.0}, "1": {"precision": 0.983402489626556, "recall": 0.7847682119205298, "f1-score": 0.8729281767955801, "support": 302.0}, "accuracy": 0.8824531516183987, "macro avg": {"precision": 0.8977706089751276, "recall": 0.8853665621006157, "f1-score": 0.881788969538836, "support": 587.0}, "weighted avg": {"precision": 0.9002505782614553, "recall": 0.8824531516183987, "f1-score": 0.8815323537353686, "support": 587.0}}, "confusion_matrix": [[281, 4], [65, 237]], "roc_auc": 0.8841117694899501, "training_time": 0.036842, "train_size": 1369, "test_size": 587, "algorithm_type": "Decision Tree"}, "DT_Entropy_Pruned": {"accuracy": 0.8671209540034072, "precision": [0.7899159663865546, 0.9869565217391304], "recall": [0.9894736842105263, 0.7516556291390728], "f1_score": [0.8785046728971962, 0.8533834586466166], "support": [285, 302], "classification_report": {"0": {"precision": 0.7899159663865546, "recall": 0.9894736842105263, "f1-score": 0.8785046728971962, "support": 285.0}, "1": {"precision": 0.9869565217391304, "recall": 0.7516556291390728, "f1-score": 0.8533834586466166, "support": 302.0}, "accuracy": 0.8671209540034072, "macro avg": {"precision": 0.8884362440628425, "recall": 0.8705646566747995, "f1-score": 0.8659440657719064, "support": 587.0}, "weighted avg": {"precision": 0.891289471866074, "recall": 0.8671209540034072, "f1-score": 0.8655803003185334, "support": 587.0}}, "confusion_matrix": [[282, 3], [75, 227]], "roc_auc": 0.8784768211920528, "training_time": 0.047361, "train_size": 1369, "test_size": 587, "algorithm_type": "Decision Tree"}, "DT_Best_First": {"accuracy": 0.868824531516184, "precision": [0.9227642276422764, 0.8299120234604106], "recall": [0.7964912280701755, 0.9370860927152318], "f1_score": [0.8549905838041432, 0.880248833592535], "support": [285, 302], "classification_report": {"0": {"precision": 0.9227642276422764, "recall": 0.7964912280701755, "f1-score": 0.8549905838041432, "support": 285.0}, "1": {"precision": 0.8299120234604106, "recall": 0.9370860927152318, "f1-score": 0.880248833592535, "support": 302.0}, "accuracy": 0.868824531516184, "macro avg": {"precision": 0.8763381255513435, "recall": 0.8667886603927036, "f1-score": 0.867619708698339, "support": 587.0}, "weighted avg": {"precision": 0.8749935876713676, "recall": 0.868824531516184, "f1-score": 0.8679854584823277, "support": 587.0}}, "confusion_matrix": [[227, 58], [19, 283]], "roc_auc": 0.9320959683978157, "training_time": 0.083609, "train_size": 1369, "test_size": 587, "algorithm_type": "Decision Tree"}, "DT_Random_Split": {"accuracy": 0.8654173764906303, "precision": [0.912, 0.8308605341246291], "recall": [0.8, 0.9271523178807947], "f1_score": [0.8523364485981308, 0.8763693270735524], "support": [285, 302], "classification_report": {"0": {"precision": 0.912, "recall": 0.8, "f1-score": 0.8523364485981308, "support": 285.0}, "1": {"precision": 0.8308605341246291, "recall": 0.9271523178807947, "f1-score": 0.8763693270735524, "support": 302.0}, "accuracy": 0.8654173764906303, "macro avg": {"precision": 0.8714302670623146, "recall": 0.8635761589403974, "f1-score": 0.8643528878358416, "support": 587.0}, "weighted avg": {"precision": 0.8702553344218705, "recall": 0.8654173764906303, "f1-score": 0.8647008937422148, "support": 587.0}}, "confusion_matrix": [[228, 57], [22, 280]], "roc_auc": 0.9275763913093994, "training_time": 0.06621, "train_size": 1369, "test_size": 587, "algorithm_type": "Decision Tree"}, "RF_Gini": {"accuracy": 0.889267461669506, "precision": [0.9296875, 0.8580060422960725], "recall": [0.8350877192982457, 0.9403973509933775], "f1_score": [0.8798521256931608, 0.8973143759873617], "support": [285, 302], "classification_report": {"0": {"precision": 0.9296875, "recall": 0.8350877192982457, "f1-score": 0.8798521256931608, "support": 285.0}, "1": {"precision": 0.8580060422960725, "recall": 0.9403973509933775, "f1-score": 0.8973143759873617, "support": 302.0}, "accuracy": 0.889267461669506, "macro avg": {"precision": 0.8938467711480362, "recall": 0.8877425351458116, "f1-score": 0.8885832508402614, "support": 587.0}, "weighted avg": {"precision": 0.8928087943329027, "recall": 0.889267461669506, "f1-score": 0.8888361113641126, "support": 587.0}}, "confusion_matrix": [[238, 47], [18, 284]], "roc_auc": 0.9572731497618218, "training_time": 0.567947, "train_size": 1369, "test_size": 587, "algorithm_type": "Random Forest"}, "RF_Entropy": {"accuracy": 0.8807495741056218, "precision": [0.9282868525896414, 0.8452380952380952], "recall": [0.8175438596491228, 0.9403973509933775], "f1_score": [0.8694029850746269, 0.890282131661442], "support": [285, 302], "classification_report": {"0": {"precision": 0.9282868525896414, "recall": 0.8175438596491228, "f1-score": 0.8694029850746269, "support": 285.0}, "1": {"precision": 0.8452380952380952, "recall": 0.9403973509933775, "f1-score": 0.890282131661442, "support": 302.0}, "accuracy": 0.8807495741056218, "macro avg": {"precision": 0.8867624739138683, "recall": 0.8789706053212502, "f1-score": 0.8798425583680345, "support": 587.0}, "weighted avg": {"precision": 0.885559893952219, "recall": 0.8807495741056218, "f1-score": 0.8801448969472302, "support": 587.0}}, "confusion_matrix": [[233, 52], [18, 284]], "roc_auc": 0.9574648541884513, "training_time": 0.617227, "train_size": 1369, "test_size": 587, "algorithm_type": "Random Forest"}, "RF_Log_Loss": {"accuracy": 0.8807495741056218, "precision": [0.9282868525896414, 0.8452380952380952], "recall": [0.8175438596491228, 0.9403973509933775], "f1_score": [0.8694029850746269, 0.890282131661442], "support": [285, 302], "classification_report": {"0": {"precision": 0.9282868525896414, "recall": 0.8175438596491228, "f1-score": 0.8694029850746269, "support": 285.0}, "1": {"precision": 0.8452380952380952, "recall": 0.9403973509933775, "f1-score": 0.890282131661442, "support": 302.0}, "accuracy": 0.8807495741056218, "macro avg": {"precision": 0.8867624739138683, "recall": 0.8789706053212502, "f1-score": 0.8798425583680345, "support": 587.0}, "weighted avg": {"precision": 0.885559893952219, "recall": 0.8807495741056218, "f1-score": 0.8801448969472302, "support": 587.0}}, "confusion_matrix": [[233, 52], [18, 284]], "roc_auc": 0.9574648541884513, "training_time": 0.655725, "train_size": 1369, "test_size": 587, "algorithm_type": "Random Forest"}, "RF_Large": {"accuracy": 0.8739352640545145, "precision": [0.8075801749271136, 0.9672131147540983], "recall": [0.9719298245614035, 0.7814569536423841], "f1_score": [0.8821656050955414, 0.8644688644688645], "support": [285, 302], "classification_report": {"0": {"precision": 0.8075801749271136, "recall": 0.9719298245614035, "f1-score": 0.8821656050955414, "support": 285.0}, "1": {"precision": 0.9672131147540983, "recall": 0.7814569536423841, "f1-score": 0.8644688644688645, "support": 302.0}, "accuracy": 0.8739352640545145, "macro avg": {"precision": 0.887396644840606, "recall": 0.8766933891018938, "f1-score": 0.8733172347822029, "support": 587.0}, "weighted avg": {"precision": 0.8897081950766016, "recall": 0.8739352640545145, "f1-score": 0.873060978742464, "support": 587.0}}, "confusion_matrix": [[277, 8], [66, 236]], "roc_auc": 0.9511792726850237, "training_time": 0.495378, "train_size": 1369, "test_size": 587, "algorithm_type": "Random Forest"}, "RF_Small": {"accuracy": 0.868824531516184, "precision": [0.804093567251462, 0.9591836734693877], "recall": [0.9649122807017544, 0.7781456953642384], "f1_score": [0.8771929824561403, 0.8592321755027422], "support": [285, 302], "classification_report": {"0": {"precision": 0.804093567251462, "recall": 0.9649122807017544, "f1-score": 0.8771929824561403, "support": 285.0}, "1": {"precision": 0.9591836734693877, "recall": 0.7781456953642384, "f1-score": 0.8592321755027422, "support": 302.0}, "accuracy": 0.868824531516184, "macro avg": {"precision": 0.8816386203604248, "recall": 0.8715289880329964, "f1-score": 0.8682125789794413, "support": 587.0}, "weighted avg": {"precision": 0.8838843885083845, "recall": 0.868824531516184, "f1-score": 0.8679524991513256, "support": 587.0}}, "confusion_matrix": [[275, 10], [67, 235]], "roc_auc": 0.9438654583478563, "training_time": 0.107471, "train_size": 1369, "test_size": 587, "algorithm_type": "Random Forest"}, "ExtraTrees": {"accuracy": 0.9011925042589438, "precision": [0.9315589353612167, 0.8765432098765432], "recall": [0.8596491228070176, 0.9403973509933775], "f1_score": [0.8941605839416058, 0.9073482428115016], "support": [285, 302], "classification_report": {"0": {"precision": 0.9315589353612167, "recall": 0.8596491228070176, "f1-score": 0.8941605839416058, "support": 285.0}, "1": {"precision": 0.8765432098765432, "recall": 0.9403973509933775, "f1-score": 0.9073482428115016, "support": 302.0}, "accuracy": 0.9011925042589438, "macro avg": {"precision": 0.90405107261888, "recall": 0.9000232369001975, "f1-score": 0.9007544133765537, "support": 587.0}, "weighted avg": {"precision": 0.9032544224202093, "recall": 0.9011925042589438, "f1-score": 0.9009453760688776, "support": 587.0}}, "confusion_matrix": [[245, 40], [18, 284]], "roc_auc": 0.9631927500871384, "training_time": 0.794376, "train_size": 1369, "test_size": 587, "algorithm_type": "Random Forest"}, "ExtraTrees_Large": {"accuracy": 0.879045996592845, "precision": [0.8147058823529412, 0.9676113360323887], "recall": [0.9719298245614035, 0.7913907284768212], "f1_score": [0.8864, 0.8706739526411658], "support": [285, 302], "classification_report": {"0": {"precision": 0.8147058823529412, "recall": 0.9719298245614035, "f1-score": 0.8864, "support": 285.0}, "1": {"precision": 0.9676113360323887, "recall": 0.7913907284768212, "f1-score": 0.8706739526411658, "support": 302.0}, "accuracy": 0.879045996592845, "macro avg": {"precision": 0.8911586091926649, "recall": 0.8816602765191124, "f1-score": 0.8785369763205828, "support": 587.0}, "weighted avg": {"precision": 0.8933727426786536, "recall": 0.879045996592845, "f1-score": 0.8783092567250972, "support": 587.0}}, "confusion_matrix": [[277, 8], [63, 239]], "roc_auc": 0.9474381317532241, "training_time": 0.350631, "train_size": 1369, "test_size": 587, "algorithm_type": "Random Forest"}, "GradientBoosting": {"accuracy": 0.8977853492333902, "precision": [0.8299120234604106, 0.991869918699187], "recall": [0.9929824561403509, 0.8079470198675497], "f1_score": [0.9041533546325878, 0.8905109489051095], "support": [285, 302], "classification_report": {"0": {"precision": 0.8299120234604106, "recall": 0.9929824561403509, "f1-score": 0.9041533546325878, "support": 285.0}, "1": {"precision": 0.991869918699187, "recall": 0.8079470198675497, "f1-score": 0.8905109489051095, "support": 302.0}, "accuracy": 0.8977853492333902, "macro avg": {"precision": 0.9108909710797988, "recall": 0.9004647380039503, "f1-score": 0.8973321517688486, "support": 587.0}, "weighted avg": {"precision": 0.913236187620735, "recall": 0.8977853492333902, "f1-score": 0.8971346041560998, "support": 587.0}}, "confusion_matrix": [[283, 2], [58, 244]], "roc_auc": 0.9470837690252119, "training_time": 0.732246, "train_size": 1369, "test_size": 587, "algorithm_type": "Ensemble"}, "AdaBoost": {"accuracy": 0.8841567291311755, "precision": [0.8359133126934984, 0.9431818181818182], "recall": [0.9473684210526315, 0.8245033112582781], "f1_score": [0.8881578947368421, 0.8798586572438163], "support": [285, 302], "classification_report": {"0": {"precision": 0.8359133126934984, "recall": 0.9473684210526315, "f1-score": 0.8881578947368421, "support": 285.0}, "1": {"precision": 0.9431818181818182, "recall": 0.8245033112582781, "f1-score": 0.8798586572438163, "support": 302.0}, "accuracy": 0.8841567291311755, "macro avg": {"precision": 0.8895475654376583, "recall": 0.8859358661554548, "f1-score": 0.8840082759903292, "support": 587.0}, "weighted avg": {"precision": 0.8911008572547806, "recall": 0.8841567291311755, "f1-score": 0.8838880996382155, "support": 587.0}}, "confusion_matrix": [[270, 15], [53, 249]], "roc_auc": 0.9373242709422562, "training_time": 0.431842, "train_size": 1369, "test_size": 587, "algorithm_type": "Ensemble"}, "AdaBoost_DT": {"accuracy": 0.8654173764906303, "precision": [0.90234375, 0.8368580060422961], "recall": [0.8105263157894737, 0.9172185430463576], "f1_score": [0.8539741219963032, 0.8751974723538705], "support": [285, 302], "classification_report": {"0": {"precision": 0.90234375, "recall": 0.8105263157894737, "f1-score": 0.8539741219963032, "support": 285.0}, "1": {"precision": 0.8368580060422961, "recall": 0.9172185430463576, "f1-score": 0.8751974723538705, "support": 302.0}, "accuracy": 0.8654173764906303, "macro avg": {"precision": 0.869600878021148, "recall": 0.8638724294179156, "f1-score": 0.8645857971750868, "support": 587.0}, "weighted avg": {"precision": 0.8686526176742307, "recall": 0.8654173764906303, "f1-score": 0.8648931199656138, "support": 587.0}}, "confusion_matrix": [[231, 54], [25, 277]], "roc_auc": 0.9522597885442082, "training_time": 0.885119, "train_size": 1369, "test_size": 587, "algorithm_type": "Decision Tree"}}