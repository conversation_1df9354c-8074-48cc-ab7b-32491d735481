TRAIN_35_TEST_65_REVERSED - DE<PERSON>ILED SPLIT ANALYSIS
============================================================
Analysis Date: 2025-06-30 00:39:29

SPLIT CONFIGURATION:
-------------------------
Split Type: Reversed
Training Size: 684 samples
Test Size: 1272 samples
Train/Test Ratio: 0.54

PERFORMANCE OVERVIEW:
-------------------------
Models Tested: 34
Best Accuracy: 0.8970 (89.70%)
Average Accuracy: 0.8680 (86.80%)
Worst Accuracy: 0.8302 (83.02%)
Standard Deviation: 0.0217

TOP 5 PERFORMERS:
--------------------
1. SVM_RBF_Tuned
   Accuracy: 0.8970 (89.70%)
   Spam F1: 0.8984
   Training Time: 0.17s

2. SVM_Linear
   Accuracy: 0.8962 (89.62%)
   Spam F1: 0.8932
   Training Time: 0.11s

3. SVM_Linear_Tuned
   Accuracy: 0.8962 (89.62%)
   Spam F1: 0.8932
   Training Time: 0.12s

4. SVM_RBF
   Accuracy: 0.8954 (89.54%)
   Spam F1: 0.8965
   Training Time: 0.15s

5. SVM_Sigmoid
   Accuracy: 0.8947 (89.47%)
   Spam F1: 0.8905
   Training Time: 0.12s

PERFORMANCE BY ALGORITHM TYPE:
-----------------------------------
Support Vector Machine:
  Models: 6
  Average Accuracy: 0.8898
  Best Accuracy: 0.8970
  Average Training Time: 0.14s

Naive Bayes:
  Models: 5
  Average Accuracy: 0.8637
  Best Accuracy: 0.8797
  Average Training Time: 0.01s

Logistic Regression:
  Models: 6
  Average Accuracy: 0.8827
  Best Accuracy: 0.8931
  Average Training Time: 0.02s

Decision Tree:
  Models: 8
  Average Accuracy: 0.8456
  Best Accuracy: 0.8608
  Average Training Time: 0.08s

Random Forest:
  Models: 7
  Average Accuracy: 0.8616
  Best Accuracy: 0.8766
  Average Training Time: 0.27s

Ensemble:
  Models: 2
  Average Accuracy: 0.8805
  Best Accuracy: 0.8829
  Average Training Time: 0.33s

TRAINING EFFICIENCY:
--------------------
Fastest Training:
  1. LR_L2: 0.00s (Acc: 0.892)
  2. LR_SAGA: 0.00s (Acc: 0.892)
  3. NB_Multinomial_Tuned: 0.00s (Acc: 0.877)

Slowest Training:
  1. AdaBoost_DT: 0.48s (Acc: 0.861)
  2. GradientBoosting: 0.39s (Acc: 0.883)
  3. ExtraTrees: 0.35s (Acc: 0.869)

============================================================