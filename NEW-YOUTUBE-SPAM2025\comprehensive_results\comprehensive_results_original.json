{"train_75_test_25": {"SVM_Linear": {"accuracy": 0.9038854805725971, "precision": [0.8715953307392996, 0.9396551724137931], "recall": [0.9411764705882353, 0.8685258964143426], "f1_score": [0.9050505050505051, 0.9026915113871635], "support": [238, 251], "classification_report": {"0": {"precision": 0.8715953307392996, "recall": 0.9411764705882353, "f1-score": 0.9050505050505051, "support": 238.0}, "1": {"precision": 0.9396551724137931, "recall": 0.8685258964143426, "f1-score": 0.9026915113871635, "support": 251.0}, "accuracy": 0.9038854805725971, "macro avg": {"precision": 0.9056252515765464, "recall": 0.9048511835012889, "f1-score": 0.9038710082188344, "support": 489.0}, "weighted avg": {"precision": 0.9065299324986, "recall": 0.9038854805725971, "f1-score": 0.9038396514523482, "support": 489.0}}, "confusion_matrix": [[224, 14], [33, 218]], "roc_auc": 0.9646288794402224, "training_time": 0.410989, "train_size": 1467, "test_size": 489, "algorithm_type": "Support Vector Machine"}, "SVM_Polynomial": {"accuracy": 0.8834355828220859, "precision": [0.8851063829787233, 0.8818897637795275], "recall": [0.8739495798319328, 0.8924302788844621], "f1_score": [0.879492600422833, 0.8871287128712871], "support": [238, 251], "classification_report": {"0": {"precision": 0.8851063829787233, "recall": 0.8739495798319328, "f1-score": 0.879492600422833, "support": 238.0}, "1": {"precision": 0.8818897637795275, "recall": 0.8924302788844621, "f1-score": 0.8871287128712871, "support": 251.0}, "accuracy": 0.8834355828220859, "macro avg": {"precision": 0.8834980733791254, "recall": 0.8831899293581975, "f1-score": 0.8833106566470601, "support": 489.0}, "weighted avg": {"precision": 0.8834553166822036, "recall": 0.8834355828220859, "f1-score": 0.8834121591642685, "support": 489.0}}, "confusion_matrix": [[208, 30], [27, 224]], "roc_auc": 0.9406491680337474, "training_time": 0.707755, "train_size": 1467, "test_size": 489, "algorithm_type": "Support Vector Machine"}, "SVM_RBF": {"accuracy": 0.918200408997955, "precision": [0.9090909090909091, 0.9271255060728745], "recall": [0.9243697478991597, 0.9123505976095617], "f1_score": [0.9166666666666666, 0.9196787148594378], "support": [238, 251], "classification_report": {"0": {"precision": 0.9090909090909091, "recall": 0.9243697478991597, "f1-score": 0.9166666666666666, "support": 238.0}, "1": {"precision": 0.9271255060728745, "recall": 0.9123505976095617, "f1-score": 0.9196787148594378, "support": 251.0}, "accuracy": 0.918200408997955, "macro avg": {"precision": 0.9181082075818918, "recall": 0.9183601727543607, "f1-score": 0.9181726907630522, "support": 489.0}, "weighted avg": {"precision": 0.9183479312636561, "recall": 0.918200408997955, "f1-score": 0.9182127282134673, "support": 489.0}}, "confusion_matrix": [[220, 18], [22, 229]], "roc_auc": 0.9517643710870803, "training_time": 0.659157, "train_size": 1467, "test_size": 489, "algorithm_type": "Support Vector Machine"}, "SVM_Sigmoid": {"accuracy": 0.9120654396728016, "precision": [0.8735632183908046, 0.956140350877193], "recall": [0.957983193277311, 0.8685258964143426], "f1_score": [0.9138276553106213, 0.9102296450939458], "support": [238, 251], "classification_report": {"0": {"precision": 0.8735632183908046, "recall": 0.957983193277311, "f1-score": 0.9138276553106213, "support": 238.0}, "1": {"precision": 0.956140350877193, "recall": 0.8685258964143426, "f1-score": 0.9102296450939458, "support": 251.0}, "accuracy": 0.9120654396728016, "macro avg": {"precision": 0.9148517846339987, "recall": 0.9132545448458268, "f1-score": 0.9120286502022835, "support": 489.0}, "weighted avg": {"precision": 0.915949435679319, "recall": 0.9120654396728016, "f1-score": 0.91198082389061, "support": 489.0}}, "confusion_matrix": [[228, 10], [33, 218]], "roc_auc": 0.9652315109310656, "training_time": 0.392051, "train_size": 1467, "test_size": 489, "algorithm_type": "Support Vector Machine"}, "SVM_RBF_Tuned": {"accuracy": 0.918200408997955, "precision": [0.9125, 0.9236947791164659], "recall": [0.9201680672268907, 0.9163346613545816], "f1_score": [0.9163179916317992, 0.92], "support": [238, 251], "classification_report": {"0": {"precision": 0.9125, "recall": 0.9201680672268907, "f1-score": 0.9163179916317992, "support": 238.0}, "1": {"precision": 0.9236947791164659, "recall": 0.9163346613545816, "f1-score": 0.92, "support": 251.0}, "accuracy": 0.918200408997955, "macro avg": {"precision": 0.918097389558233, "recall": 0.9182513642907362, "f1-score": 0.9181589958158995, "support": 489.0}, "weighted avg": {"precision": 0.9182461954156093, "recall": 0.918200408997955, "f1-score": 0.9182079386674197, "support": 489.0}}, "confusion_matrix": [[219, 19], [21, 230]], "roc_auc": 0.9509608624326225, "training_time": 0.700371, "train_size": 1467, "test_size": 489, "algorithm_type": "Support Vector Machine"}, "SVM_Linear_Tuned": {"accuracy": 0.9038854805725971, "precision": [0.8715953307392996, 0.9396551724137931], "recall": [0.9411764705882353, 0.8685258964143426], "f1_score": [0.9050505050505051, 0.9026915113871635], "support": [238, 251], "classification_report": {"0": {"precision": 0.8715953307392996, "recall": 0.9411764705882353, "f1-score": 0.9050505050505051, "support": 238.0}, "1": {"precision": 0.9396551724137931, "recall": 0.8685258964143426, "f1-score": 0.9026915113871635, "support": 251.0}, "accuracy": 0.9038854805725971, "macro avg": {"precision": 0.9056252515765464, "recall": 0.9048511835012889, "f1-score": 0.9038710082188344, "support": 489.0}, "weighted avg": {"precision": 0.9065299324986, "recall": 0.9038854805725971, "f1-score": 0.9038396514523482, "support": 489.0}}, "confusion_matrix": [[224, 14], [33, 218]], "roc_auc": 0.9646288794402224, "training_time": 0.423068, "train_size": 1467, "test_size": 489, "algorithm_type": "Support Vector Machine"}, "NB_Multinomial": {"accuracy": 0.8997955010224948, "precision": [0.9276018099547512, 0.8768656716417911], "recall": [0.8613445378151261, 0.9362549800796812], "f1_score": [0.8932461873638344, 0.905587668593449], "support": [238, 251], "classification_report": {"0": {"precision": 0.9276018099547512, "recall": 0.8613445378151261, "f1-score": 0.8932461873638344, "support": 238.0}, "1": {"precision": 0.8768656716417911, "recall": 0.9362549800796812, "f1-score": 0.905587668593449, "support": 251.0}, "accuracy": 0.8997955010224948, "macro avg": {"precision": 0.9022337407982711, "recall": 0.8987997589474037, "f1-score": 0.8994169279786417, "support": 489.0}, "weighted avg": {"precision": 0.9015593340517798, "recall": 0.8997955010224948, "f1-score": 0.8995809762976448, "support": 489.0}}, "confusion_matrix": [[205, 33], [16, 235]], "roc_auc": 0.9589792092135659, "training_time": 0.025735, "train_size": 1467, "test_size": 489, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli": {"accuracy": 0.8445807770961146, "precision": [0.7682119205298014, 0.9679144385026738], "recall": [0.9747899159663865, 0.7211155378486056], "f1_score": [0.8592592592592593, 0.8264840182648402], "support": [238, 251], "classification_report": {"0": {"precision": 0.7682119205298014, "recall": 0.9747899159663865, "f1-score": 0.8592592592592593, "support": 238.0}, "1": {"precision": 0.9679144385026738, "recall": 0.7211155378486056, "f1-score": 0.8264840182648402, "support": 251.0}, "accuracy": 0.8445807770961146, "macro avg": {"precision": 0.8680631795162376, "recall": 0.847952726907496, "f1-score": 0.8428716387620497, "support": 489.0}, "weighted avg": {"precision": 0.8707177119637297, "recall": 0.8445807770961146, "f1-score": 0.842435976049445, "support": 489.0}}, "confusion_matrix": [[232, 6], [70, 181]], "roc_auc": 0.9440891894606448, "training_time": 0.022064, "train_size": 1467, "test_size": 489, "algorithm_type": "<PERSON><PERSON>"}, "NB_Complement": {"accuracy": 0.8875255623721882, "precision": [0.8674698795180723, 0.9083333333333333], "recall": [0.907563025210084, 0.8685258964143426], "f1_score": [0.8870636550308009, 0.8879837067209776], "support": [238, 251], "classification_report": {"0": {"precision": 0.8674698795180723, "recall": 0.907563025210084, "f1-score": 0.8870636550308009, "support": 238.0}, "1": {"precision": 0.9083333333333333, "recall": 0.8685258964143426, "f1-score": 0.8879837067209776, "support": 251.0}, "accuracy": 0.8875255623721882, "macro avg": {"precision": 0.8879016064257028, "recall": 0.8880444608122133, "f1-score": 0.8875236808758893, "support": 489.0}, "weighted avg": {"precision": 0.8884447811696684, "recall": 0.8875255623721882, "f1-score": 0.8875359106018322, "support": 489.0}}, "confusion_matrix": [[216, 22], [33, 218]], "roc_auc": 0.9589792092135659, "training_time": 0.034594, "train_size": 1467, "test_size": 489, "algorithm_type": "<PERSON><PERSON>"}, "NB_Multinomial_Tuned": {"accuracy": 0.8936605316973415, "precision": [0.9151785714285714, 0.8754716981132076], "recall": [0.8613445378151261, 0.9243027888446215], "f1_score": [0.8874458874458875, 0.8992248062015504], "support": [238, 251], "classification_report": {"0": {"precision": 0.9151785714285714, "recall": 0.8613445378151261, "f1-score": 0.8874458874458875, "support": 238.0}, "1": {"precision": 0.8754716981132076, "recall": 0.9243027888446215, "f1-score": 0.8992248062015504, "support": 251.0}, "accuracy": 0.8936605316973415, "macro avg": {"precision": 0.8953251347708895, "recall": 0.8928236633298738, "f1-score": 0.893335346823719, "support": 489.0}, "weighted avg": {"precision": 0.89479733379635, "recall": 0.8936605316973415, "f1-score": 0.893491917318426, "support": 489.0}}, "confusion_matrix": [[205, 33], [19, 232]], "roc_auc": 0.9531872509960159, "training_time": 0.004936, "train_size": 1467, "test_size": 489, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli_Tuned": {"accuracy": 0.8752556237218814, "precision": [0.8218181818181818, 0.9439252336448598], "recall": [0.9495798319327731, 0.8047808764940239], "f1_score": [0.8810916179337231, 0.8688172043010752], "support": [238, 251], "classification_report": {"0": {"precision": 0.8218181818181818, "recall": 0.9495798319327731, "f1-score": 0.8810916179337231, "support": 238.0}, "1": {"precision": 0.9439252336448598, "recall": 0.8047808764940239, "f1-score": 0.8688172043010752, "support": 251.0}, "accuracy": 0.8752556237218814, "macro avg": {"precision": 0.8828717077315208, "recall": 0.8771803542133985, "f1-score": 0.8749544111173992, "support": 489.0}, "weighted avg": {"precision": 0.8844948076024275, "recall": 0.8752556237218814, "f1-score": 0.8747912542899714, "support": 489.0}}, "confusion_matrix": [[226, 12], [49, 202]], "roc_auc": 0.9344805651344203, "training_time": 0.011625, "train_size": 1467, "test_size": 489, "algorithm_type": "<PERSON><PERSON>"}, "LR_L1": {"accuracy": 0.8773006134969326, "precision": [0.8201438848920863, 0.95260663507109], "recall": [0.957983193277311, 0.8007968127490039], "f1_score": [0.8837209302325582, 0.8701298701298701], "support": [238, 251], "classification_report": {"0": {"precision": 0.8201438848920863, "recall": 0.957983193277311, "f1-score": 0.8837209302325582, "support": 238.0}, "1": {"precision": 0.95260663507109, "recall": 0.8007968127490039, "f1-score": 0.8701298701298701, "support": 251.0}, "accuracy": 0.8773006134969326, "macro avg": {"precision": 0.8863752599815882, "recall": 0.8793900030131574, "f1-score": 0.8769254001812141, "support": 489.0}, "weighted avg": {"precision": 0.8881360122845811, "recall": 0.8773006134969326, "f1-score": 0.8767447419180905, "support": 489.0}}, "confusion_matrix": [[228, 10], [50, 201]], "roc_auc": 0.9432187217516489, "training_time": 0.037543, "train_size": 1467, "test_size": 489, "algorithm_type": "Logistic Regression"}, "LR_L2": {"accuracy": 0.9059304703476483, "precision": [0.8692307692307693, 0.9475982532751092], "recall": [0.9495798319327731, 0.8645418326693227], "f1_score": [0.9076305220883534, 0.9041666666666667], "support": [238, 251], "classification_report": {"0": {"precision": 0.8692307692307693, "recall": 0.9495798319327731, "f1-score": 0.9076305220883534, "support": 238.0}, "1": {"precision": 0.9475982532751092, "recall": 0.8645418326693227, "f1-score": 0.9041666666666667, "support": 251.0}, "accuracy": 0.9059304703476483, "macro avg": {"precision": 0.9084145112529391, "recall": 0.9070608323010478, "f1-score": 0.90589859437751, "support": 489.0}, "weighted avg": {"precision": 0.9094562058261257, "recall": 0.9059304703476483, "f1-score": 0.9058525513095326, "support": 489.0}}, "confusion_matrix": [[226, 12], [34, 217]], "roc_auc": 0.9571043556864977, "training_time": 0.035401, "train_size": 1467, "test_size": 489, "algorithm_type": "Logistic Regression"}, "LR_ElasticNet": {"accuracy": 0.885480572597137, "precision": [0.8395522388059702, 0.9411764705882353], "recall": [0.9453781512605042, 0.8286852589641435], "f1_score": [0.8893280632411067, 0.8813559322033898], "support": [238, 251], "classification_report": {"0": {"precision": 0.8395522388059702, "recall": 0.9453781512605042, "f1-score": 0.8893280632411067, "support": 238.0}, "1": {"precision": 0.9411764705882353, "recall": 0.8286852589641435, "f1-score": 0.8813559322033898, "support": 251.0}, "accuracy": 0.885480572597137, "macro avg": {"precision": 0.8903643546971027, "recall": 0.8870317051123238, "f1-score": 0.8853419977222483, "support": 489.0}, "weighted avg": {"precision": 0.8917151880439018, "recall": 0.885480572597137, "f1-score": 0.8852360287002745, "support": 489.0}}, "confusion_matrix": [[225, 13], [43, 208]], "roc_auc": 0.9490357896146506, "training_time": 0.062065, "train_size": 1467, "test_size": 489, "algorithm_type": "Logistic Regression"}, "LR_LBFGS": {"accuracy": 0.9059304703476483, "precision": [0.8692307692307693, 0.9475982532751092], "recall": [0.9495798319327731, 0.8645418326693227], "f1_score": [0.9076305220883534, 0.9041666666666667], "support": [238, 251], "classification_report": {"0": {"precision": 0.8692307692307693, "recall": 0.9495798319327731, "f1-score": 0.9076305220883534, "support": 238.0}, "1": {"precision": 0.9475982532751092, "recall": 0.8645418326693227, "f1-score": 0.9041666666666667, "support": 251.0}, "accuracy": 0.9059304703476483, "macro avg": {"precision": 0.9084145112529391, "recall": 0.9070608323010478, "f1-score": 0.90589859437751, "support": 489.0}, "weighted avg": {"precision": 0.9094562058261257, "recall": 0.9059304703476483, "f1-score": 0.9058525513095326, "support": 489.0}}, "confusion_matrix": [[226, 12], [34, 217]], "roc_auc": 0.957271753322843, "training_time": 0.023275, "train_size": 1467, "test_size": 489, "algorithm_type": "Logistic Regression"}, "LR_SAG": {"accuracy": 0.9059304703476483, "precision": [0.8692307692307693, 0.9475982532751092], "recall": [0.9495798319327731, 0.8645418326693227], "f1_score": [0.9076305220883534, 0.9041666666666667], "support": [238, 251], "classification_report": {"0": {"precision": 0.8692307692307693, "recall": 0.9495798319327731, "f1-score": 0.9076305220883534, "support": 238.0}, "1": {"precision": 0.9475982532751092, "recall": 0.8645418326693227, "f1-score": 0.9041666666666667, "support": 251.0}, "accuracy": 0.9059304703476483, "macro avg": {"precision": 0.9084145112529391, "recall": 0.9070608323010478, "f1-score": 0.90589859437751, "support": 489.0}, "weighted avg": {"precision": 0.9094562058261257, "recall": 0.9059304703476483, "f1-score": 0.9058525513095326, "support": 489.0}}, "confusion_matrix": [[226, 12], [34, 217]], "roc_auc": 0.9571378352137667, "training_time": 0.03352, "train_size": 1467, "test_size": 489, "algorithm_type": "Logistic Regression"}, "LR_SAGA": {"accuracy": 0.9059304703476483, "precision": [0.8692307692307693, 0.9475982532751092], "recall": [0.9495798319327731, 0.8645418326693227], "f1_score": [0.9076305220883534, 0.9041666666666667], "support": [238, 251], "classification_report": {"0": {"precision": 0.8692307692307693, "recall": 0.9495798319327731, "f1-score": 0.9076305220883534, "support": 238.0}, "1": {"precision": 0.9475982532751092, "recall": 0.8645418326693227, "f1-score": 0.9041666666666667, "support": 251.0}, "accuracy": 0.9059304703476483, "macro avg": {"precision": 0.9084145112529391, "recall": 0.9070608323010478, "f1-score": 0.90589859437751, "support": 489.0}, "weighted avg": {"precision": 0.9094562058261257, "recall": 0.9059304703476483, "f1-score": 0.9058525513095326, "support": 489.0}}, "confusion_matrix": [[226, 12], [34, 217]], "roc_auc": 0.9571210954501322, "training_time": 0.032705, "train_size": 1467, "test_size": 489, "algorithm_type": "Logistic Regression"}, "DT_Gini": {"accuracy": 0.8752556237218814, "precision": [0.9359605911330049, 0.8321678321678322], "recall": [0.7983193277310925, 0.9482071713147411], "f1_score": [0.8616780045351474, 0.8864059590316573], "support": [238, 251], "classification_report": {"0": {"precision": 0.9359605911330049, "recall": 0.7983193277310925, "f1-score": 0.8616780045351474, "support": 238.0}, "1": {"precision": 0.8321678321678322, "recall": 0.9482071713147411, "f1-score": 0.8864059590316573, "support": 251.0}, "accuracy": 0.8752556237218814, "macro avg": {"precision": 0.8840642116504185, "recall": 0.8732632495229168, "f1-score": 0.8740419817834024, "support": 489.0}, "weighted avg": {"precision": 0.8826845533001657, "recall": 0.8752556237218814, "f1-score": 0.8743706764750738, "support": 489.0}}, "confusion_matrix": [[190, 48], [13, 238]], "roc_auc": 0.9408918946064482, "training_time": 0.085818, "train_size": 1467, "test_size": 489, "algorithm_type": "Decision Tree"}, "DT_Entropy": {"accuracy": 0.852760736196319, "precision": [0.8990384615384616, 0.8185053380782918], "recall": [0.7857142857142857, 0.9163346613545816], "f1_score": [0.8385650224215246, 0.8646616541353384], "support": [238, 251], "classification_report": {"0": {"precision": 0.8990384615384616, "recall": 0.7857142857142857, "f1-score": 0.8385650224215246, "support": 238.0}, "1": {"precision": 0.8185053380782918, "recall": 0.9163346613545816, "f1-score": 0.8646616541353384, "support": 251.0}, "accuracy": 0.852760736196319, "macro avg": {"precision": 0.8587718998083766, "recall": 0.8510244735344337, "f1-score": 0.8516133382784314, "support": 489.0}, "weighted avg": {"precision": 0.8577014186171883, "recall": 0.852760736196319, "f1-score": 0.8519602260210487, "support": 489.0}}, "confusion_matrix": [[187, 51], [21, 230]], "roc_auc": 0.9155227828183066, "training_time": 0.084084, "train_size": 1467, "test_size": 489, "algorithm_type": "Decision Tree"}, "DT_Log_Loss": {"accuracy": 0.852760736196319, "precision": [0.8990384615384616, 0.8185053380782918], "recall": [0.7857142857142857, 0.9163346613545816], "f1_score": [0.8385650224215246, 0.8646616541353384], "support": [238, 251], "classification_report": {"0": {"precision": 0.8990384615384616, "recall": 0.7857142857142857, "f1-score": 0.8385650224215246, "support": 238.0}, "1": {"precision": 0.8185053380782918, "recall": 0.9163346613545816, "f1-score": 0.8646616541353384, "support": 251.0}, "accuracy": 0.852760736196319, "macro avg": {"precision": 0.8587718998083766, "recall": 0.8510244735344337, "f1-score": 0.8516133382784314, "support": 489.0}, "weighted avg": {"precision": 0.8577014186171883, "recall": 0.852760736196319, "f1-score": 0.8519602260210487, "support": 489.0}}, "confusion_matrix": [[187, 51], [21, 230]], "roc_auc": 0.9155227828183066, "training_time": 0.093953, "train_size": 1467, "test_size": 489, "algorithm_type": "Decision Tree"}, "DT_Gini_Pruned": {"accuracy": 0.8875255623721882, "precision": [0.818815331010453, 0.9851485148514851], "recall": [0.9873949579831933, 0.7928286852589641], "f1_score": [0.8952380952380953, 0.8785871964679912], "support": [238, 251], "classification_report": {"0": {"precision": 0.818815331010453, "recall": 0.9873949579831933, "f1-score": 0.8952380952380953, "support": 238.0}, "1": {"precision": 0.9851485148514851, "recall": 0.7928286852589641, "f1-score": 0.8785871964679912, "support": 251.0}, "accuracy": 0.8875255623721882, "macro avg": {"precision": 0.9019819229309691, "recall": 0.8901118216210787, "f1-score": 0.8869126458530432, "support": 489.0}, "weighted avg": {"precision": 0.904192895722312, "recall": 0.8875255623721882, "f1-score": 0.8866913148877964, "support": 489.0}}, "confusion_matrix": [[235, 3], [52, 199]], "roc_auc": 0.8881532692758377, "training_time": 0.051334, "train_size": 1467, "test_size": 489, "algorithm_type": "Decision Tree"}, "DT_Entropy_Pruned": {"accuracy": 0.8670756646216768, "precision": [0.7912457912457912, 0.984375], "recall": [0.9873949579831933, 0.7529880478087649], "f1_score": [0.8785046728971962, 0.8532731376975169], "support": [238, 251], "classification_report": {"0": {"precision": 0.7912457912457912, "recall": 0.9873949579831933, "f1-score": 0.8785046728971962, "support": 238.0}, "1": {"precision": 0.984375, "recall": 0.7529880478087649, "f1-score": 0.8532731376975169, "support": 251.0}, "accuracy": 0.8670756646216768, "macro avg": {"precision": 0.8878103956228955, "recall": 0.8701915028959791, "f1-score": 0.8658889052973566, "support": 489.0}, "weighted avg": {"precision": 0.890377552794475, "recall": 0.8670756646216768, "f1-score": 0.8655535167926574, "support": 489.0}}, "confusion_matrix": [[235, 3], [62, 189]], "roc_auc": 0.8742843751046235, "training_time": 0.040649, "train_size": 1467, "test_size": 489, "algorithm_type": "Decision Tree"}, "DT_Best_First": {"accuracy": 0.8752556237218814, "precision": [0.9359605911330049, 0.8321678321678322], "recall": [0.7983193277310925, 0.9482071713147411], "f1_score": [0.8616780045351474, 0.8864059590316573], "support": [238, 251], "classification_report": {"0": {"precision": 0.9359605911330049, "recall": 0.7983193277310925, "f1-score": 0.8616780045351474, "support": 238.0}, "1": {"precision": 0.8321678321678322, "recall": 0.9482071713147411, "f1-score": 0.8864059590316573, "support": 251.0}, "accuracy": 0.8752556237218814, "macro avg": {"precision": 0.8840642116504185, "recall": 0.8732632495229168, "f1-score": 0.8740419817834024, "support": 489.0}, "weighted avg": {"precision": 0.8826845533001657, "recall": 0.8752556237218814, "f1-score": 0.8743706764750738, "support": 489.0}}, "confusion_matrix": [[190, 48], [13, 238]], "roc_auc": 0.9408918946064482, "training_time": 0.084186, "train_size": 1467, "test_size": 489, "algorithm_type": "Decision Tree"}, "DT_Random_Split": {"accuracy": 0.8752556237218814, "precision": [0.9402985074626866, 0.8298611111111112], "recall": [0.7941176470588235, 0.952191235059761], "f1_score": [0.8610478359908884, 0.8868274582560297], "support": [238, 251], "classification_report": {"0": {"precision": 0.9402985074626866, "recall": 0.7941176470588235, "f1-score": 0.8610478359908884, "support": 238.0}, "1": {"precision": 0.8298611111111112, "recall": 0.952191235059761, "f1-score": 0.8868274582560297, "support": 251.0}, "accuracy": 0.8752556237218814, "macro avg": {"precision": 0.8850798092868989, "recall": 0.8731544410592922, "f1-score": 0.873937647123459, "support": 489.0}, "weighted avg": {"precision": 0.8836118275358044, "recall": 0.8752556237218814, "f1-score": 0.8742803210390488, "support": 489.0}}, "confusion_matrix": [[189, 49], [12, 239]], "roc_auc": 0.9377448190431552, "training_time": 0.077542, "train_size": 1467, "test_size": 489, "algorithm_type": "Decision Tree"}, "RF_Gini": {"accuracy": 0.8936605316973415, "precision": [0.9428571428571428, 0.8566308243727598], "recall": [0.8319327731092437, 0.952191235059761], "f1_score": [0.8839285714285714, 0.9018867924528302], "support": [238, 251], "classification_report": {"0": {"precision": 0.9428571428571428, "recall": 0.8319327731092437, "f1-score": 0.8839285714285714, "support": 238.0}, "1": {"precision": 0.8566308243727598, "recall": 0.952191235059761, "f1-score": 0.9018867924528302, "support": 251.0}, "accuracy": 0.8936605316973415, "macro avg": {"precision": 0.8997439836149513, "recall": 0.8920620040845024, "f1-score": 0.8929076819407008, "support": 489.0}, "weighted avg": {"precision": 0.8985978260072858, "recall": 0.8936605316973415, "f1-score": 0.8931463904001234, "support": 489.0}}, "confusion_matrix": [[198, 40], [12, 239]], "roc_auc": 0.9604857879406741, "training_time": 0.611209, "train_size": 1467, "test_size": 489, "algorithm_type": "Random Forest"}, "RF_Entropy": {"accuracy": 0.8813905930470347, "precision": [0.9368932038834952, 0.8409893992932862], "recall": [0.8109243697478992, 0.9482071713147411], "f1_score": [0.8693693693693694, 0.8913857677902621], "support": [238, 251], "classification_report": {"0": {"precision": 0.9368932038834952, "recall": 0.8109243697478992, "f1-score": 0.8693693693693694, "support": 238.0}, "1": {"precision": 0.8409893992932862, "recall": 0.9482071713147411, "f1-score": 0.8913857677902621, "support": 251.0}, "accuracy": 0.8813905930470347, "macro avg": {"precision": 0.8889413015883907, "recall": 0.8795657705313201, "f1-score": 0.8803775685798158, "support": 489.0}, "weighted avg": {"precision": 0.8876665066398501, "recall": 0.8813905930470347, "f1-score": 0.8806702200925679, "support": 489.0}}, "confusion_matrix": [[193, 45], [13, 238]], "roc_auc": 0.9620007365495999, "training_time": 0.616691, "train_size": 1467, "test_size": 489, "algorithm_type": "Random Forest"}, "RF_Log_Loss": {"accuracy": 0.8813905930470347, "precision": [0.9368932038834952, 0.8409893992932862], "recall": [0.8109243697478992, 0.9482071713147411], "f1_score": [0.8693693693693694, 0.8913857677902621], "support": [238, 251], "classification_report": {"0": {"precision": 0.9368932038834952, "recall": 0.8109243697478992, "f1-score": 0.8693693693693694, "support": 238.0}, "1": {"precision": 0.8409893992932862, "recall": 0.9482071713147411, "f1-score": 0.8913857677902621, "support": 251.0}, "accuracy": 0.8813905930470347, "macro avg": {"precision": 0.8889413015883907, "recall": 0.8795657705313201, "f1-score": 0.8803775685798158, "support": 489.0}, "weighted avg": {"precision": 0.8876665066398501, "recall": 0.8813905930470347, "f1-score": 0.8806702200925679, "support": 489.0}}, "confusion_matrix": [[193, 45], [13, 238]], "roc_auc": 0.9620007365495999, "training_time": 0.633383, "train_size": 1467, "test_size": 489, "algorithm_type": "Random Forest"}, "RF_Large": {"accuracy": 0.8752556237218814, "precision": [0.8083623693379791, 0.9702970297029703], "recall": [0.9747899159663865, 0.7808764940239044], "f1_score": [0.8838095238095238, 0.8653421633554084], "support": [238, 251], "classification_report": {"0": {"precision": 0.8083623693379791, "recall": 0.9747899159663865, "f1-score": 0.8838095238095238, "support": 238.0}, "1": {"precision": 0.9702970297029703, "recall": 0.7808764940239044, "f1-score": 0.8653421633554084, "support": 251.0}, "accuracy": 0.8752556237218814, "macro avg": {"precision": 0.8893296995204747, "recall": 0.8778332049951454, "f1-score": 0.8745758435824661, "support": 489.0}, "weighted avg": {"precision": 0.8914822052308478, "recall": 0.8752556237218814, "f1-score": 0.8743303674210104, "support": 489.0}}, "confusion_matrix": [[232, 6], [55, 196]], "roc_auc": 0.9502243128327028, "training_time": 0.429908, "train_size": 1467, "test_size": 489, "algorithm_type": "Random Forest"}, "RF_Small": {"accuracy": 0.8773006134969326, "precision": [0.8156028368794326, 0.961352657004831], "recall": [0.9663865546218487, 0.7928286852589641], "f1_score": [0.8846153846153846, 0.868995633187773], "support": [238, 251], "classification_report": {"0": {"precision": 0.8156028368794326, "recall": 0.9663865546218487, "f1-score": 0.8846153846153846, "support": 238.0}, "1": {"precision": 0.961352657004831, "recall": 0.7928286852589641, "f1-score": 0.868995633187773, "support": 251.0}, "accuracy": 0.8773006134969326, "macro avg": {"precision": 0.8884777469421318, "recall": 0.8796076199404064, "f1-score": 0.8768055089015787, "support": 489.0}, "weighted avg": {"precision": 0.8904151167392996, "recall": 0.8773006134969326, "f1-score": 0.8765978843938498, "support": 489.0}}, "confusion_matrix": [[230, 8], [52, 199]], "roc_auc": 0.9426411999062574, "training_time": 0.131241, "train_size": 1467, "test_size": 489, "algorithm_type": "Random Forest"}, "ExtraTrees": {"accuracy": 0.8916155419222904, "precision": [0.9302325581395349, 0.8613138686131386], "recall": [0.8403361344537815, 0.9402390438247012], "f1_score": [0.8830022075055187, 0.8990476190476191], "support": [238, 251], "classification_report": {"0": {"precision": 0.9302325581395349, "recall": 0.8403361344537815, "f1-score": 0.8830022075055187, "support": 238.0}, "1": {"precision": 0.8613138686131386, "recall": 0.9402390438247012, "f1-score": 0.8990476190476191, "support": 251.0}, "accuracy": 0.8916155419222904, "macro avg": {"precision": 0.8957732133763368, "recall": 0.8902875891392414, "f1-score": 0.8910249132765689, "support": 489.0}, "weighted avg": {"precision": 0.8948571162762926, "recall": 0.8916155419222904, "f1-score": 0.8912381958430795, "support": 489.0}}, "confusion_matrix": [[200, 38], [15, 236]], "roc_auc": 0.9634236164585356, "training_time": 0.879046, "train_size": 1467, "test_size": 489, "algorithm_type": "Random Forest"}, "ExtraTrees_Large": {"accuracy": 0.8732106339468303, "precision": [0.8165467625899281, 0.9478672985781991], "recall": [0.9537815126050421, 0.796812749003984], "f1_score": [0.8798449612403101, 0.8658008658008658], "support": [238, 251], "classification_report": {"0": {"precision": 0.8165467625899281, "recall": 0.9537815126050421, "f1-score": 0.8798449612403101, "support": 238.0}, "1": {"precision": 0.9478672985781991, "recall": 0.796812749003984, "f1-score": 0.8658008658008658, "support": 251.0}, "accuracy": 0.8732106339468303, "macro avg": {"precision": 0.8822070305840636, "recall": 0.875297130804513, "f1-score": 0.8728229135205879, "support": 489.0}, "weighted avg": {"precision": 0.8839526000808402, "recall": 0.8732106339468303, "f1-score": 0.8726362333153601, "support": 489.0}}, "confusion_matrix": [[227, 11], [51, 200]], "roc_auc": 0.9458050152331848, "training_time": 0.340362, "train_size": 1467, "test_size": 489, "algorithm_type": "Random Forest"}, "GradientBoosting": {"accuracy": 0.901840490797546, "precision": [0.841726618705036, 0.981042654028436], "recall": [0.9831932773109243, 0.8247011952191236], "f1_score": [0.9069767441860465, 0.8961038961038961], "support": [238, 251], "classification_report": {"0": {"precision": 0.841726618705036, "recall": 0.9831932773109243, "f1-score": 0.9069767441860465, "support": 238.0}, "1": {"precision": 0.981042654028436, "recall": 0.8247011952191236, "f1-score": 0.8961038961038961, "support": 251.0}, "accuracy": 0.901840490797546, "macro avg": {"precision": 0.911384636366736, "recall": 0.9039472362650239, "f1-score": 0.9015403201449712, "support": 489.0}, "weighted avg": {"precision": 0.9132364855070266, "recall": 0.901840490797546, "f1-score": 0.9013957935344723, "support": 489.0}}, "confusion_matrix": [[234, 4], [44, 207]], "roc_auc": 0.9520154675415983, "training_time": 0.734363, "train_size": 1467, "test_size": 489, "algorithm_type": "Ensemble"}, "AdaBoost": {"accuracy": 0.901840490797546, "precision": [0.8492647058823529, 0.967741935483871], "recall": [0.9705882352941176, 0.8366533864541833], "f1_score": [0.9058823529411765, 0.8974358974358975], "support": [238, 251], "classification_report": {"0": {"precision": 0.8492647058823529, "recall": 0.9705882352941176, "f1-score": 0.9058823529411765, "support": 238.0}, "1": {"precision": 0.967741935483871, "recall": 0.8366533864541833, "f1-score": 0.8974358974358975, "support": 251.0}, "accuracy": 0.901840490797546, "macro avg": {"precision": 0.9085033206831119, "recall": 0.9036208108741505, "f1-score": 0.901659125188537, "support": 489.0}, "weighted avg": {"precision": 0.9100781713833366, "recall": 0.901840490797546, "f1-score": 0.9015468512401028, "support": 489.0}}, "confusion_matrix": [[231, 7], [41, 210]], "roc_auc": 0.9406993873246509, "training_time": 0.39252, "train_size": 1467, "test_size": 489, "algorithm_type": "Ensemble"}, "AdaBoost_DT": {"accuracy": 0.8609406952965235, "precision": [0.9166666666666666, 0.8210526315789474], "recall": [0.7857142857142857, 0.9322709163346613], "f1_score": [0.8461538461538461, 0.8731343283582089], "support": [238, 251], "classification_report": {"0": {"precision": 0.9166666666666666, "recall": 0.7857142857142857, "f1-score": 0.8461538461538461, "support": 238.0}, "1": {"precision": 0.8210526315789474, "recall": 0.9322709163346613, "f1-score": 0.8731343283582089, "support": 251.0}, "accuracy": 0.8609406952965235, "macro avg": {"precision": 0.868859649122807, "recall": 0.8589926010244735, "f1-score": 0.8596440872560276, "support": 489.0}, "weighted avg": {"precision": 0.8675887059161195, "recall": 0.8609406952965235, "f1-score": 0.8600027235225478, "support": 489.0}}, "confusion_matrix": [[187, 51], [17, 234]], "roc_auc": 0.9501322441327127, "training_time": 0.847828, "train_size": 1467, "test_size": 489, "algorithm_type": "Decision Tree"}}, "train_70_test_30": {"SVM_Linear": {"accuracy": 0.9011925042589438, "precision": [0.8603174603174604, 0.9485294117647058], "recall": [0.9508771929824561, 0.8543046357615894], "f1_score": [0.9033333333333333, 0.8989547038327527], "support": [285, 302], "classification_report": {"0": {"precision": 0.8603174603174604, "recall": 0.9508771929824561, "f1-score": 0.9033333333333333, "support": 285.0}, "1": {"precision": 0.9485294117647058, "recall": 0.8543046357615894, "f1-score": 0.8989547038327527, "support": 302.0}, "accuracy": 0.9011925042589438, "macro avg": {"precision": 0.9044234360410831, "recall": 0.9025909143720228, "f1-score": 0.901144018583043, "support": 587.0}, "weighted avg": {"precision": 0.9057007811642545, "recall": 0.9011925042589438, "f1-score": 0.9010806142376341, "support": 587.0}}, "confusion_matrix": [[271, 14], [44, 258]], "roc_auc": 0.9655280585569884, "training_time": 0.372387, "train_size": 1369, "test_size": 587, "algorithm_type": "Support Vector Machine"}, "SVM_Polynomial": {"accuracy": 0.879045996592845, "precision": [0.8794326241134752, 0.8786885245901639], "recall": [0.8701754385964913, 0.8874172185430463], "f1_score": [0.8747795414462081, 0.8830313014827018], "support": [285, 302], "classification_report": {"0": {"precision": 0.8794326241134752, "recall": 0.8701754385964913, "f1-score": 0.8747795414462081, "support": 285.0}, "1": {"precision": 0.8786885245901639, "recall": 0.8874172185430463, "f1-score": 0.8830313014827018, "support": 302.0}, "accuracy": 0.879045996592845, "macro avg": {"precision": 0.8790605743518196, "recall": 0.8787963285697689, "f1-score": 0.8789054214644549, "support": 587.0}, "weighted avg": {"precision": 0.8790497994864904, "recall": 0.879045996592845, "f1-score": 0.8790249103235864, "support": 587.0}}, "confusion_matrix": [[248, 37], [34, 268]], "roc_auc": 0.9404786801440689, "training_time": 0.531368, "train_size": 1369, "test_size": 587, "algorithm_type": "Support Vector Machine"}, "SVM_RBF": {"accuracy": 0.9063032367972743, "precision": [0.8833333333333333, 0.9303135888501742], "recall": [0.9298245614035088, 0.8841059602649006], "f1_score": [0.905982905982906, 0.9066213921901528], "support": [285, 302], "classification_report": {"0": {"precision": 0.8833333333333333, "recall": 0.9298245614035088, "f1-score": 0.905982905982906, "support": 285.0}, "1": {"precision": 0.9303135888501742, "recall": 0.8841059602649006, "f1-score": 0.9066213921901528, "support": 302.0}, "accuracy": 0.9063032367972743, "macro avg": {"precision": 0.9068234610917538, "recall": 0.9069652608342047, "f1-score": 0.9063021490865294, "support": 587.0}, "weighted avg": {"precision": 0.9075037543999191, "recall": 0.9063032367972743, "f1-score": 0.906311394627861, "support": 587.0}}, "confusion_matrix": [[265, 20], [35, 267]], "roc_auc": 0.9511560357848262, "training_time": 0.55546, "train_size": 1369, "test_size": 587, "algorithm_type": "Support Vector Machine"}, "SVM_Sigmoid": {"accuracy": 0.9045996592844975, "precision": [0.8589341692789969, 0.9589552238805971], "recall": [0.9614035087719298, 0.8509933774834437], "f1_score": [0.9072847682119205, 0.9017543859649123], "support": [285, 302], "classification_report": {"0": {"precision": 0.8589341692789969, "recall": 0.9614035087719298, "f1-score": 0.9072847682119205, "support": 285.0}, "1": {"precision": 0.9589552238805971, "recall": 0.8509933774834437, "f1-score": 0.9017543859649123, "support": 302.0}, "accuracy": 0.9045996592844975, "macro avg": {"precision": 0.9089446965797969, "recall": 0.9061984431276868, "f1-score": 0.9045195770884165, "support": 587.0}, "weighted avg": {"precision": 0.9103930423448968, "recall": 0.9045996592844975, "f1-score": 0.9044394948923353, "support": 587.0}}, "confusion_matrix": [[274, 11], [45, 257]], "roc_auc": 0.9642035552457302, "training_time": 0.346543, "train_size": 1369, "test_size": 587, "algorithm_type": "Support Vector Machine"}, "SVM_RBF_Tuned": {"accuracy": 0.9028960817717206, "precision": [0.88, 0.926829268292683], "recall": [0.9263157894736842, 0.8807947019867549], "f1_score": [0.9025641025641026, 0.9032258064516129], "support": [285, 302], "classification_report": {"0": {"precision": 0.88, "recall": 0.9263157894736842, "f1-score": 0.9025641025641026, "support": 285.0}, "1": {"precision": 0.926829268292683, "recall": 0.8807947019867549, "f1-score": 0.9032258064516129, "support": 302.0}, "accuracy": 0.9028960817717206, "macro avg": {"precision": 0.9034146341463415, "recall": 0.9035552457302196, "f1-score": 0.9028949545078577, "support": 587.0}, "weighted avg": {"precision": 0.9040927410977688, "recall": 0.9028960817717206, "f1-score": 0.9029045362506922, "support": 587.0}}, "confusion_matrix": [[264, 21], [36, 266]], "roc_auc": 0.9510049959335425, "training_time": 0.576654, "train_size": 1369, "test_size": 587, "algorithm_type": "Support Vector Machine"}, "SVM_Linear_Tuned": {"accuracy": 0.9011925042589438, "precision": [0.8603174603174604, 0.9485294117647058], "recall": [0.9508771929824561, 0.8543046357615894], "f1_score": [0.9033333333333333, 0.8989547038327527], "support": [285, 302], "classification_report": {"0": {"precision": 0.8603174603174604, "recall": 0.9508771929824561, "f1-score": 0.9033333333333333, "support": 285.0}, "1": {"precision": 0.9485294117647058, "recall": 0.8543046357615894, "f1-score": 0.8989547038327527, "support": 302.0}, "accuracy": 0.9011925042589438, "macro avg": {"precision": 0.9044234360410831, "recall": 0.9025909143720228, "f1-score": 0.901144018583043, "support": 587.0}, "weighted avg": {"precision": 0.9057007811642545, "recall": 0.9011925042589438, "f1-score": 0.9010806142376341, "support": 587.0}}, "confusion_matrix": [[271, 14], [44, 258]], "roc_auc": 0.9655280585569884, "training_time": 0.382698, "train_size": 1369, "test_size": 587, "algorithm_type": "Support Vector Machine"}, "NB_Multinomial": {"accuracy": 0.9028960817717206, "precision": [0.9253731343283582, 0.8840125391849529], "recall": [0.8701754385964913, 0.9337748344370861], "f1_score": [0.8969258589511754, 0.9082125603864735], "support": [285, 302], "classification_report": {"0": {"precision": 0.9253731343283582, "recall": 0.8701754385964913, "f1-score": 0.8969258589511754, "support": 285.0}, "1": {"precision": 0.8840125391849529, "recall": 0.9337748344370861, "f1-score": 0.9082125603864735, "support": 302.0}, "accuracy": 0.9028960817717206, "macro avg": {"precision": 0.9046928367566556, "recall": 0.9019751365167887, "f1-score": 0.9025692096688245, "support": 587.0}, "weighted avg": {"precision": 0.904093918428344, "recall": 0.9028960817717206, "f1-score": 0.9027326457202725, "support": 587.0}}, "confusion_matrix": [[248, 37], [20, 282]], "roc_auc": 0.9590798187521784, "training_time": 0.021128, "train_size": 1369, "test_size": 587, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli": {"accuracy": 0.8432708688245315, "precision": [0.7643835616438356, 0.972972972972973], "recall": [0.9789473684210527, 0.7152317880794702], "f1_score": [0.8584615384615385, 0.8244274809160306], "support": [285, 302], "classification_report": {"0": {"precision": 0.7643835616438356, "recall": 0.9789473684210527, "f1-score": 0.8584615384615385, "support": 285.0}, "1": {"precision": 0.972972972972973, "recall": 0.7152317880794702, "f1-score": 0.8244274809160306, "support": 302.0}, "accuracy": 0.8432708688245315, "macro avg": {"precision": 0.8686782673084044, "recall": 0.8470895782502614, "f1-score": 0.8414445096887846, "support": 587.0}, "weighted avg": {"precision": 0.8716987272680256, "recall": 0.8432708688245315, "f1-score": 0.8409516826204083, "support": 587.0}}, "confusion_matrix": [[279, 6], [86, 216]], "roc_auc": 0.9444347624026955, "training_time": 0.028851, "train_size": 1369, "test_size": 587, "algorithm_type": "<PERSON><PERSON>"}, "NB_Complement": {"accuracy": 0.8909710391822828, "precision": [0.8695652173913043, 0.9131944444444444], "recall": [0.9122807017543859, 0.8708609271523179], "f1_score": [0.8904109589041096, 0.8915254237288136], "support": [285, 302], "classification_report": {"0": {"precision": 0.8695652173913043, "recall": 0.9122807017543859, "f1-score": 0.8904109589041096, "support": 285.0}, "1": {"precision": 0.9131944444444444, "recall": 0.8708609271523179, "f1-score": 0.8915254237288136, "support": 302.0}, "accuracy": 0.8909710391822828, "macro avg": {"precision": 0.8913798309178744, "recall": 0.8915708144533518, "f1-score": 0.8909681913164615, "support": 587.0}, "weighted avg": {"precision": 0.8920115999637886, "recall": 0.8909710391822828, "f1-score": 0.8909843292227819, "support": 587.0}}, "confusion_matrix": [[260, 25], [39, 263]], "roc_auc": 0.9590798187521784, "training_time": 0.022617, "train_size": 1369, "test_size": 587, "algorithm_type": "<PERSON><PERSON>"}, "NB_Multinomial_Tuned": {"accuracy": 0.8926746166950597, "precision": [0.9111111111111111, 0.8769716088328076], "recall": [0.8631578947368421, 0.9205298013245033], "f1_score": [0.8864864864864865, 0.8982229402261712], "support": [285, 302], "classification_report": {"0": {"precision": 0.9111111111111111, "recall": 0.8631578947368421, "f1-score": 0.8864864864864865, "support": 285.0}, "1": {"precision": 0.8769716088328076, "recall": 0.9205298013245033, "f1-score": 0.8982229402261712, "support": 302.0}, "accuracy": 0.8926746166950597, "macro avg": {"precision": 0.8940413599719593, "recall": 0.8918438480306727, "f1-score": 0.8923547133563289, "support": 587.0}, "weighted avg": {"precision": 0.8935470060207403, "recall": 0.8926746166950597, "f1-score": 0.8925246620050297, "support": 587.0}}, "confusion_matrix": [[246, 39], [24, 278]], "roc_auc": 0.9542697804112932, "training_time": 0.020655, "train_size": 1369, "test_size": 587, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli_Tuned": {"accuracy": 0.8756388415672913, "precision": [0.8173652694610778, 0.9525691699604744], "recall": [0.9578947368421052, 0.7980132450331126], "f1_score": [0.8820678513731826, 0.8684684684684685], "support": [285, 302], "classification_report": {"0": {"precision": 0.8173652694610778, "recall": 0.9578947368421052, "f1-score": 0.8820678513731826, "support": 285.0}, "1": {"precision": 0.9525691699604744, "recall": 0.7980132450331126, "f1-score": 0.8684684684684685, "support": 302.0}, "accuracy": 0.8756388415672913, "macro avg": {"precision": 0.8849672197107761, "recall": 0.8779539909376088, "f1-score": 0.8752681599208255, "support": 587.0}, "weighted avg": {"precision": 0.8869250274692851, "recall": 0.8756388415672913, "f1-score": 0.8750712352961405, "support": 587.0}}, "confusion_matrix": [[273, 12], [61, 241]], "roc_auc": 0.9344312768676658, "training_time": 0.004915, "train_size": 1369, "test_size": 587, "algorithm_type": "<PERSON><PERSON>"}, "LR_L1": {"accuracy": 0.8773424190800682, "precision": [0.8160237388724035, 0.96], "recall": [0.9649122807017544, 0.7947019867549668], "f1_score": [0.8842443729903537, 0.8695652173913043], "support": [285, 302], "classification_report": {"0": {"precision": 0.8160237388724035, "recall": 0.9649122807017544, "f1-score": 0.8842443729903537, "support": 285.0}, "1": {"precision": 0.96, "recall": 0.7947019867549668, "f1-score": 0.8695652173913043, "support": 302.0}, "accuracy": 0.8773424190800682, "macro avg": {"precision": 0.8880118694362018, "recall": 0.8798071337283606, "f1-score": 0.8769047951908291, "support": 587.0}, "weighted avg": {"precision": 0.8900967045632623, "recall": 0.8773424190800682, "f1-score": 0.8766922350160558, "support": 587.0}}, "confusion_matrix": [[275, 10], [62, 240]], "roc_auc": 0.941129313349599, "training_time": 0.015019, "train_size": 1369, "test_size": 587, "algorithm_type": "Logistic Regression"}, "LR_L2": {"accuracy": 0.9045996592844975, "precision": [0.861198738170347, 0.9555555555555556], "recall": [0.9578947368421052, 0.8543046357615894], "f1_score": [0.9069767441860465, 0.9020979020979021], "support": [285, 302], "classification_report": {"0": {"precision": 0.861198738170347, "recall": 0.9578947368421052, "f1-score": 0.9069767441860465, "support": 285.0}, "1": {"precision": 0.9555555555555556, "recall": 0.8543046357615894, "f1-score": 0.9020979020979021, "support": 302.0}, "accuracy": 0.9045996592844975, "macro avg": {"precision": 0.9083771468629513, "recall": 0.9060996863018473, "f1-score": 0.9045373231419742, "support": 587.0}, "weighted avg": {"precision": 0.9097434721572856, "recall": 0.9045996592844975, "f1-score": 0.9044666755137813, "support": 587.0}}, "confusion_matrix": [[273, 12], [44, 258]], "roc_auc": 0.9576275124898338, "training_time": 0.021481, "train_size": 1369, "test_size": 587, "algorithm_type": "Logistic Regression"}, "LR_ElasticNet": {"accuracy": 0.8807495741056218, "precision": [0.8267477203647416, 0.9496124031007752], "recall": [0.9543859649122807, 0.8112582781456954], "f1_score": [0.8859934853420195, 0.875], "support": [285, 302], "classification_report": {"0": {"precision": 0.8267477203647416, "recall": 0.9543859649122807, "f1-score": 0.8859934853420195, "support": 285.0}, "1": {"precision": 0.9496124031007752, "recall": 0.8112582781456954, "f1-score": 0.875, "support": 302.0}, "accuracy": 0.8807495741056218, "macro avg": {"precision": 0.8881800617327584, "recall": 0.882822121528988, "f1-score": 0.8804967426710097, "support": 587.0}, "weighted avg": {"precision": 0.8899591925730588, "recall": 0.8807495741056218, "f1-score": 0.8803375525084762, "support": 587.0}}, "confusion_matrix": [[272, 13], [57, 245]], "roc_auc": 0.9497153479725804, "training_time": 0.064227, "train_size": 1369, "test_size": 587, "algorithm_type": "Logistic Regression"}, "LR_LBFGS": {"accuracy": 0.9028960817717206, "precision": [0.8607594936708861, 0.9520295202952029], "recall": [0.9543859649122807, 0.8543046357615894], "f1_score": [0.9051580698835274, 0.900523560209424], "support": [285, 302], "classification_report": {"0": {"precision": 0.8607594936708861, "recall": 0.9543859649122807, "f1-score": 0.9051580698835274, "support": 285.0}, "1": {"precision": 0.9520295202952029, "recall": 0.8543046357615894, "f1-score": 0.900523560209424, "support": 302.0}, "accuracy": 0.9028960817717206, "macro avg": {"precision": 0.9063945069830446, "recall": 0.904345300336935, "f1-score": 0.9028408150464757, "support": 587.0}, "weighted avg": {"precision": 0.9077161342851002, "recall": 0.9028960817717206, "f1-score": 0.9027737054515353, "support": 587.0}}, "confusion_matrix": [[272, 13], [44, 258]], "roc_auc": 0.9578714999419077, "training_time": 0.036061, "train_size": 1369, "test_size": 587, "algorithm_type": "Logistic Regression"}, "LR_SAG": {"accuracy": 0.9045996592844975, "precision": [0.861198738170347, 0.9555555555555556], "recall": [0.9578947368421052, 0.8543046357615894], "f1_score": [0.9069767441860465, 0.9020979020979021], "support": [285, 302], "classification_report": {"0": {"precision": 0.861198738170347, "recall": 0.9578947368421052, "f1-score": 0.9069767441860465, "support": 285.0}, "1": {"precision": 0.9555555555555556, "recall": 0.8543046357615894, "f1-score": 0.9020979020979021, "support": 302.0}, "accuracy": 0.9045996592844975, "macro avg": {"precision": 0.9083771468629513, "recall": 0.9060996863018473, "f1-score": 0.9045373231419742, "support": 587.0}, "weighted avg": {"precision": 0.9097434721572856, "recall": 0.9045996592844975, "f1-score": 0.9044666755137813, "support": 587.0}}, "confusion_matrix": [[273, 12], [44, 258]], "roc_auc": 0.9576158940397352, "training_time": 0.022879, "train_size": 1369, "test_size": 587, "algorithm_type": "Logistic Regression"}, "LR_SAGA": {"accuracy": 0.9045996592844975, "precision": [0.861198738170347, 0.9555555555555556], "recall": [0.9578947368421052, 0.8543046357615894], "f1_score": [0.9069767441860465, 0.9020979020979021], "support": [285, 302], "classification_report": {"0": {"precision": 0.861198738170347, "recall": 0.9578947368421052, "f1-score": 0.9069767441860465, "support": 285.0}, "1": {"precision": 0.9555555555555556, "recall": 0.8543046357615894, "f1-score": 0.9020979020979021, "support": 302.0}, "accuracy": 0.9045996592844975, "macro avg": {"precision": 0.9083771468629513, "recall": 0.9060996863018473, "f1-score": 0.9045373231419742, "support": 587.0}, "weighted avg": {"precision": 0.9097434721572856, "recall": 0.9045996592844975, "f1-score": 0.9044666755137813, "support": 587.0}}, "confusion_matrix": [[273, 12], [44, 258]], "roc_auc": 0.9577553154409201, "training_time": 0.029609, "train_size": 1369, "test_size": 587, "algorithm_type": "Logistic Regression"}, "DT_Gini": {"accuracy": 0.868824531516184, "precision": [0.9227642276422764, 0.8299120234604106], "recall": [0.7964912280701755, 0.9370860927152318], "f1_score": [0.8549905838041432, 0.880248833592535], "support": [285, 302], "classification_report": {"0": {"precision": 0.9227642276422764, "recall": 0.7964912280701755, "f1-score": 0.8549905838041432, "support": 285.0}, "1": {"precision": 0.8299120234604106, "recall": 0.9370860927152318, "f1-score": 0.880248833592535, "support": 302.0}, "accuracy": 0.868824531516184, "macro avg": {"precision": 0.8763381255513435, "recall": 0.8667886603927036, "f1-score": 0.867619708698339, "support": 587.0}, "weighted avg": {"precision": 0.8749935876713676, "recall": 0.868824531516184, "f1-score": 0.8679854584823277, "support": 587.0}}, "confusion_matrix": [[227, 58], [19, 283]], "roc_auc": 0.9320959683978157, "training_time": 0.077057, "train_size": 1369, "test_size": 587, "algorithm_type": "Decision Tree"}, "DT_Entropy": {"accuracy": 0.858603066439523, "precision": [0.907258064516129, 0.8230088495575221], "recall": [0.7894736842105263, 0.9238410596026491], "f1_score": [0.8442776735459663, 0.8705148205928237], "support": [285, 302], "classification_report": {"0": {"precision": 0.907258064516129, "recall": 0.7894736842105263, "f1-score": 0.8442776735459663, "support": 285.0}, "1": {"precision": 0.8230088495575221, "recall": 0.9238410596026491, "f1-score": 0.8705148205928237, "support": 302.0}, "accuracy": 0.858603066439523, "macro avg": {"precision": 0.8651334570368255, "recall": 0.8566573719065878, "f1-score": 0.857396247069395, "support": 587.0}, "weighted avg": {"precision": 0.8639134939582085, "recall": 0.858603066439523, "f1-score": 0.8577761716859167, "support": 587.0}}, "confusion_matrix": [[225, 60], [23, 279]], "roc_auc": 0.9209829208783548, "training_time": 0.070655, "train_size": 1369, "test_size": 587, "algorithm_type": "Decision Tree"}, "DT_Log_Loss": {"accuracy": 0.858603066439523, "precision": [0.907258064516129, 0.8230088495575221], "recall": [0.7894736842105263, 0.9238410596026491], "f1_score": [0.8442776735459663, 0.8705148205928237], "support": [285, 302], "classification_report": {"0": {"precision": 0.907258064516129, "recall": 0.7894736842105263, "f1-score": 0.8442776735459663, "support": 285.0}, "1": {"precision": 0.8230088495575221, "recall": 0.9238410596026491, "f1-score": 0.8705148205928237, "support": 302.0}, "accuracy": 0.858603066439523, "macro avg": {"precision": 0.8651334570368255, "recall": 0.8566573719065878, "f1-score": 0.857396247069395, "support": 587.0}, "weighted avg": {"precision": 0.8639134939582085, "recall": 0.858603066439523, "f1-score": 0.8577761716859167, "support": 587.0}}, "confusion_matrix": [[225, 60], [23, 279]], "roc_auc": 0.9209829208783548, "training_time": 0.085074, "train_size": 1369, "test_size": 587, "algorithm_type": "Decision Tree"}, "DT_Gini_Pruned": {"accuracy": 0.8824531516183987, "precision": [0.8121387283236994, 0.983402489626556], "recall": [0.9859649122807017, 0.7847682119205298], "f1_score": [0.8906497622820919, 0.8729281767955801], "support": [285, 302], "classification_report": {"0": {"precision": 0.8121387283236994, "recall": 0.9859649122807017, "f1-score": 0.8906497622820919, "support": 285.0}, "1": {"precision": 0.983402489626556, "recall": 0.7847682119205298, "f1-score": 0.8729281767955801, "support": 302.0}, "accuracy": 0.8824531516183987, "macro avg": {"precision": 0.8977706089751276, "recall": 0.8853665621006157, "f1-score": 0.881788969538836, "support": 587.0}, "weighted avg": {"precision": 0.9002505782614553, "recall": 0.8824531516183987, "f1-score": 0.8815323537353686, "support": 587.0}}, "confusion_matrix": [[281, 4], [65, 237]], "roc_auc": 0.8841117694899501, "training_time": 0.036842, "train_size": 1369, "test_size": 587, "algorithm_type": "Decision Tree"}, "DT_Entropy_Pruned": {"accuracy": 0.8671209540034072, "precision": [0.7899159663865546, 0.9869565217391304], "recall": [0.9894736842105263, 0.7516556291390728], "f1_score": [0.8785046728971962, 0.8533834586466166], "support": [285, 302], "classification_report": {"0": {"precision": 0.7899159663865546, "recall": 0.9894736842105263, "f1-score": 0.8785046728971962, "support": 285.0}, "1": {"precision": 0.9869565217391304, "recall": 0.7516556291390728, "f1-score": 0.8533834586466166, "support": 302.0}, "accuracy": 0.8671209540034072, "macro avg": {"precision": 0.8884362440628425, "recall": 0.8705646566747995, "f1-score": 0.8659440657719064, "support": 587.0}, "weighted avg": {"precision": 0.891289471866074, "recall": 0.8671209540034072, "f1-score": 0.8655803003185334, "support": 587.0}}, "confusion_matrix": [[282, 3], [75, 227]], "roc_auc": 0.8784768211920528, "training_time": 0.047361, "train_size": 1369, "test_size": 587, "algorithm_type": "Decision Tree"}, "DT_Best_First": {"accuracy": 0.868824531516184, "precision": [0.9227642276422764, 0.8299120234604106], "recall": [0.7964912280701755, 0.9370860927152318], "f1_score": [0.8549905838041432, 0.880248833592535], "support": [285, 302], "classification_report": {"0": {"precision": 0.9227642276422764, "recall": 0.7964912280701755, "f1-score": 0.8549905838041432, "support": 285.0}, "1": {"precision": 0.8299120234604106, "recall": 0.9370860927152318, "f1-score": 0.880248833592535, "support": 302.0}, "accuracy": 0.868824531516184, "macro avg": {"precision": 0.8763381255513435, "recall": 0.8667886603927036, "f1-score": 0.867619708698339, "support": 587.0}, "weighted avg": {"precision": 0.8749935876713676, "recall": 0.868824531516184, "f1-score": 0.8679854584823277, "support": 587.0}}, "confusion_matrix": [[227, 58], [19, 283]], "roc_auc": 0.9320959683978157, "training_time": 0.083609, "train_size": 1369, "test_size": 587, "algorithm_type": "Decision Tree"}, "DT_Random_Split": {"accuracy": 0.8654173764906303, "precision": [0.912, 0.8308605341246291], "recall": [0.8, 0.9271523178807947], "f1_score": [0.8523364485981308, 0.8763693270735524], "support": [285, 302], "classification_report": {"0": {"precision": 0.912, "recall": 0.8, "f1-score": 0.8523364485981308, "support": 285.0}, "1": {"precision": 0.8308605341246291, "recall": 0.9271523178807947, "f1-score": 0.8763693270735524, "support": 302.0}, "accuracy": 0.8654173764906303, "macro avg": {"precision": 0.8714302670623146, "recall": 0.8635761589403974, "f1-score": 0.8643528878358416, "support": 587.0}, "weighted avg": {"precision": 0.8702553344218705, "recall": 0.8654173764906303, "f1-score": 0.8647008937422148, "support": 587.0}}, "confusion_matrix": [[228, 57], [22, 280]], "roc_auc": 0.9275763913093994, "training_time": 0.06621, "train_size": 1369, "test_size": 587, "algorithm_type": "Decision Tree"}, "RF_Gini": {"accuracy": 0.889267461669506, "precision": [0.9296875, 0.8580060422960725], "recall": [0.8350877192982457, 0.9403973509933775], "f1_score": [0.8798521256931608, 0.8973143759873617], "support": [285, 302], "classification_report": {"0": {"precision": 0.9296875, "recall": 0.8350877192982457, "f1-score": 0.8798521256931608, "support": 285.0}, "1": {"precision": 0.8580060422960725, "recall": 0.9403973509933775, "f1-score": 0.8973143759873617, "support": 302.0}, "accuracy": 0.889267461669506, "macro avg": {"precision": 0.8938467711480362, "recall": 0.8877425351458116, "f1-score": 0.8885832508402614, "support": 587.0}, "weighted avg": {"precision": 0.8928087943329027, "recall": 0.889267461669506, "f1-score": 0.8888361113641126, "support": 587.0}}, "confusion_matrix": [[238, 47], [18, 284]], "roc_auc": 0.9572731497618218, "training_time": 0.567947, "train_size": 1369, "test_size": 587, "algorithm_type": "Random Forest"}, "RF_Entropy": {"accuracy": 0.8807495741056218, "precision": [0.9282868525896414, 0.8452380952380952], "recall": [0.8175438596491228, 0.9403973509933775], "f1_score": [0.8694029850746269, 0.890282131661442], "support": [285, 302], "classification_report": {"0": {"precision": 0.9282868525896414, "recall": 0.8175438596491228, "f1-score": 0.8694029850746269, "support": 285.0}, "1": {"precision": 0.8452380952380952, "recall": 0.9403973509933775, "f1-score": 0.890282131661442, "support": 302.0}, "accuracy": 0.8807495741056218, "macro avg": {"precision": 0.8867624739138683, "recall": 0.8789706053212502, "f1-score": 0.8798425583680345, "support": 587.0}, "weighted avg": {"precision": 0.885559893952219, "recall": 0.8807495741056218, "f1-score": 0.8801448969472302, "support": 587.0}}, "confusion_matrix": [[233, 52], [18, 284]], "roc_auc": 0.9574648541884513, "training_time": 0.617227, "train_size": 1369, "test_size": 587, "algorithm_type": "Random Forest"}, "RF_Log_Loss": {"accuracy": 0.8807495741056218, "precision": [0.9282868525896414, 0.8452380952380952], "recall": [0.8175438596491228, 0.9403973509933775], "f1_score": [0.8694029850746269, 0.890282131661442], "support": [285, 302], "classification_report": {"0": {"precision": 0.9282868525896414, "recall": 0.8175438596491228, "f1-score": 0.8694029850746269, "support": 285.0}, "1": {"precision": 0.8452380952380952, "recall": 0.9403973509933775, "f1-score": 0.890282131661442, "support": 302.0}, "accuracy": 0.8807495741056218, "macro avg": {"precision": 0.8867624739138683, "recall": 0.8789706053212502, "f1-score": 0.8798425583680345, "support": 587.0}, "weighted avg": {"precision": 0.885559893952219, "recall": 0.8807495741056218, "f1-score": 0.8801448969472302, "support": 587.0}}, "confusion_matrix": [[233, 52], [18, 284]], "roc_auc": 0.9574648541884513, "training_time": 0.655725, "train_size": 1369, "test_size": 587, "algorithm_type": "Random Forest"}, "RF_Large": {"accuracy": 0.8739352640545145, "precision": [0.8075801749271136, 0.9672131147540983], "recall": [0.9719298245614035, 0.7814569536423841], "f1_score": [0.8821656050955414, 0.8644688644688645], "support": [285, 302], "classification_report": {"0": {"precision": 0.8075801749271136, "recall": 0.9719298245614035, "f1-score": 0.8821656050955414, "support": 285.0}, "1": {"precision": 0.9672131147540983, "recall": 0.7814569536423841, "f1-score": 0.8644688644688645, "support": 302.0}, "accuracy": 0.8739352640545145, "macro avg": {"precision": 0.887396644840606, "recall": 0.8766933891018938, "f1-score": 0.8733172347822029, "support": 587.0}, "weighted avg": {"precision": 0.8897081950766016, "recall": 0.8739352640545145, "f1-score": 0.873060978742464, "support": 587.0}}, "confusion_matrix": [[277, 8], [66, 236]], "roc_auc": 0.9511792726850237, "training_time": 0.495378, "train_size": 1369, "test_size": 587, "algorithm_type": "Random Forest"}, "RF_Small": {"accuracy": 0.868824531516184, "precision": [0.804093567251462, 0.9591836734693877], "recall": [0.9649122807017544, 0.7781456953642384], "f1_score": [0.8771929824561403, 0.8592321755027422], "support": [285, 302], "classification_report": {"0": {"precision": 0.804093567251462, "recall": 0.9649122807017544, "f1-score": 0.8771929824561403, "support": 285.0}, "1": {"precision": 0.9591836734693877, "recall": 0.7781456953642384, "f1-score": 0.8592321755027422, "support": 302.0}, "accuracy": 0.868824531516184, "macro avg": {"precision": 0.8816386203604248, "recall": 0.8715289880329964, "f1-score": 0.8682125789794413, "support": 587.0}, "weighted avg": {"precision": 0.8838843885083845, "recall": 0.868824531516184, "f1-score": 0.8679524991513256, "support": 587.0}}, "confusion_matrix": [[275, 10], [67, 235]], "roc_auc": 0.9438654583478563, "training_time": 0.107471, "train_size": 1369, "test_size": 587, "algorithm_type": "Random Forest"}, "ExtraTrees": {"accuracy": 0.9011925042589438, "precision": [0.9315589353612167, 0.8765432098765432], "recall": [0.8596491228070176, 0.9403973509933775], "f1_score": [0.8941605839416058, 0.9073482428115016], "support": [285, 302], "classification_report": {"0": {"precision": 0.9315589353612167, "recall": 0.8596491228070176, "f1-score": 0.8941605839416058, "support": 285.0}, "1": {"precision": 0.8765432098765432, "recall": 0.9403973509933775, "f1-score": 0.9073482428115016, "support": 302.0}, "accuracy": 0.9011925042589438, "macro avg": {"precision": 0.90405107261888, "recall": 0.9000232369001975, "f1-score": 0.9007544133765537, "support": 587.0}, "weighted avg": {"precision": 0.9032544224202093, "recall": 0.9011925042589438, "f1-score": 0.9009453760688776, "support": 587.0}}, "confusion_matrix": [[245, 40], [18, 284]], "roc_auc": 0.9631927500871384, "training_time": 0.794376, "train_size": 1369, "test_size": 587, "algorithm_type": "Random Forest"}, "ExtraTrees_Large": {"accuracy": 0.879045996592845, "precision": [0.8147058823529412, 0.9676113360323887], "recall": [0.9719298245614035, 0.7913907284768212], "f1_score": [0.8864, 0.8706739526411658], "support": [285, 302], "classification_report": {"0": {"precision": 0.8147058823529412, "recall": 0.9719298245614035, "f1-score": 0.8864, "support": 285.0}, "1": {"precision": 0.9676113360323887, "recall": 0.7913907284768212, "f1-score": 0.8706739526411658, "support": 302.0}, "accuracy": 0.879045996592845, "macro avg": {"precision": 0.8911586091926649, "recall": 0.8816602765191124, "f1-score": 0.8785369763205828, "support": 587.0}, "weighted avg": {"precision": 0.8933727426786536, "recall": 0.879045996592845, "f1-score": 0.8783092567250972, "support": 587.0}}, "confusion_matrix": [[277, 8], [63, 239]], "roc_auc": 0.9474381317532241, "training_time": 0.350631, "train_size": 1369, "test_size": 587, "algorithm_type": "Random Forest"}, "GradientBoosting": {"accuracy": 0.8977853492333902, "precision": [0.8299120234604106, 0.991869918699187], "recall": [0.9929824561403509, 0.8079470198675497], "f1_score": [0.9041533546325878, 0.8905109489051095], "support": [285, 302], "classification_report": {"0": {"precision": 0.8299120234604106, "recall": 0.9929824561403509, "f1-score": 0.9041533546325878, "support": 285.0}, "1": {"precision": 0.991869918699187, "recall": 0.8079470198675497, "f1-score": 0.8905109489051095, "support": 302.0}, "accuracy": 0.8977853492333902, "macro avg": {"precision": 0.9108909710797988, "recall": 0.9004647380039503, "f1-score": 0.8973321517688486, "support": 587.0}, "weighted avg": {"precision": 0.913236187620735, "recall": 0.8977853492333902, "f1-score": 0.8971346041560998, "support": 587.0}}, "confusion_matrix": [[283, 2], [58, 244]], "roc_auc": 0.9470837690252119, "training_time": 0.732246, "train_size": 1369, "test_size": 587, "algorithm_type": "Ensemble"}, "AdaBoost": {"accuracy": 0.8841567291311755, "precision": [0.8359133126934984, 0.9431818181818182], "recall": [0.9473684210526315, 0.8245033112582781], "f1_score": [0.8881578947368421, 0.8798586572438163], "support": [285, 302], "classification_report": {"0": {"precision": 0.8359133126934984, "recall": 0.9473684210526315, "f1-score": 0.8881578947368421, "support": 285.0}, "1": {"precision": 0.9431818181818182, "recall": 0.8245033112582781, "f1-score": 0.8798586572438163, "support": 302.0}, "accuracy": 0.8841567291311755, "macro avg": {"precision": 0.8895475654376583, "recall": 0.8859358661554548, "f1-score": 0.8840082759903292, "support": 587.0}, "weighted avg": {"precision": 0.8911008572547806, "recall": 0.8841567291311755, "f1-score": 0.8838880996382155, "support": 587.0}}, "confusion_matrix": [[270, 15], [53, 249]], "roc_auc": 0.9373242709422562, "training_time": 0.431842, "train_size": 1369, "test_size": 587, "algorithm_type": "Ensemble"}, "AdaBoost_DT": {"accuracy": 0.8654173764906303, "precision": [0.90234375, 0.8368580060422961], "recall": [0.8105263157894737, 0.9172185430463576], "f1_score": [0.8539741219963032, 0.8751974723538705], "support": [285, 302], "classification_report": {"0": {"precision": 0.90234375, "recall": 0.8105263157894737, "f1-score": 0.8539741219963032, "support": 285.0}, "1": {"precision": 0.8368580060422961, "recall": 0.9172185430463576, "f1-score": 0.8751974723538705, "support": 302.0}, "accuracy": 0.8654173764906303, "macro avg": {"precision": 0.869600878021148, "recall": 0.8638724294179156, "f1-score": 0.8645857971750868, "support": 587.0}, "weighted avg": {"precision": 0.8686526176742307, "recall": 0.8654173764906303, "f1-score": 0.8648931199656138, "support": 587.0}}, "confusion_matrix": [[231, 54], [25, 277]], "roc_auc": 0.9522597885442082, "training_time": 0.885119, "train_size": 1369, "test_size": 587, "algorithm_type": "Decision Tree"}}, "train_65_test_35": {"SVM_Linear": {"accuracy": 0.9007299270072993, "precision": [0.8610354223433242, 0.9465408805031447], "recall": [0.948948948948949, 0.8551136363636364], "f1_score": [0.9028571428571428, 0.8985074626865671], "support": [333, 352], "classification_report": {"0": {"precision": 0.8610354223433242, "recall": 0.948948948948949, "f1-score": 0.9028571428571428, "support": 333.0}, "1": {"precision": 0.9465408805031447, "recall": 0.8551136363636364, "f1-score": 0.8985074626865671, "support": 352.0}, "accuracy": 0.9007299270072993, "macro avg": {"precision": 0.9037881514232344, "recall": 0.9020312926562927, "f1-score": 0.9006823027718549, "support": 685.0}, "weighted avg": {"precision": 0.9049739935436991, "recall": 0.9007299270072993, "f1-score": 0.9006219787402922, "support": 685.0}}, "confusion_matrix": [[316, 17], [51, 301]], "roc_auc": 0.9637762762762764, "training_time": 0.304597, "train_size": 1271, "test_size": 685, "algorithm_type": "Support Vector Machine"}, "SVM_Polynomial": {"accuracy": 0.872992700729927, "precision": [0.875, 0.8711484593837535], "recall": [0.8618618618618619, 0.8835227272727273], "f1_score": [0.8683812405446294, 0.8772919605077574], "support": [333, 352], "classification_report": {"0": {"precision": 0.875, "recall": 0.8618618618618619, "f1-score": 0.8683812405446294, "support": 333.0}, "1": {"precision": 0.8711484593837535, "recall": 0.8835227272727273, "f1-score": 0.8772919605077574, "support": 352.0}, "accuracy": 0.872992700729927, "macro avg": {"precision": 0.8730742296918768, "recall": 0.8726922945672946, "f1-score": 0.8728366005261934, "support": 685.0}, "weighted avg": {"precision": 0.873020814165082, "recall": 0.872992700729927, "f1-score": 0.8729601798541492, "support": 685.0}}, "confusion_matrix": [[287, 46], [41, 311]], "roc_auc": 0.9374488124488124, "training_time": 0.474958, "train_size": 1271, "test_size": 685, "algorithm_type": "Support Vector Machine"}, "SVM_RBF": {"accuracy": 0.9065693430656935, "precision": [0.8788732394366198, 0.9363636363636364], "recall": [0.9369369369369369, 0.8778409090909091], "f1_score": [0.9069767441860465, 0.906158357771261], "support": [333, 352], "classification_report": {"0": {"precision": 0.8788732394366198, "recall": 0.9369369369369369, "f1-score": 0.9069767441860465, "support": 333.0}, "1": {"precision": 0.9363636363636364, "recall": 0.8778409090909091, "f1-score": 0.906158357771261, "support": 352.0}, "accuracy": 0.9065693430656935, "macro avg": {"precision": 0.9076184379001281, "recall": 0.9073889230139229, "f1-score": 0.9065675509786537, "support": 685.0}, "weighted avg": {"precision": 0.9084157499742984, "recall": 0.9065693430656935, "f1-score": 0.9065562010940692, "support": 685.0}}, "confusion_matrix": [[312, 21], [43, 309]], "roc_auc": 0.9487015424515424, "training_time": 0.506284, "train_size": 1271, "test_size": 685, "algorithm_type": "Support Vector Machine"}, "SVM_Sigmoid": {"accuracy": 0.9007299270072993, "precision": [0.8571428571428571, 0.9522292993630573], "recall": [0.954954954954955, 0.8494318181818182], "f1_score": [0.9034090909090909, 0.8978978978978979], "support": [333, 352], "classification_report": {"0": {"precision": 0.8571428571428571, "recall": 0.954954954954955, "f1-score": 0.9034090909090909, "support": 333.0}, "1": {"precision": 0.9522292993630573, "recall": 0.8494318181818182, "f1-score": 0.8978978978978979, "support": 352.0}, "accuracy": 0.9007299270072993, "macro avg": {"precision": 0.9046860782529572, "recall": 0.9021933865683867, "f1-score": 0.9006534944034944, "support": 685.0}, "weighted avg": {"precision": 0.9060047953348432, "recall": 0.9007299270072993, "f1-score": 0.9005770617996895, "support": 685.0}}, "confusion_matrix": [[318, 15], [53, 299]], "roc_auc": 0.9634435571935572, "training_time": 0.315534, "train_size": 1271, "test_size": 685, "algorithm_type": "Support Vector Machine"}, "SVM_RBF_Tuned": {"accuracy": 0.9036496350364963, "precision": [0.8825214899713467, 0.9255952380952381], "recall": [0.924924924924925, 0.8835227272727273], "f1_score": [0.9032258064516129, 0.9040697674418605], "support": [333, 352], "classification_report": {"0": {"precision": 0.8825214899713467, "recall": 0.924924924924925, "f1-score": 0.9032258064516129, "support": 333.0}, "1": {"precision": 0.9255952380952381, "recall": 0.8835227272727273, "f1-score": 0.9040697674418605, "support": 352.0}, "accuracy": 0.9036496350364963, "macro avg": {"precision": 0.9040583640332924, "recall": 0.9042238260988261, "f1-score": 0.9036477869467368, "support": 685.0}, "weighted avg": {"precision": 0.904655737182456, "recall": 0.9036496350364963, "f1-score": 0.9036594915152144, "support": 685.0}}, "confusion_matrix": [[308, 25], [41, 311]], "roc_auc": 0.9476777914277913, "training_time": 0.499647, "train_size": 1271, "test_size": 685, "algorithm_type": "Support Vector Machine"}, "SVM_Linear_Tuned": {"accuracy": 0.9007299270072993, "precision": [0.8610354223433242, 0.9465408805031447], "recall": [0.948948948948949, 0.8551136363636364], "f1_score": [0.9028571428571428, 0.8985074626865671], "support": [333, 352], "classification_report": {"0": {"precision": 0.8610354223433242, "recall": 0.948948948948949, "f1-score": 0.9028571428571428, "support": 333.0}, "1": {"precision": 0.9465408805031447, "recall": 0.8551136363636364, "f1-score": 0.8985074626865671, "support": 352.0}, "accuracy": 0.9007299270072993, "macro avg": {"precision": 0.9037881514232344, "recall": 0.9020312926562927, "f1-score": 0.9006823027718549, "support": 685.0}, "weighted avg": {"precision": 0.9049739935436991, "recall": 0.9007299270072993, "f1-score": 0.9006219787402922, "support": 685.0}}, "confusion_matrix": [[316, 17], [51, 301]], "roc_auc": 0.9637762762762764, "training_time": 0.343782, "train_size": 1271, "test_size": 685, "algorithm_type": "Support Vector Machine"}, "NB_Multinomial": {"accuracy": 0.8934306569343066, "precision": [0.9193548387096774, 0.872], "recall": [0.8558558558558559, 0.9289772727272727], "f1_score": [0.8864696734059098, 0.8995873452544704], "support": [333, 352], "classification_report": {"0": {"precision": 0.9193548387096774, "recall": 0.8558558558558559, "f1-score": 0.8864696734059098, "support": 333.0}, "1": {"precision": 0.872, "recall": 0.9289772727272727, "f1-score": 0.8995873452544704, "support": 352.0}, "accuracy": 0.8934306569343066, "macro avg": {"precision": 0.8956774193548387, "recall": 0.8924165642915642, "f1-score": 0.8930285093301901, "support": 685.0}, "weighted avg": {"precision": 0.8950206734165294, "recall": 0.8934306569343066, "f1-score": 0.893210433246338, "support": 685.0}}, "confusion_matrix": [[285, 48], [25, 327]], "roc_auc": 0.9567550505050505, "training_time": 0.023143, "train_size": 1271, "test_size": 685, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli": {"accuracy": 0.8467153284671532, "precision": [0.7663551401869159, 0.980544747081712], "recall": [0.984984984984985, 0.7159090909090909], "f1_score": [0.8620236530880421, 0.8275862068965517], "support": [333, 352], "classification_report": {"0": {"precision": 0.7663551401869159, "recall": 0.984984984984985, "f1-score": 0.8620236530880421, "support": 333.0}, "1": {"precision": 0.980544747081712, "recall": 0.7159090909090909, "f1-score": 0.8275862068965517, "support": 352.0}, "accuracy": 0.8467153284671532, "macro avg": {"precision": 0.8734499436343139, "recall": 0.8504470379470379, "f1-score": 0.8448049299922968, "support": 685.0}, "weighted avg": {"precision": 0.8764204564306651, "recall": 0.8467153284671532, "f1-score": 0.8443273303735829, "support": 685.0}}, "confusion_matrix": [[328, 5], [100, 252]], "roc_auc": 0.9460611179361178, "training_time": 0.01596, "train_size": 1271, "test_size": 685, "algorithm_type": "<PERSON><PERSON>"}, "NB_Complement": {"accuracy": 0.8832116788321168, "precision": [0.8624641833810889, 0.9047619047619048], "recall": [0.9039039039039038, 0.8636363636363636], "f1_score": [0.8826979472140762, 0.8837209302325582], "support": [333, 352], "classification_report": {"0": {"precision": 0.8624641833810889, "recall": 0.9039039039039038, "f1-score": 0.8826979472140762, "support": 333.0}, "1": {"precision": 0.9047619047619048, "recall": 0.8636363636363636, "f1-score": 0.8837209302325582, "support": 352.0}, "accuracy": 0.8832116788321168, "macro avg": {"precision": 0.8836130440714969, "recall": 0.8837701337701338, "f1-score": 0.8832094387233171, "support": 685.0}, "weighted avg": {"precision": 0.8841996548059754, "recall": 0.8832116788321168, "f1-score": 0.883223626079048, "support": 685.0}}, "confusion_matrix": [[301, 32], [48, 304]], "roc_auc": 0.9567550505050505, "training_time": 0.013929, "train_size": 1271, "test_size": 685, "algorithm_type": "<PERSON><PERSON>"}, "NB_Multinomial_Tuned": {"accuracy": 0.8890510948905109, "precision": [0.9105431309904153, 0.8709677419354839], "recall": [0.8558558558558559, 0.9204545454545454], "f1_score": [0.8823529411764706, 0.8950276243093923], "support": [333, 352], "classification_report": {"0": {"precision": 0.9105431309904153, "recall": 0.8558558558558559, "f1-score": 0.8823529411764706, "support": 333.0}, "1": {"precision": 0.8709677419354839, "recall": 0.9204545454545454, "f1-score": 0.8950276243093923, "support": 352.0}, "accuracy": 0.8890510948905109, "macro avg": {"precision": 0.8907554364629495, "recall": 0.8881552006552007, "f1-score": 0.8886902827429315, "support": 685.0}, "weighted avg": {"precision": 0.8902065807023336, "recall": 0.8890510948905109, "f1-score": 0.8888660630199573, "support": 685.0}}, "confusion_matrix": [[285, 48], [28, 324]], "roc_auc": 0.9515339202839203, "training_time": 0.018869, "train_size": 1271, "test_size": 685, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli_Tuned": {"accuracy": 0.8744525547445255, "precision": [0.8126582278481013, 0.9586206896551724], "recall": [0.963963963963964, 0.7897727272727273], "f1_score": [0.8818681318681318, 0.8660436137071651], "support": [333, 352], "classification_report": {"0": {"precision": 0.8126582278481013, "recall": 0.963963963963964, "f1-score": 0.8818681318681318, "support": 333.0}, "1": {"precision": 0.9586206896551724, "recall": 0.7897727272727273, "f1-score": 0.8660436137071651, "support": 352.0}, "accuracy": 0.8744525547445255, "macro avg": {"precision": 0.8856394587516369, "recall": 0.8768683456183457, "f1-score": 0.8739558727876484, "support": 685.0}, "weighted avg": {"precision": 0.8876637556672093, "recall": 0.8744525547445255, "f1-score": 0.873736408667168, "support": 685.0}}, "confusion_matrix": [[321, 12], [74, 278]], "roc_auc": 0.9384597665847665, "training_time": 0.019495, "train_size": 1271, "test_size": 685, "algorithm_type": "<PERSON><PERSON>"}, "LR_L1": {"accuracy": 0.8773722627737226, "precision": [0.8151898734177215, 0.9620689655172414], "recall": [0.9669669669669669, 0.7926136363636364], "f1_score": [0.8846153846153846, 0.8691588785046729], "support": [333, 352], "classification_report": {"0": {"precision": 0.8151898734177215, "recall": 0.9669669669669669, "f1-score": 0.8846153846153846, "support": 333.0}, "1": {"precision": 0.9620689655172414, "recall": 0.7926136363636364, "f1-score": 0.8691588785046729, "support": 352.0}, "accuracy": 0.8773722627737226, "macro avg": {"precision": 0.8886294194674815, "recall": 0.8797903016653017, "f1-score": 0.8768871315600287, "support": 685.0}, "weighted avg": {"precision": 0.8906664287739712, "recall": 0.8773722627737226, "f1-score": 0.8766727712563035, "support": 685.0}}, "confusion_matrix": [[322, 11], [73, 279]], "roc_auc": 0.9438216625716626, "training_time": 0.022273, "train_size": 1271, "test_size": 685, "algorithm_type": "Logistic Regression"}, "LR_L2": {"accuracy": 0.8978102189781022, "precision": [0.8583106267029973, 0.9433962264150944], "recall": [0.9459459459459459, 0.8522727272727273], "f1_score": [0.9, 0.8955223880597015], "support": [333, 352], "classification_report": {"0": {"precision": 0.8583106267029973, "recall": 0.9459459459459459, "f1-score": 0.9, "support": 333.0}, "1": {"precision": 0.9433962264150944, "recall": 0.8522727272727273, "f1-score": 0.8955223880597015, "support": 352.0}, "accuracy": 0.8978102189781022, "macro avg": {"precision": 0.9008534265590458, "recall": 0.8991093366093366, "f1-score": 0.8977611940298508, "support": 685.0}, "weighted avg": {"precision": 0.902033445825126, "recall": 0.8978102189781022, "f1-score": 0.8976990957620655, "support": 685.0}}, "confusion_matrix": [[315, 18], [52, 300]], "roc_auc": 0.957591113841114, "training_time": 0.016846, "train_size": 1271, "test_size": 685, "algorithm_type": "Logistic Regression"}, "LR_ElasticNet": {"accuracy": 0.8846715328467153, "precision": [0.8342105263157895, 0.9475409836065574], "recall": [0.9519519519519519, 0.8210227272727273], "f1_score": [0.8892005610098177, 0.8797564687975646], "support": [333, 352], "classification_report": {"0": {"precision": 0.8342105263157895, "recall": 0.9519519519519519, "f1-score": 0.8892005610098177, "support": 333.0}, "1": {"precision": 0.9475409836065574, "recall": 0.8210227272727273, "f1-score": 0.8797564687975646, "support": 352.0}, "accuracy": 0.8846715328467153, "macro avg": {"precision": 0.8908757549611734, "recall": 0.8864873396123396, "f1-score": 0.8844785149036911, "support": 685.0}, "weighted avg": {"precision": 0.8924474912301695, "recall": 0.8846715328467153, "f1-score": 0.8843475384423534, "support": 685.0}}, "confusion_matrix": [[317, 16], [63, 289]], "roc_auc": 0.9497679497679498, "training_time": 0.029847, "train_size": 1271, "test_size": 685, "algorithm_type": "Logistic Regression"}, "LR_LBFGS": {"accuracy": 0.8978102189781022, "precision": [0.8583106267029973, 0.9433962264150944], "recall": [0.9459459459459459, 0.8522727272727273], "f1_score": [0.9, 0.8955223880597015], "support": [333, 352], "classification_report": {"0": {"precision": 0.8583106267029973, "recall": 0.9459459459459459, "f1-score": 0.9, "support": 333.0}, "1": {"precision": 0.9433962264150944, "recall": 0.8522727272727273, "f1-score": 0.8955223880597015, "support": 352.0}, "accuracy": 0.8978102189781022, "macro avg": {"precision": 0.9008534265590458, "recall": 0.8991093366093366, "f1-score": 0.8977611940298508, "support": 685.0}, "weighted avg": {"precision": 0.902033445825126, "recall": 0.8978102189781022, "f1-score": 0.8976990957620655, "support": 685.0}}, "confusion_matrix": [[315, 18], [52, 300]], "roc_auc": 0.9575911138411138, "training_time": 0.042764, "train_size": 1271, "test_size": 685, "algorithm_type": "Logistic Regression"}, "LR_SAG": {"accuracy": 0.8978102189781022, "precision": [0.8583106267029973, 0.9433962264150944], "recall": [0.9459459459459459, 0.8522727272727273], "f1_score": [0.9, 0.8955223880597015], "support": [333, 352], "classification_report": {"0": {"precision": 0.8583106267029973, "recall": 0.9459459459459459, "f1-score": 0.9, "support": 333.0}, "1": {"precision": 0.9433962264150944, "recall": 0.8522727272727273, "f1-score": 0.8955223880597015, "support": 352.0}, "accuracy": 0.8978102189781022, "macro avg": {"precision": 0.9008534265590458, "recall": 0.8991093366093366, "f1-score": 0.8977611940298508, "support": 685.0}, "weighted avg": {"precision": 0.902033445825126, "recall": 0.8978102189781022, "f1-score": 0.8976990957620655, "support": 685.0}}, "confusion_matrix": [[315, 18], [52, 300]], "roc_auc": 0.9575911138411138, "training_time": 0.02187, "train_size": 1271, "test_size": 685, "algorithm_type": "Logistic Regression"}, "LR_SAGA": {"accuracy": 0.8978102189781022, "precision": [0.8583106267029973, 0.9433962264150944], "recall": [0.9459459459459459, 0.8522727272727273], "f1_score": [0.9, 0.8955223880597015], "support": [333, 352], "classification_report": {"0": {"precision": 0.8583106267029973, "recall": 0.9459459459459459, "f1-score": 0.9, "support": 333.0}, "1": {"precision": 0.9433962264150944, "recall": 0.8522727272727273, "f1-score": 0.8955223880597015, "support": 352.0}, "accuracy": 0.8978102189781022, "macro avg": {"precision": 0.9008534265590458, "recall": 0.8991093366093366, "f1-score": 0.8977611940298508, "support": 685.0}, "weighted avg": {"precision": 0.902033445825126, "recall": 0.8978102189781022, "f1-score": 0.8976990957620655, "support": 685.0}}, "confusion_matrix": [[315, 18], [52, 300]], "roc_auc": 0.9575996450996451, "training_time": 0.022795, "train_size": 1271, "test_size": 685, "algorithm_type": "Logistic Regression"}, "DT_Gini": {"accuracy": 0.8613138686131386, "precision": [0.916083916083916, 0.8220551378446115], "recall": [0.7867867867867868, 0.9318181818181818], "f1_score": [0.8465266558966075, 0.8735019973368842], "support": [333, 352], "classification_report": {"0": {"precision": 0.916083916083916, "recall": 0.7867867867867868, "f1-score": 0.8465266558966075, "support": 333.0}, "1": {"precision": 0.8220551378446115, "recall": 0.9318181818181818, "f1-score": 0.8735019973368842, "support": 352.0}, "accuracy": 0.8613138686131386, "macro avg": {"precision": 0.8690695269642638, "recall": 0.8593024843024843, "f1-score": 0.8600143266167458, "support": 685.0}, "weighted avg": {"precision": 0.8677654782149595, "recall": 0.8613138686131386, "f1-score": 0.8603884371914651, "support": 685.0}}, "confusion_matrix": [[262, 71], [24, 328]], "roc_auc": 0.9262984575484576, "training_time": 0.062686, "train_size": 1271, "test_size": 685, "algorithm_type": "Decision Tree"}, "DT_Entropy": {"accuracy": 0.8613138686131386, "precision": [0.9131944444444444, 0.8236775818639799], "recall": [0.7897897897897898, 0.9289772727272727], "f1_score": [0.8470209339774557, 0.8731642189586115], "support": [333, 352], "classification_report": {"0": {"precision": 0.9131944444444444, "recall": 0.7897897897897898, "f1-score": 0.8470209339774557, "support": 333.0}, "1": {"precision": 0.8236775818639799, "recall": 0.9289772727272727, "f1-score": 0.8731642189586115, "support": 352.0}, "accuracy": 0.8613138686131386, "macro avg": {"precision": 0.8684360131542121, "recall": 0.8593835312585312, "f1-score": 0.8600925764680336, "support": 685.0}, "weighted avg": {"precision": 0.8671945384176948, "recall": 0.8613138686131386, "f1-score": 0.8604551475736116, "support": 685.0}}, "confusion_matrix": [[263, 70], [25, 327]], "roc_auc": 0.9227963759213759, "training_time": 0.072757, "train_size": 1271, "test_size": 685, "algorithm_type": "Decision Tree"}, "DT_Log_Loss": {"accuracy": 0.8613138686131386, "precision": [0.9131944444444444, 0.8236775818639799], "recall": [0.7897897897897898, 0.9289772727272727], "f1_score": [0.8470209339774557, 0.8731642189586115], "support": [333, 352], "classification_report": {"0": {"precision": 0.9131944444444444, "recall": 0.7897897897897898, "f1-score": 0.8470209339774557, "support": 333.0}, "1": {"precision": 0.8236775818639799, "recall": 0.9289772727272727, "f1-score": 0.8731642189586115, "support": 352.0}, "accuracy": 0.8613138686131386, "macro avg": {"precision": 0.8684360131542121, "recall": 0.8593835312585312, "f1-score": 0.8600925764680336, "support": 685.0}, "weighted avg": {"precision": 0.8671945384176948, "recall": 0.8613138686131386, "f1-score": 0.8604551475736116, "support": 685.0}}, "confusion_matrix": [[263, 70], [25, 327]], "roc_auc": 0.9227963759213759, "training_time": 0.079877, "train_size": 1271, "test_size": 685, "algorithm_type": "Decision Tree"}, "DT_Gini_Pruned": {"accuracy": 0.8817518248175182, "precision": [0.8073170731707318, 0.9927272727272727], "recall": [0.993993993993994, 0.7755681818181818], "f1_score": [0.8909825033647375, 0.8708133971291866], "support": [333, 352], "classification_report": {"0": {"precision": 0.8073170731707318, "recall": 0.993993993993994, "f1-score": 0.8909825033647375, "support": 333.0}, "1": {"precision": 0.9927272727272727, "recall": 0.7755681818181818, "f1-score": 0.8708133971291866, "support": 352.0}, "accuracy": 0.8817518248175182, "macro avg": {"precision": 0.9000221729490022, "recall": 0.8847810879060879, "f1-score": 0.880897950246962, "support": 685.0}, "weighted avg": {"precision": 0.9025935552786184, "recall": 0.8817518248175182, "f1-score": 0.8806182327152282, "support": 685.0}}, "confusion_matrix": [[331, 2], [79, 273]], "roc_auc": 0.8907615001365002, "training_time": 0.04306, "train_size": 1271, "test_size": 685, "algorithm_type": "Decision Tree"}, "DT_Entropy_Pruned": {"accuracy": 0.8656934306569343, "precision": [0.7917675544794189, 0.9779411764705882], "recall": [0.9819819819819819, 0.7556818181818182], "f1_score": [0.8766756032171582, 0.8525641025641025], "support": [333, 352], "classification_report": {"0": {"precision": 0.7917675544794189, "recall": 0.9819819819819819, "f1-score": 0.8766756032171582, "support": 333.0}, "1": {"precision": 0.9779411764705882, "recall": 0.7556818181818182, "f1-score": 0.8525641025641025, "support": 352.0}, "accuracy": 0.8656934306569343, "macro avg": {"precision": 0.8848543654750036, "recall": 0.8688319000819, "f1-score": 0.8646198528906304, "support": 685.0}, "weighted avg": {"precision": 0.8874363354150272, "recall": 0.8656934306569343, "f1-score": 0.86428545981588, "support": 685.0}}, "confusion_matrix": [[327, 6], [86, 266]], "roc_auc": 0.8875025593775595, "training_time": 0.035989, "train_size": 1271, "test_size": 685, "algorithm_type": "Decision Tree"}, "DT_Best_First": {"accuracy": 0.8613138686131386, "precision": [0.916083916083916, 0.8220551378446115], "recall": [0.7867867867867868, 0.9318181818181818], "f1_score": [0.8465266558966075, 0.8735019973368842], "support": [333, 352], "classification_report": {"0": {"precision": 0.916083916083916, "recall": 0.7867867867867868, "f1-score": 0.8465266558966075, "support": 333.0}, "1": {"precision": 0.8220551378446115, "recall": 0.9318181818181818, "f1-score": 0.8735019973368842, "support": 352.0}, "accuracy": 0.8613138686131386, "macro avg": {"precision": 0.8690695269642638, "recall": 0.8593024843024843, "f1-score": 0.8600143266167458, "support": 685.0}, "weighted avg": {"precision": 0.8677654782149595, "recall": 0.8613138686131386, "f1-score": 0.8603884371914651, "support": 685.0}}, "confusion_matrix": [[262, 71], [24, 328]], "roc_auc": 0.9262984575484576, "training_time": 0.057627, "train_size": 1271, "test_size": 685, "algorithm_type": "Decision Tree"}, "DT_Random_Split": {"accuracy": 0.8744525547445255, "precision": [0.9303135888501742, 0.8341708542713567], "recall": [0.8018018018018018, 0.9431818181818182], "f1_score": [0.8612903225806452, 0.8853333333333333], "support": [333, 352], "classification_report": {"0": {"precision": 0.9303135888501742, "recall": 0.8018018018018018, "f1-score": 0.8612903225806452, "support": 333.0}, "1": {"precision": 0.8341708542713567, "recall": 0.9431818181818182, "f1-score": 0.8853333333333333, "support": 352.0}, "accuracy": 0.8744525547445255, "macro avg": {"precision": 0.8822422215607655, "recall": 0.87249180999181, "f1-score": 0.8733118279569893, "support": 685.0}, "weighted avg": {"precision": 0.8809088551687966, "recall": 0.8744525547445255, "f1-score": 0.8736452711718075, "support": 685.0}}, "confusion_matrix": [[267, 66], [20, 332]], "roc_auc": 0.9347529347529346, "training_time": 0.083369, "train_size": 1271, "test_size": 685, "algorithm_type": "Decision Tree"}, "RF_Gini": {"accuracy": 0.8890510948905109, "precision": [0.9240924092409241, 0.8612565445026178], "recall": [0.8408408408408409, 0.9346590909090909], "f1_score": [0.8805031446540881, 0.896457765667575], "support": [333, 352], "classification_report": {"0": {"precision": 0.9240924092409241, "recall": 0.8408408408408409, "f1-score": 0.8805031446540881, "support": 333.0}, "1": {"precision": 0.8612565445026178, "recall": 0.9346590909090909, "f1-score": 0.896457765667575, "support": 352.0}, "accuracy": 0.8890510948905109, "macro avg": {"precision": 0.8926744768717709, "recall": 0.8877499658749659, "f1-score": 0.8884804551608315, "support": 685.0}, "weighted avg": {"precision": 0.8918030305724804, "recall": 0.8890510948905109, "f1-score": 0.888701723627442, "support": 685.0}}, "confusion_matrix": [[280, 53], [23, 329]], "roc_auc": 0.9572925197925197, "training_time": 0.504389, "train_size": 1271, "test_size": 685, "algorithm_type": "Random Forest"}, "RF_Entropy": {"accuracy": 0.8905109489051095, "precision": [0.9328859060402684, 0.8578811369509044], "recall": [0.8348348348348348, 0.9431818181818182], "f1_score": [0.8811410459587956, 0.8985115020297699], "support": [333, 352], "classification_report": {"0": {"precision": 0.9328859060402684, "recall": 0.8348348348348348, "f1-score": 0.8811410459587956, "support": 333.0}, "1": {"precision": 0.8578811369509044, "recall": 0.9431818181818182, "f1-score": 0.8985115020297699, "support": 352.0}, "accuracy": 0.8905109489051095, "macro avg": {"precision": 0.8953835214955864, "recall": 0.8890083265083265, "f1-score": 0.8898262739942828, "support": 685.0}, "weighted avg": {"precision": 0.8943433093695294, "recall": 0.8905109489051095, "f1-score": 0.8900671781295737, "support": 685.0}}, "confusion_matrix": [[278, 55], [20, 332]], "roc_auc": 0.9551426426426427, "training_time": 0.51252, "train_size": 1271, "test_size": 685, "algorithm_type": "Random Forest"}, "RF_Log_Loss": {"accuracy": 0.8905109489051095, "precision": [0.9328859060402684, 0.8578811369509044], "recall": [0.8348348348348348, 0.9431818181818182], "f1_score": [0.8811410459587956, 0.8985115020297699], "support": [333, 352], "classification_report": {"0": {"precision": 0.9328859060402684, "recall": 0.8348348348348348, "f1-score": 0.8811410459587956, "support": 333.0}, "1": {"precision": 0.8578811369509044, "recall": 0.9431818181818182, "f1-score": 0.8985115020297699, "support": 352.0}, "accuracy": 0.8905109489051095, "macro avg": {"precision": 0.8953835214955864, "recall": 0.8890083265083265, "f1-score": 0.8898262739942828, "support": 685.0}, "weighted avg": {"precision": 0.8943433093695294, "recall": 0.8905109489051095, "f1-score": 0.8900671781295737, "support": 685.0}}, "confusion_matrix": [[278, 55], [20, 332]], "roc_auc": 0.9551426426426427, "training_time": 0.493064, "train_size": 1271, "test_size": 685, "algorithm_type": "Random Forest"}, "RF_Large": {"accuracy": 0.8802919708029197, "precision": [0.8129675810473815, 0.9753521126760564], "recall": [0.978978978978979, 0.7869318181818182], "f1_score": [0.888283378746594, 0.8710691823899371], "support": [333, 352], "classification_report": {"0": {"precision": 0.8129675810473815, "recall": 0.978978978978979, "f1-score": 0.888283378746594, "support": 333.0}, "1": {"precision": 0.9753521126760564, "recall": 0.7869318181818182, "f1-score": 0.8710691823899371, "support": 352.0}, "accuracy": 0.8802919708029197, "macro avg": {"precision": 0.8941598468617189, "recall": 0.8829553985803986, "f1-score": 0.8796762805682656, "support": 685.0}, "weighted avg": {"precision": 0.8964118951105837, "recall": 0.8802919708029197, "f1-score": 0.8794375435385017, "support": 685.0}}, "confusion_matrix": [[326, 7], [75, 277]], "roc_auc": 0.9546947515697516, "training_time": 0.40096, "train_size": 1271, "test_size": 685, "algorithm_type": "Random Forest"}, "RF_Small": {"accuracy": 0.8759124087591241, "precision": [0.8212435233160622, 0.9464882943143813], "recall": [0.9519519519519519, 0.8039772727272727], "f1_score": [0.8817802503477051, 0.869431643625192], "support": [333, 352], "classification_report": {"0": {"precision": 0.8212435233160622, "recall": 0.9519519519519519, "f1-score": 0.8817802503477051, "support": 333.0}, "1": {"precision": 0.9464882943143813, "recall": 0.8039772727272727, "f1-score": 0.869431643625192, "support": 352.0}, "accuracy": 0.8759124087591241, "macro avg": {"precision": 0.8838659088152218, "recall": 0.8779646123396123, "f1-score": 0.8756059469864486, "support": 685.0}, "weighted avg": {"precision": 0.8856028800918407, "recall": 0.8759124087591241, "f1-score": 0.8754346889370123, "support": 685.0}}, "confusion_matrix": [[317, 16], [69, 283]], "roc_auc": 0.9472299003549004, "training_time": 0.112528, "train_size": 1271, "test_size": 685, "algorithm_type": "Random Forest"}, "ExtraTrees": {"accuracy": 0.8963503649635036, "precision": [0.9253246753246753, 0.8726790450928382], "recall": [0.8558558558558559, 0.9346590909090909], "f1_score": [0.8892355694227769, 0.9026063100137174], "support": [333, 352], "classification_report": {"0": {"precision": 0.9253246753246753, "recall": 0.8558558558558559, "f1-score": 0.8892355694227769, "support": 333.0}, "1": {"precision": 0.8726790450928382, "recall": 0.9346590909090909, "f1-score": 0.9026063100137174, "support": 352.0}, "accuracy": 0.8963503649635036, "macro avg": {"precision": 0.8990018602087568, "recall": 0.8952574733824734, "f1-score": 0.8959209397182472, "support": 685.0}, "weighted avg": {"precision": 0.898271738329629, "recall": 0.8963503649635036, "f1-score": 0.8961063733468806, "support": 685.0}}, "confusion_matrix": [[285, 48], [23, 329]], "roc_auc": 0.9615709459459458, "training_time": 0.676624, "train_size": 1271, "test_size": 685, "algorithm_type": "Random Forest"}, "ExtraTrees_Large": {"accuracy": 0.8686131386861314, "precision": [0.8075949367088607, 0.9517241379310345], "recall": [0.9579579579579579, 0.7840909090909091], "f1_score": [0.8763736263736264, 0.8598130841121495], "support": [333, 352], "classification_report": {"0": {"precision": 0.8075949367088607, "recall": 0.9579579579579579, "f1-score": 0.8763736263736264, "support": 333.0}, "1": {"precision": 0.9517241379310345, "recall": 0.7840909090909091, "f1-score": 0.8598130841121495, "support": 352.0}, "accuracy": 0.8686131386861314, "macro avg": {"precision": 0.8796595373199476, "recall": 0.8710244335244335, "f1-score": 0.868093355242888, "support": 685.0}, "weighted avg": {"precision": 0.8816584094536858, "recall": 0.8686131386861314, "f1-score": 0.8678636834888966, "support": 685.0}}, "confusion_matrix": [[319, 14], [76, 276]], "roc_auc": 0.9462146805896806, "training_time": 0.329771, "train_size": 1271, "test_size": 685, "algorithm_type": "Random Forest"}, "GradientBoosting": {"accuracy": 0.9007299270072993, "precision": [0.8406169665809768, 0.9797297297297297], "recall": [0.9819819819819819, 0.8238636363636364], "f1_score": [0.9058171745152355, 0.8950617283950617], "support": [333, 352], "classification_report": {"0": {"precision": 0.8406169665809768, "recall": 0.9819819819819819, "f1-score": 0.9058171745152355, "support": 333.0}, "1": {"precision": 0.9797297297297297, "recall": 0.8238636363636364, "f1-score": 0.8950617283950617, "support": 352.0}, "accuracy": 0.9007299270072993, "macro avg": {"precision": 0.9101733481553533, "recall": 0.9029228091728092, "f1-score": 0.9004394514551486, "support": 685.0}, "weighted avg": {"precision": 0.9121026492501171, "recall": 0.9007299270072993, "f1-score": 0.9002902883337739, "support": 685.0}}, "confusion_matrix": [[327, 6], [62, 290]], "roc_auc": 0.948603432978433, "training_time": 0.622562, "train_size": 1271, "test_size": 685, "algorithm_type": "Ensemble"}, "AdaBoost": {"accuracy": 0.8934306569343066, "precision": [0.8421052631578947, 0.9573770491803278], "recall": [0.960960960960961, 0.8295454545454546], "f1_score": [0.8976157082748948, 0.8888888888888888], "support": [333, 352], "classification_report": {"0": {"precision": 0.8421052631578947, "recall": 0.960960960960961, "f1-score": 0.8976157082748948, "support": 333.0}, "1": {"precision": 0.9573770491803278, "recall": 0.8295454545454546, "f1-score": 0.8888888888888888, "support": 352.0}, "accuracy": 0.8934306569343066, "macro avg": {"precision": 0.8997411561691113, "recall": 0.8952532077532078, "f1-score": 0.8932522985818918, "support": 685.0}, "weighted avg": {"precision": 0.9013398159752619, "recall": 0.8934306569343066, "f1-score": 0.8931312696998961, "support": 685.0}}, "confusion_matrix": [[320, 13], [60, 292]], "roc_auc": 0.9350600600600601, "training_time": 0.415062, "train_size": 1271, "test_size": 685, "algorithm_type": "Ensemble"}, "AdaBoost_DT": {"accuracy": 0.8671532846715329, "precision": [0.8980263157894737, 0.84251968503937], "recall": [0.8198198198198198, 0.9119318181818182], "f1_score": [0.8571428571428571, 0.8758526603001364], "support": [333, 352], "classification_report": {"0": {"precision": 0.8980263157894737, "recall": 0.8198198198198198, "f1-score": 0.8571428571428571, "support": 333.0}, "1": {"precision": 0.84251968503937, "recall": 0.9119318181818182, "f1-score": 0.8758526603001364, "support": 352.0}, "accuracy": 0.8671532846715329, "macro avg": {"precision": 0.8702730004144219, "recall": 0.865875819000819, "f1-score": 0.8664977587214968, "support": 685.0}, "weighted avg": {"precision": 0.8695032004259167, "recall": 0.8671532846715329, "f1-score": 0.8667572377433861, "support": 685.0}}, "confusion_matrix": [[273, 60], [31, 321]], "roc_auc": 0.9442055692055692, "training_time": 0.761879, "train_size": 1271, "test_size": 685, "algorithm_type": "Decision Tree"}}}