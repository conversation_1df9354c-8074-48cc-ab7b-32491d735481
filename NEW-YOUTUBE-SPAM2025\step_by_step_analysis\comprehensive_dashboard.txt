🎯 YOUTUBE SPAM CLASSIFICATION - COMPREHENSIVE DASHBOARD
======================================================================
📅 Generated: 2025-06-30 00:39:29
📊 Total Models: 204 | Algorithms: 34 | Splits: 6
======================================================================

📈 QUICK PERFORMANCE STATS:
------------------------------
🏆 Best Model: SVM_RBF (91.82%)
⚡ Fastest: NB_Complement (0.000s)
📊 Average Accuracy: 87.24%
🎯 Success Rate: 97.1% (204/210 models)

🏅 ALGORITHM TYPE LEADERBOARD:
-----------------------------------
🥇 Support Vector Machine: 89.22%
🥈 Ensemble: 88.63%
🥉 Logistic Regression: 88.60%
4️⃣ Naive Bayes: 86.81%
5️⃣ Random Forest: 86.59%
6️⃣ Decision Tree: 85.24%

🌟 TOP 10 INDIVIDUAL MODELS:
------------------------------
🥇 SVM_RBF (Orig): 91.82%
🥈 SVM_RBF_Tuned (Orig): 91.82%
🥉 SVM_Sigmoid (Orig): 91.21%
 4. SVM_RBF (Orig): 90.66%
 5. SVM_RBF (Orig): 90.63%
 6. LR_L2 (Orig): 90.59%
 7. LR_LBFGS (Orig): 90.59%
 8. LR_SAG (Orig): 90.59%
 9. LR_SAGA (Orig): 90.59%
10. SVM_Sigmoid (Orig): 90.46%

📊 SPLIT PERFORMANCE SUMMARY:
------------------------------
📈 Original Splits:
   Best: 91.82% | Avg: 88.49%
   Models: 102 | Time: 0.26s avg
📉 Reversed Splits:
   Best: 89.70% | Avg: 86.00%
   Models: 102 | Time: 0.11s avg

⚡ SPEED vs ACCURACY CHAMPIONS:
-----------------------------------
🎯 High Accuracy (>90%): 28 models
   Champion: SVM_RBF (91.82%)
⚡ Fast Training (<0.1s): 118 models
   Champion: LR_L2 (90.59%, 0.035s)
⚖️ Balanced (>85% acc, <1s): 163 models
   Champion: SVM_RBF (91.82%, 0.66s)

💡 KEY INSIGHTS:
---------------
• SVM kernels dominate top performance rankings
• Naive Bayes excels in speed-accuracy balance
• Original splits consistently outperform reversed
• Random Forest shows excellent consistency
• Ensemble methods provide competitive results
• Training time varies 1000x across algorithms

🎯 QUICK RECOMMENDATIONS:
-------------------------
🏆 Best Overall: SVM_RBF
⚡ Best Speed: NB_Multinomial (fast + accurate)
🎯 Best Balance: RF_Gini (robust + consistent)
🔬 Best Research: GradientBoosting (ensemble power)

======================================================================
📁 Detailed analysis available in step_by_step_analysis/
📊 Visualizations available in comprehensive_visualizations/
📈 Raw data available in comprehensive_analysis/
======================================================================