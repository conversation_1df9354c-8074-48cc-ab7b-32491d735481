{"SVM_Linear": {"accuracy": 0.8847989093387867, "precision": [0.8358024691358025, 0.9452054794520548], "recall": [0.9495091164095372, 0.8236074270557029], "f1_score": [0.8890347997373604, 0.8802267895109851], "support": [713, 754], "classification_report": {"0": {"precision": 0.8358024691358025, "recall": 0.9495091164095372, "f1-score": 0.8890347997373604, "support": 713.0}, "1": {"precision": 0.9452054794520548, "recall": 0.8236074270557029, "f1-score": 0.8802267895109851, "support": 754.0}, "accuracy": 0.8847989093387867, "macro avg": {"precision": 0.8905039742939287, "recall": 0.88655827173262, "f1-score": 0.8846307946241727, "support": 1467.0}, "weighted avg": {"precision": 0.8920327825498817, "recall": 0.8847989093387867, "f1-score": 0.8845077106366875, "support": 1467.0}}, "confusion_matrix": [[677, 36], [133, 621]], "roc_auc": 0.953125174385512, "training_time": 0.070128, "train_size": 489, "test_size": 1467, "algorithm_type": "Support Vector Machine"}, "SVM_Polynomial": {"accuracy": 0.8500340831629175, "precision": [0.8619676945668135, 0.8396946564885496], "recall": [0.82328190743338, 0.8753315649867374], "f1_score": [0.8421807747489239, 0.8571428571428571], "support": [713, 754], "classification_report": {"0": {"precision": 0.8619676945668135, "recall": 0.82328190743338, "f1-score": 0.8421807747489239, "support": 713.0}, "1": {"precision": 0.8396946564885496, "recall": 0.8753315649867374, "f1-score": 0.8571428571428571, "support": 754.0}, "accuracy": 0.8500340831629175, "macro avg": {"precision": 0.8508311755276816, "recall": 0.8493067362100587, "f1-score": 0.8496618159458905, "support": 1467.0}, "weighted avg": {"precision": 0.8505199299376309, "recall": 0.8500340831629175, "f1-score": 0.8498708975335358, "support": 1467.0}}, "confusion_matrix": [[587, 126], [94, 660]], "roc_auc": 0.9221915097042049, "training_time": 0.08412, "train_size": 489, "test_size": 1467, "algorithm_type": "Support Vector Machine"}, "SVM_RBF": {"accuracy": 0.880027266530334, "precision": [0.8584779706275033, 0.9025069637883009], "recall": [0.9018232819074333, 0.8594164456233422], "f1_score": [0.8796169630642955, 0.8804347826086957], "support": [713, 754], "classification_report": {"0": {"precision": 0.8584779706275033, "recall": 0.9018232819074333, "f1-score": 0.8796169630642955, "support": 713.0}, "1": {"precision": 0.9025069637883009, "recall": 0.8594164456233422, "f1-score": 0.8804347826086957, "support": 754.0}, "accuracy": 0.880027266530334, "macro avg": {"precision": 0.8804924672079021, "recall": 0.8806198637653877, "f1-score": 0.8800258728364956, "support": 1467.0}, "weighted avg": {"precision": 0.8811077326201696, "recall": 0.880027266530334, "f1-score": 0.8800373011259708, "support": 1467.0}}, "confusion_matrix": [[643, 70], [106, 648]], "roc_auc": 0.9276723300880577, "training_time": 0.072141, "train_size": 489, "test_size": 1467, "algorithm_type": "Support Vector Machine"}, "SVM_Sigmoid": {"accuracy": 0.874573960463531, "precision": [0.8190591073582629, 0.9467084639498433], "recall": [0.9523141654978962, 0.8010610079575596], "f1_score": [0.880674448767834, 0.867816091954023], "support": [713, 754], "classification_report": {"0": {"precision": 0.8190591073582629, "recall": 0.9523141654978962, "f1-score": 0.880674448767834, "support": 713.0}, "1": {"precision": 0.9467084639498433, "recall": 0.8010610079575596, "f1-score": 0.867816091954023, "support": 754.0}, "accuracy": 0.874573960463531, "macro avg": {"precision": 0.8828837856540531, "recall": 0.876687586727728, "f1-score": 0.8742452703609285, "support": 1467.0}, "weighted avg": {"precision": 0.8846675701190344, "recall": 0.874573960463531, "f1-score": 0.8740655864381723, "support": 1467.0}}, "confusion_matrix": [[679, 34], [150, 604]], "roc_auc": 0.9523588081889577, "training_time": 0.064856, "train_size": 489, "test_size": 1467, "algorithm_type": "Support Vector Machine"}, "SVM_RBF_Tuned": {"accuracy": 0.8895705521472392, "precision": [0.8748299319727891, 0.9043715846994536], "recall": [0.9018232819074333, 0.8779840848806366], "f1_score": [0.888121546961326, 0.8909825033647375], "support": [713, 754], "classification_report": {"0": {"precision": 0.8748299319727891, "recall": 0.9018232819074333, "f1-score": 0.888121546961326, "support": 713.0}, "1": {"precision": 0.9043715846994536, "recall": 0.8779840848806366, "f1-score": 0.8909825033647375, "support": 754.0}, "accuracy": 0.8895705521472392, "macro avg": {"precision": 0.8896007583361214, "recall": 0.889903683394035, "f1-score": 0.8895520251630318, "support": 1467.0}, "weighted avg": {"precision": 0.8900135762508429, "recall": 0.8895705521472392, "f1-score": 0.8895920044447425, "support": 1467.0}}, "confusion_matrix": [[643, 70], [92, 662]], "roc_auc": 0.9285484429001379, "training_time": 0.092383, "train_size": 489, "test_size": 1467, "algorithm_type": "Support Vector Machine"}, "SVM_Linear_Tuned": {"accuracy": 0.8847989093387867, "precision": [0.8358024691358025, 0.9452054794520548], "recall": [0.9495091164095372, 0.8236074270557029], "f1_score": [0.8890347997373604, 0.8802267895109851], "support": [713, 754], "classification_report": {"0": {"precision": 0.8358024691358025, "recall": 0.9495091164095372, "f1-score": 0.8890347997373604, "support": 713.0}, "1": {"precision": 0.9452054794520548, "recall": 0.8236074270557029, "f1-score": 0.8802267895109851, "support": 754.0}, "accuracy": 0.8847989093387867, "macro avg": {"precision": 0.8905039742939287, "recall": 0.88655827173262, "f1-score": 0.8846307946241727, "support": 1467.0}, "weighted avg": {"precision": 0.8920327825498817, "recall": 0.8847989093387867, "f1-score": 0.8845077106366875, "support": 1467.0}}, "confusion_matrix": [[677, 36], [133, 621]], "roc_auc": 0.953125174385512, "training_time": 0.077098, "train_size": 489, "test_size": 1467, "algorithm_type": "Support Vector Machine"}, "NB_Multinomial": {"accuracy": 0.8623040218132243, "precision": [0.8924731182795699, 0.8382352941176471], "recall": [0.814866760168303, 0.9071618037135278], "f1_score": [0.8519061583577713, 0.8713375796178344], "support": [713, 754], "classification_report": {"0": {"precision": 0.8924731182795699, "recall": 0.814866760168303, "f1-score": 0.8519061583577713, "support": 713.0}, "1": {"precision": 0.8382352941176471, "recall": 0.9071618037135278, "f1-score": 0.8713375796178344, "support": 754.0}, "accuracy": 0.8623040218132243, "macro avg": {"precision": 0.8653542061986085, "recall": 0.8610142819409154, "f1-score": 0.8616218689878028, "support": 1467.0}, "weighted avg": {"precision": 0.8645962815937556, "recall": 0.8623040218132243, "f1-score": 0.8618934055493783, "support": 1467.0}}, "confusion_matrix": [[581, 132], [70, 684]], "roc_auc": 0.9455554480824104, "training_time": 0.02135, "train_size": 489, "test_size": 1467, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli": {"accuracy": 0.8057259713701431, "precision": [0.7165991902834008, 0.9895615866388309], "recall": [0.9929873772791024, 0.6286472148541115], "f1_score": [0.8324514991181657, 0.7688564476885644], "support": [713, 754], "classification_report": {"0": {"precision": 0.7165991902834008, "recall": 0.9929873772791024, "f1-score": 0.8324514991181657, "support": 713.0}, "1": {"precision": 0.9895615866388309, "recall": 0.6286472148541115, "f1-score": 0.7688564476885644, "support": 754.0}, "accuracy": 0.8057259713701431, "macro avg": {"precision": 0.8530803884611158, "recall": 0.8108172960666069, "f1-score": 0.8006539734033651, "support": 1467.0}, "weighted avg": {"precision": 0.8568947914095045, "recall": 0.8057259713701431, "f1-score": 0.7997652899989296, "support": 1467.0}}, "confusion_matrix": [[708, 5], [280, 474]], "roc_auc": 0.9421635336178065, "training_time": 0.020103, "train_size": 489, "test_size": 1467, "algorithm_type": "<PERSON><PERSON>"}, "NB_Complement": {"accuracy": 0.8609406952965235, "precision": [0.8344283837056504, 0.8895184135977338], "recall": [0.8906030855539971, 0.8328912466843501], "f1_score": [0.8616010854816825, 0.8602739726027397], "support": [713, 754], "classification_report": {"0": {"precision": 0.8344283837056504, "recall": 0.8906030855539971, "f1-score": 0.8616010854816825, "support": 713.0}, "1": {"precision": 0.8895184135977338, "recall": 0.8328912466843501, "f1-score": 0.8602739726027397, "support": 754.0}, "accuracy": 0.8609406952965235, "macro avg": {"precision": 0.8619733986516921, "recall": 0.8617471661191736, "f1-score": 0.8609375290422111, "support": 1467.0}, "weighted avg": {"precision": 0.8627432320619087, "recall": 0.8609406952965235, "f1-score": 0.8609189838383813, "support": 1467.0}}, "confusion_matrix": [[635, 78], [126, 628]], "roc_auc": 0.9455554480824104, "training_time": 0.0, "train_size": 489, "test_size": 1467, "algorithm_type": "<PERSON><PERSON>"}, "NB_Multinomial_Tuned": {"accuracy": 0.869120654396728, "precision": [0.8859259259259259, 0.8547979797979798], "recall": [0.8387096774193549, 0.8978779840848806], "f1_score": [0.861671469740634, 0.8758085381630013], "support": [713, 754], "classification_report": {"0": {"precision": 0.8859259259259259, "recall": 0.8387096774193549, "f1-score": 0.861671469740634, "support": 713.0}, "1": {"precision": 0.8547979797979798, "recall": 0.8978779840848806, "f1-score": 0.8758085381630013, "support": 754.0}, "accuracy": 0.869120654396728, "macro avg": {"precision": 0.8703619528619528, "recall": 0.8682938307521177, "f1-score": 0.8687400039518176, "support": 1467.0}, "weighted avg": {"precision": 0.8699269679296946, "recall": 0.869120654396728, "f1-score": 0.8689375567143661, "support": 1467.0}}, "confusion_matrix": [[598, 115], [77, 677]], "roc_auc": 0.9438478651493112, "training_time": 0.007986, "train_size": 489, "test_size": 1467, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli_Tuned": {"accuracy": 0.8377641445126107, "precision": [0.7624309392265194, 0.9590747330960854], "recall": [0.967741935483871, 0.7148541114058355], "f1_score": [0.8529048207663782, 0.8191489361702128], "support": [713, 754], "classification_report": {"0": {"precision": 0.7624309392265194, "recall": 0.967741935483871, "f1-score": 0.8529048207663782, "support": 713.0}, "1": {"precision": 0.9590747330960854, "recall": 0.7148541114058355, "f1-score": 0.8191489361702128, "support": 754.0}, "accuracy": 0.8377641445126107, "macro avg": {"precision": 0.8607528361613024, "recall": 0.8412980234448533, "f1-score": 0.8360268784682955, "support": 1467.0}, "weighted avg": {"precision": 0.8635007555712044, "recall": 0.8377641445126107, "f1-score": 0.8355551704695079, "support": 1467.0}}, "confusion_matrix": [[690, 23], [215, 539]], "roc_auc": 0.9361116588107932, "training_time": 0.004937, "train_size": 489, "test_size": 1467, "algorithm_type": "<PERSON><PERSON>"}, "LR_L1": {"accuracy": 0.8425357873210634, "precision": [0.7660044150110376, 0.966131907308378], "recall": [0.9733520336605891, 0.7188328912466844], "f1_score": [0.8573193329215565, 0.8243346007604563], "support": [713, 754], "classification_report": {"0": {"precision": 0.7660044150110376, "recall": 0.9733520336605891, "f1-score": 0.8573193329215565, "support": 713.0}, "1": {"precision": 0.966131907308378, "recall": 0.7188328912466844, "f1-score": 0.8243346007604563, "support": 754.0}, "accuracy": 0.8425357873210634, "macro avg": {"precision": 0.8660681611597078, "recall": 0.8460924624536368, "f1-score": 0.8408269668410064, "support": 1467.0}, "weighted avg": {"precision": 0.868864762108648, "recall": 0.8425357873210634, "f1-score": 0.8403660350009909, "support": 1467.0}}, "confusion_matrix": [[694, 19], [212, 542]], "roc_auc": 0.9007034944066428, "training_time": 0.008984, "train_size": 489, "test_size": 1467, "algorithm_type": "Logistic Regression"}, "LR_L2": {"accuracy": 0.8779822767552828, "precision": [0.8396946564885496, 0.922173274596182], "recall": [0.9256661991584852, 0.8328912466843501], "f1_score": [0.8805870580386924, 0.8752613240418119], "support": [713, 754], "classification_report": {"0": {"precision": 0.8396946564885496, "recall": 0.9256661991584852, "f1-score": 0.8805870580386924, "support": 713.0}, "1": {"precision": 0.922173274596182, "recall": 0.8328912466843501, "f1-score": 0.8752613240418119, "support": 754.0}, "accuracy": 0.8779822767552828, "macro avg": {"precision": 0.8809339655423658, "recall": 0.8792787229214176, "f1-score": 0.8779241910402522, "support": 1467.0}, "weighted avg": {"precision": 0.8820865297354173, "recall": 0.8779822767552828, "f1-score": 0.877849768717869, "support": 1467.0}}, "confusion_matrix": [[660, 53], [126, 628]], "roc_auc": 0.946468763137042, "training_time": 0.010949, "train_size": 489, "test_size": 1467, "algorithm_type": "Logistic Regression"}, "LR_ElasticNet": {"accuracy": 0.8725289706884799, "precision": [0.8145933014354066, 0.9492868462757528], "recall": [0.9551192145862553, 0.7944297082228117], "f1_score": [0.8792769528728211, 0.8649819494584837], "support": [713, 754], "classification_report": {"0": {"precision": 0.8145933014354066, "recall": 0.9551192145862553, "f1-score": 0.8792769528728211, "support": 713.0}, "1": {"precision": 0.9492868462757528, "recall": 0.7944297082228117, "f1-score": 0.8649819494584837, "support": 754.0}, "accuracy": 0.8725289706884799, "macro avg": {"precision": 0.8819400738555797, "recall": 0.8747744614045334, "f1-score": 0.8721294511656524, "support": 1467.0}, "weighted avg": {"precision": 0.8838222944889997, "recall": 0.8725289706884799, "f1-score": 0.8719296914042387, "support": 1467.0}}, "confusion_matrix": [[681, 32], [155, 599]], "roc_auc": 0.9273728520355208, "training_time": 0.009992, "train_size": 489, "test_size": 1467, "algorithm_type": "Logistic Regression"}, "LR_LBFGS": {"accuracy": 0.8779822767552828, "precision": [0.8396946564885496, 0.922173274596182], "recall": [0.9256661991584852, 0.8328912466843501], "f1_score": [0.8805870580386924, 0.8752613240418119], "support": [713, 754], "classification_report": {"0": {"precision": 0.8396946564885496, "recall": 0.9256661991584852, "f1-score": 0.8805870580386924, "support": 713.0}, "1": {"precision": 0.922173274596182, "recall": 0.8328912466843501, "f1-score": 0.8752613240418119, "support": 754.0}, "accuracy": 0.8779822767552828, "macro avg": {"precision": 0.8809339655423658, "recall": 0.8792787229214176, "f1-score": 0.8779241910402522, "support": 1467.0}, "weighted avg": {"precision": 0.8820865297354173, "recall": 0.8779822767552828, "f1-score": 0.877849768717869, "support": 1467.0}}, "confusion_matrix": [[660, 53], [126, 628]], "roc_auc": 0.9465599086312924, "training_time": 0.017698, "train_size": 489, "test_size": 1467, "algorithm_type": "Logistic Regression"}, "LR_SAG": {"accuracy": 0.8773006134969326, "precision": [0.8386277001270648, 0.9220588235294118], "recall": [0.9256661991584852, 0.8315649867374005], "f1_score": [0.88, 0.8744769874476988], "support": [713, 754], "classification_report": {"0": {"precision": 0.8386277001270648, "recall": 0.9256661991584852, "f1-score": 0.88, "support": 713.0}, "1": {"precision": 0.9220588235294118, "recall": 0.8315649867374005, "f1-score": 0.8744769874476988, "support": 754.0}, "accuracy": 0.8773006134969326, "macro avg": {"precision": 0.8803432618282383, "recall": 0.8786155929479429, "f1-score": 0.8772384937238493, "support": 1467.0}, "weighted avg": {"precision": 0.8815091364224771, "recall": 0.8773006134969326, "f1-score": 0.877161314611837, "support": 1467.0}}, "confusion_matrix": [[660, 53], [127, 627]], "roc_auc": 0.9465096856038483, "training_time": 0.0, "train_size": 489, "test_size": 1467, "algorithm_type": "Logistic Regression"}, "LR_SAGA": {"accuracy": 0.8779822767552828, "precision": [0.8396946564885496, 0.922173274596182], "recall": [0.9256661991584852, 0.8328912466843501], "f1_score": [0.8805870580386924, 0.8752613240418119], "support": [713, 754], "classification_report": {"0": {"precision": 0.8396946564885496, "recall": 0.9256661991584852, "f1-score": 0.8805870580386924, "support": 713.0}, "1": {"precision": 0.922173274596182, "recall": 0.8328912466843501, "f1-score": 0.8752613240418119, "support": 754.0}, "accuracy": 0.8779822767552828, "macro avg": {"precision": 0.8809339655423658, "recall": 0.8792787229214176, "f1-score": 0.8779241910402522, "support": 1467.0}, "weighted avg": {"precision": 0.8820865297354173, "recall": 0.8779822767552828, "f1-score": 0.877849768717869, "support": 1467.0}}, "confusion_matrix": [[660, 53], [126, 628]], "roc_auc": 0.9462939125970514, "training_time": 0.011267, "train_size": 489, "test_size": 1467, "algorithm_type": "Logistic Regression"}, "DT_Gini": {"accuracy": 0.8214042263122018, "precision": [0.8921739130434783, 0.7757847533632287], "recall": [0.7194950911640954, 0.9177718832891246], "f1_score": [0.796583850931677, 0.8408262454434994], "support": [713, 754], "classification_report": {"0": {"precision": 0.8921739130434783, "recall": 0.7194950911640954, "f1-score": 0.796583850931677, "support": 713.0}, "1": {"precision": 0.7757847533632287, "recall": 0.9177718832891246, "f1-score": 0.8408262454434994, "support": 754.0}, "accuracy": 0.8214042263122018, "macro avg": {"precision": 0.8339793332033535, "recall": 0.81863348722661, "f1-score": 0.8187050481875882, "support": 1467.0}, "weighted avg": {"precision": 0.8323528998199552, "recall": 0.8214042263122018, "f1-score": 0.8193232956909915, "support": 1467.0}}, "confusion_matrix": [[513, 200], [62, 692]], "roc_auc": 0.9141846570511272, "training_time": 0.022977, "train_size": 489, "test_size": 1467, "algorithm_type": "Decision Tree"}, "DT_Entropy": {"accuracy": 0.8173142467620995, "precision": [0.8856152512998267, 0.7730337078651686], "recall": [0.7166900420757363, 0.9124668435013262], "f1_score": [0.7922480620155039, 0.8369829683698297], "support": [713, 754], "classification_report": {"0": {"precision": 0.8856152512998267, "recall": 0.7166900420757363, "f1-score": 0.7922480620155039, "support": 713.0}, "1": {"precision": 0.7730337078651686, "recall": 0.9124668435013262, "f1-score": 0.8369829683698297, "support": 754.0}, "accuracy": 0.8173142467620995, "macro avg": {"precision": 0.8293244795824977, "recall": 0.8145784427885312, "f1-score": 0.8146155151926668, "support": 1467.0}, "weighted avg": {"precision": 0.8277512541970781, "recall": 0.8173142467620995, "f1-score": 0.8152406451042303, "support": 1467.0}}, "confusion_matrix": [[511, 202], [66, 688]], "roc_auc": 0.9051761340173586, "training_time": 0.02528, "train_size": 489, "test_size": 1467, "algorithm_type": "Decision Tree"}, "DT_Log_Loss": {"accuracy": 0.8173142467620995, "precision": [0.8856152512998267, 0.7730337078651686], "recall": [0.7166900420757363, 0.9124668435013262], "f1_score": [0.7922480620155039, 0.8369829683698297], "support": [713, 754], "classification_report": {"0": {"precision": 0.8856152512998267, "recall": 0.7166900420757363, "f1-score": 0.7922480620155039, "support": 713.0}, "1": {"precision": 0.7730337078651686, "recall": 0.9124668435013262, "f1-score": 0.8369829683698297, "support": 754.0}, "accuracy": 0.8173142467620995, "macro avg": {"precision": 0.8293244795824977, "recall": 0.8145784427885312, "f1-score": 0.8146155151926668, "support": 1467.0}, "weighted avg": {"precision": 0.8277512541970781, "recall": 0.8173142467620995, "f1-score": 0.8152406451042303, "support": 1467.0}}, "confusion_matrix": [[511, 202], [66, 688]], "roc_auc": 0.9051761340173586, "training_time": 0.027984, "train_size": 489, "test_size": 1467, "algorithm_type": "Decision Tree"}, "DT_Gini_Pruned": {"accuracy": 0.858214042263122, "precision": [0.7840269966254219, 0.972318339100346], "recall": [0.9775596072931276, 0.7453580901856764], "f1_score": [0.8701622971285893, 0.8438438438438438], "support": [713, 754], "classification_report": {"0": {"precision": 0.7840269966254219, "recall": 0.9775596072931276, "f1-score": 0.8701622971285893, "support": 713.0}, "1": {"precision": 0.972318339100346, "recall": 0.7453580901856764, "f1-score": 0.8438438438438438, "support": 754.0}, "accuracy": 0.858214042263122, "macro avg": {"precision": 0.878172667862884, "recall": 0.861458848739402, "f1-score": 0.8570030704862166, "support": 1467.0}, "weighted avg": {"precision": 0.8808038693085117, "recall": 0.858214042263122, "f1-score": 0.8566352938724896, "support": 1467.0}}, "confusion_matrix": [[697, 16], [192, 562]], "roc_auc": 0.8579032444075728, "training_time": 0.018774, "train_size": 489, "test_size": 1467, "algorithm_type": "Decision Tree"}, "DT_Entropy_Pruned": {"accuracy": 0.8289025221540559, "precision": [0.746268656716418, 0.9754253308128544], "recall": [0.9817671809256662, 0.6843501326259946], "f1_score": [0.8479709267110842, 0.8043647700701481], "support": [713, 754], "classification_report": {"0": {"precision": 0.746268656716418, "recall": 0.9817671809256662, "f1-score": 0.8479709267110842, "support": 713.0}, "1": {"precision": 0.9754253308128544, "recall": 0.6843501326259946, "f1-score": 0.8043647700701481, "support": 754.0}, "accuracy": 0.8289025221540559, "macro avg": {"precision": 0.8608469937646361, "recall": 0.8330586567758305, "f1-score": 0.8261678483906161, "support": 1467.0}, "weighted avg": {"precision": 0.8640492513099511, "recall": 0.8289025221540559, "f1-score": 0.8255584917368062, "support": 1467.0}}, "confusion_matrix": [[700, 13], [238, 516]], "roc_auc": 0.8560012797571438, "training_time": 0.018963, "train_size": 489, "test_size": 1467, "algorithm_type": "Decision Tree"}, "DT_Best_First": {"accuracy": 0.8214042263122018, "precision": [0.8921739130434783, 0.7757847533632287], "recall": [0.7194950911640954, 0.9177718832891246], "f1_score": [0.796583850931677, 0.8408262454434994], "support": [713, 754], "classification_report": {"0": {"precision": 0.8921739130434783, "recall": 0.7194950911640954, "f1-score": 0.796583850931677, "support": 713.0}, "1": {"precision": 0.7757847533632287, "recall": 0.9177718832891246, "f1-score": 0.8408262454434994, "support": 754.0}, "accuracy": 0.8214042263122018, "macro avg": {"precision": 0.8339793332033535, "recall": 0.81863348722661, "f1-score": 0.8187050481875882, "support": 1467.0}, "weighted avg": {"precision": 0.8323528998199552, "recall": 0.8214042263122018, "f1-score": 0.8193232956909915, "support": 1467.0}}, "confusion_matrix": [[513, 200], [62, 692]], "roc_auc": 0.9141846570511272, "training_time": 0.0235, "train_size": 489, "test_size": 1467, "algorithm_type": "Decision Tree"}, "DT_Random_Split": {"accuracy": 0.8159509202453987, "precision": [0.8773424190800682, 0.775], "recall": [0.7223001402524544, 0.9045092838196287], "f1_score": [0.7923076923076923, 0.8347613219094248], "support": [713, 754], "classification_report": {"0": {"precision": 0.8773424190800682, "recall": 0.7223001402524544, "f1-score": 0.7923076923076923, "support": 713.0}, "1": {"precision": 0.775, "recall": 0.9045092838196287, "f1-score": 0.8347613219094248, "support": 754.0}, "accuracy": 0.8159509202453987, "macro avg": {"precision": 0.8261712095400341, "recall": 0.8134047120360415, "f1-score": 0.8135345071085585, "support": 1467.0}, "weighted avg": {"precision": 0.8247410666694538, "recall": 0.8159509202453987, "f1-score": 0.8141277582379624, "support": 1467.0}}, "confusion_matrix": [[515, 198], [72, 682]], "roc_auc": 0.9029663208098184, "training_time": 0.017964, "train_size": 489, "test_size": 1467, "algorithm_type": "Decision Tree"}, "RF_Gini": {"accuracy": 0.8084526244035446, "precision": [0.8985239852398524, 0.7556756756756757], "recall": [0.6830294530154277, 0.9270557029177718], "f1_score": [0.7760956175298804, 0.8326384752829065], "support": [713, 754], "classification_report": {"0": {"precision": 0.8985239852398524, "recall": 0.6830294530154277, "f1-score": 0.7760956175298804, "support": 713.0}, "1": {"precision": 0.7556756756756757, "recall": 0.9270557029177718, "f1-score": 0.8326384752829065, "support": 754.0}, "accuracy": 0.8084526244035446, "macro avg": {"precision": 0.8270998304577641, "recall": 0.8050425779665997, "f1-score": 0.8043670464063934, "support": 1467.0}, "weighted avg": {"precision": 0.8251036543527432, "recall": 0.8084526244035446, "f1-score": 0.805157181773767, "support": 1467.0}}, "confusion_matrix": [[487, 226], [55, 699]], "roc_auc": 0.9001249995349719, "training_time": 0.314842, "train_size": 489, "test_size": 1467, "algorithm_type": "Random Forest"}, "RF_Entropy": {"accuracy": 0.8084526244035446, "precision": [0.9060150375939849, 0.7529411764705882], "recall": [0.6760168302945302, 0.9336870026525199], "f1_score": [0.7742971887550201, 0.8336293664890467], "support": [713, 754], "classification_report": {"0": {"precision": 0.9060150375939849, "recall": 0.6760168302945302, "f1-score": 0.7742971887550201, "support": 713.0}, "1": {"precision": 0.7529411764705882, "recall": 0.9336870026525199, "f1-score": 0.8336293664890467, "support": 754.0}, "accuracy": 0.8084526244035446, "macro avg": {"precision": 0.8294781070322865, "recall": 0.804851916473525, "f1-score": 0.8039632776220333, "support": 1467.0}, "weighted avg": {"precision": 0.8273390380799828, "recall": 0.8084526244035446, "f1-score": 0.804792391216817, "support": 1467.0}}, "confusion_matrix": [[482, 231], [50, 704]], "roc_auc": 0.9033932165430933, "training_time": 0.237189, "train_size": 489, "test_size": 1467, "algorithm_type": "Random Forest"}, "RF_Log_Loss": {"accuracy": 0.8084526244035446, "precision": [0.9060150375939849, 0.7529411764705882], "recall": [0.6760168302945302, 0.9336870026525199], "f1_score": [0.7742971887550201, 0.8336293664890467], "support": [713, 754], "classification_report": {"0": {"precision": 0.9060150375939849, "recall": 0.6760168302945302, "f1-score": 0.7742971887550201, "support": 713.0}, "1": {"precision": 0.7529411764705882, "recall": 0.9336870026525199, "f1-score": 0.8336293664890467, "support": 754.0}, "accuracy": 0.8084526244035446, "macro avg": {"precision": 0.8294781070322865, "recall": 0.804851916473525, "f1-score": 0.8039632776220333, "support": 1467.0}, "weighted avg": {"precision": 0.8273390380799828, "recall": 0.8084526244035446, "f1-score": 0.804792391216817, "support": 1467.0}}, "confusion_matrix": [[482, 231], [50, 704]], "roc_auc": 0.9033932165430933, "training_time": 0.243431, "train_size": 489, "test_size": 1467, "algorithm_type": "Random Forest"}, "RF_Large": {"accuracy": 0.8616223585548739, "precision": [0.791095890410959, 0.9661590524534687], "recall": [0.9719495091164095, 0.7572944297082228], "f1_score": [0.8722466960352423, 0.8490706319702602], "support": [713, 754], "classification_report": {"0": {"precision": 0.791095890410959, "recall": 0.9719495091164095, "f1-score": 0.8722466960352423, "support": 713.0}, "1": {"precision": 0.9661590524534687, "recall": 0.7572944297082228, "f1-score": 0.8490706319702602, "support": 754.0}, "accuracy": 0.8616223585548739, "macro avg": {"precision": 0.8786274714322138, "recall": 0.8646219694123162, "f1-score": 0.8606586640027513, "support": 1467.0}, "weighted avg": {"precision": 0.8810738210040416, "recall": 0.8616223585548739, "f1-score": 0.8603347994401528, "support": 1467.0}}, "confusion_matrix": [[693, 20], [183, 571]], "roc_auc": 0.9428331739837277, "training_time": 0.293418, "train_size": 489, "test_size": 1467, "algorithm_type": "Random Forest"}, "RF_Small": {"accuracy": 0.8568507157464212, "precision": [0.7848244620611552, 0.9657534246575342], "recall": [0.9719495091164095, 0.7480106100795756], "f1_score": [0.868421052631579, 0.8430493273542601], "support": [713, 754], "classification_report": {"0": {"precision": 0.7848244620611552, "recall": 0.9719495091164095, "f1-score": 0.868421052631579, "support": 713.0}, "1": {"precision": 0.9657534246575342, "recall": 0.7480106100795756, "f1-score": 0.8430493273542601, "support": 754.0}, "accuracy": 0.8568507157464212, "macro avg": {"precision": 0.8752889433593447, "recall": 0.8599800595979925, "f1-score": 0.8557351899929195, "support": 1467.0}, "weighted avg": {"precision": 0.8778172621958994, "recall": 0.8568507157464212, "f1-score": 0.8553806430480081, "support": 1467.0}}, "confusion_matrix": [[693, 20], [190, 564]], "roc_auc": 0.9302495154407907, "training_time": 0.071284, "train_size": 489, "test_size": 1467, "algorithm_type": "Random Forest"}, "ExtraTrees": {"accuracy": 0.8486707566462167, "precision": [0.9139966273187183, 0.8043478260869565], "recall": [0.7601683029453016, 0.9323607427055703], "f1_score": [0.8300153139356815, 0.8636363636363636], "support": [713, 754], "classification_report": {"0": {"precision": 0.9139966273187183, "recall": 0.7601683029453016, "f1-score": 0.8300153139356815, "support": 713.0}, "1": {"precision": 0.8043478260869565, "recall": 0.9323607427055703, "f1-score": 0.8636363636363636, "support": 754.0}, "accuracy": 0.8486707566462167, "macro avg": {"precision": 0.8591722267028374, "recall": 0.846264522825436, "f1-score": 0.8468258387860226, "support": 1467.0}, "weighted avg": {"precision": 0.8576399837408394, "recall": 0.8486707566462167, "f1-score": 0.8472956625889292, "support": 1467.0}}, "confusion_matrix": [[542, 171], [51, 703]], "roc_auc": 0.9211172949505397, "training_time": 0.255983, "train_size": 489, "test_size": 1467, "algorithm_type": "Random Forest"}, "ExtraTrees_Large": {"accuracy": 0.8629856850715747, "precision": [0.7976744186046512, 0.9555189456342669], "recall": [0.9621318373071529, 0.7692307692307693], "f1_score": [0.8722186904005086, 0.852314474650992], "support": [713, 754], "classification_report": {"0": {"precision": 0.7976744186046512, "recall": 0.9621318373071529, "f1-score": 0.8722186904005086, "support": 713.0}, "1": {"precision": 0.9555189456342669, "recall": 0.7692307692307693, "f1-score": 0.852314474650992, "support": 754.0}, "accuracy": 0.8629856850715747, "macro avg": {"precision": 0.8765966821194591, "recall": 0.8656813032689611, "f1-score": 0.8622665825257503, "support": 1467.0}, "weighted avg": {"precision": 0.8788024168189186, "recall": 0.8629856850715747, "f1-score": 0.8619884390882145, "support": 1467.0}}, "confusion_matrix": [[686, 27], [174, 580]], "roc_auc": 0.9451601742553041, "training_time": 0.233306, "train_size": 489, "test_size": 1467, "algorithm_type": "Random Forest"}, "GradientBoosting": {"accuracy": 0.8813905930470347, "precision": [0.8122827346465817, 0.9801324503311258], "recall": [0.9831697054698457, 0.7851458885941645], "f1_score": [0.8895939086294417, 0.8718703976435935], "support": [713, 754], "classification_report": {"0": {"precision": 0.8122827346465817, "recall": 0.9831697054698457, "f1-score": 0.8895939086294417, "support": 713.0}, "1": {"precision": 0.9801324503311258, "recall": 0.7851458885941645, "f1-score": 0.8718703976435935, "support": 754.0}, "accuracy": 0.8813905930470347, "macro avg": {"precision": 0.8962075924888537, "recall": 0.8841577970320051, "f1-score": 0.8807321531365175, "support": 1467.0}, "weighted avg": {"precision": 0.8985531406630415, "recall": 0.8813905930470347, "f1-score": 0.8804844830784332, "support": 1467.0}}, "confusion_matrix": [[701, 12], [162, 592]], "roc_auc": 0.9325049013954559, "training_time": 0.295825, "train_size": 489, "test_size": 1467, "algorithm_type": "Ensemble"}, "AdaBoost": {"accuracy": 0.8575323790047716, "precision": [0.7916666666666666, 0.9519071310116086], "recall": [0.9593267882187938, 0.7612732095490716], "f1_score": [0.8674698795180723, 0.8459837877671333], "support": [713, 754], "classification_report": {"0": {"precision": 0.7916666666666666, "recall": 0.9593267882187938, "f1-score": 0.8674698795180723, "support": 713.0}, "1": {"precision": 0.9519071310116086, "recall": 0.7612732095490716, "f1-score": 0.8459837877671333, "support": 754.0}, "accuracy": 0.8575323790047716, "macro avg": {"precision": 0.8717868988391376, "recall": 0.8602999988839327, "f1-score": 0.8567268336426028, "support": 1467.0}, "weighted avg": {"precision": 0.8740261145985591, "recall": 0.8575323790047716, "f1-score": 0.8564265849167035, "support": 1467.0}}, "confusion_matrix": [[684, 29], [180, 574]], "roc_auc": 0.9162484514566539, "training_time": 0.278965, "train_size": 489, "test_size": 1467, "algorithm_type": "Ensemble"}, "AdaBoost_DT": {"accuracy": 0.8548057259713702, "precision": [0.8918495297805643, 0.8262967430639324], "recall": [0.7980364656381487, 0.9084880636604774], "f1_score": [0.8423390081421169, 0.8654453569172458], "support": [713, 754], "classification_report": {"0": {"precision": 0.8918495297805643, "recall": 0.7980364656381487, "f1-score": 0.8423390081421169, "support": 713.0}, "1": {"precision": 0.8262967430639324, "recall": 0.9084880636604774, "f1-score": 0.8654453569172458, "support": 754.0}, "accuracy": 0.8548057259713702, "macro avg": {"precision": 0.8590731364222484, "recall": 0.8532622646493131, "f1-score": 0.8538921825296814, "support": 1467.0}, "weighted avg": {"precision": 0.8581570954354106, "recall": 0.8548057259713702, "f1-score": 0.8542150728840714, "support": 1467.0}}, "confusion_matrix": [[569, 144], [69, 685]], "roc_auc": 0.9409749219682962, "training_time": 0.47017, "train_size": 489, "test_size": 1467, "algorithm_type": "Decision Tree"}}