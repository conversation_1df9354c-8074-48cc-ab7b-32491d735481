{"train_25_test_75_REVERSED": {"SVM_Linear": {"accuracy": 0.8847989093387867, "precision": [0.8358024691358025, 0.9452054794520548], "recall": [0.9495091164095372, 0.8236074270557029], "f1_score": [0.8890347997373604, 0.8802267895109851], "support": [713, 754], "classification_report": {"0": {"precision": 0.8358024691358025, "recall": 0.9495091164095372, "f1-score": 0.8890347997373604, "support": 713.0}, "1": {"precision": 0.9452054794520548, "recall": 0.8236074270557029, "f1-score": 0.8802267895109851, "support": 754.0}, "accuracy": 0.8847989093387867, "macro avg": {"precision": 0.8905039742939287, "recall": 0.88655827173262, "f1-score": 0.8846307946241727, "support": 1467.0}, "weighted avg": {"precision": 0.8920327825498817, "recall": 0.8847989093387867, "f1-score": 0.8845077106366875, "support": 1467.0}}, "confusion_matrix": [[677, 36], [133, 621]], "roc_auc": 0.953125174385512, "training_time": 0.070128, "train_size": 489, "test_size": 1467, "algorithm_type": "Support Vector Machine"}, "SVM_Polynomial": {"accuracy": 0.8500340831629175, "precision": [0.8619676945668135, 0.8396946564885496], "recall": [0.82328190743338, 0.8753315649867374], "f1_score": [0.8421807747489239, 0.8571428571428571], "support": [713, 754], "classification_report": {"0": {"precision": 0.8619676945668135, "recall": 0.82328190743338, "f1-score": 0.8421807747489239, "support": 713.0}, "1": {"precision": 0.8396946564885496, "recall": 0.8753315649867374, "f1-score": 0.8571428571428571, "support": 754.0}, "accuracy": 0.8500340831629175, "macro avg": {"precision": 0.8508311755276816, "recall": 0.8493067362100587, "f1-score": 0.8496618159458905, "support": 1467.0}, "weighted avg": {"precision": 0.8505199299376309, "recall": 0.8500340831629175, "f1-score": 0.8498708975335358, "support": 1467.0}}, "confusion_matrix": [[587, 126], [94, 660]], "roc_auc": 0.9221915097042049, "training_time": 0.08412, "train_size": 489, "test_size": 1467, "algorithm_type": "Support Vector Machine"}, "SVM_RBF": {"accuracy": 0.880027266530334, "precision": [0.8584779706275033, 0.9025069637883009], "recall": [0.9018232819074333, 0.8594164456233422], "f1_score": [0.8796169630642955, 0.8804347826086957], "support": [713, 754], "classification_report": {"0": {"precision": 0.8584779706275033, "recall": 0.9018232819074333, "f1-score": 0.8796169630642955, "support": 713.0}, "1": {"precision": 0.9025069637883009, "recall": 0.8594164456233422, "f1-score": 0.8804347826086957, "support": 754.0}, "accuracy": 0.880027266530334, "macro avg": {"precision": 0.8804924672079021, "recall": 0.8806198637653877, "f1-score": 0.8800258728364956, "support": 1467.0}, "weighted avg": {"precision": 0.8811077326201696, "recall": 0.880027266530334, "f1-score": 0.8800373011259708, "support": 1467.0}}, "confusion_matrix": [[643, 70], [106, 648]], "roc_auc": 0.9276723300880577, "training_time": 0.072141, "train_size": 489, "test_size": 1467, "algorithm_type": "Support Vector Machine"}, "SVM_Sigmoid": {"accuracy": 0.874573960463531, "precision": [0.8190591073582629, 0.9467084639498433], "recall": [0.9523141654978962, 0.8010610079575596], "f1_score": [0.880674448767834, 0.867816091954023], "support": [713, 754], "classification_report": {"0": {"precision": 0.8190591073582629, "recall": 0.9523141654978962, "f1-score": 0.880674448767834, "support": 713.0}, "1": {"precision": 0.9467084639498433, "recall": 0.8010610079575596, "f1-score": 0.867816091954023, "support": 754.0}, "accuracy": 0.874573960463531, "macro avg": {"precision": 0.8828837856540531, "recall": 0.876687586727728, "f1-score": 0.8742452703609285, "support": 1467.0}, "weighted avg": {"precision": 0.8846675701190344, "recall": 0.874573960463531, "f1-score": 0.8740655864381723, "support": 1467.0}}, "confusion_matrix": [[679, 34], [150, 604]], "roc_auc": 0.9523588081889577, "training_time": 0.064856, "train_size": 489, "test_size": 1467, "algorithm_type": "Support Vector Machine"}, "SVM_RBF_Tuned": {"accuracy": 0.8895705521472392, "precision": [0.8748299319727891, 0.9043715846994536], "recall": [0.9018232819074333, 0.8779840848806366], "f1_score": [0.888121546961326, 0.8909825033647375], "support": [713, 754], "classification_report": {"0": {"precision": 0.8748299319727891, "recall": 0.9018232819074333, "f1-score": 0.888121546961326, "support": 713.0}, "1": {"precision": 0.9043715846994536, "recall": 0.8779840848806366, "f1-score": 0.8909825033647375, "support": 754.0}, "accuracy": 0.8895705521472392, "macro avg": {"precision": 0.8896007583361214, "recall": 0.889903683394035, "f1-score": 0.8895520251630318, "support": 1467.0}, "weighted avg": {"precision": 0.8900135762508429, "recall": 0.8895705521472392, "f1-score": 0.8895920044447425, "support": 1467.0}}, "confusion_matrix": [[643, 70], [92, 662]], "roc_auc": 0.9285484429001379, "training_time": 0.092383, "train_size": 489, "test_size": 1467, "algorithm_type": "Support Vector Machine"}, "SVM_Linear_Tuned": {"accuracy": 0.8847989093387867, "precision": [0.8358024691358025, 0.9452054794520548], "recall": [0.9495091164095372, 0.8236074270557029], "f1_score": [0.8890347997373604, 0.8802267895109851], "support": [713, 754], "classification_report": {"0": {"precision": 0.8358024691358025, "recall": 0.9495091164095372, "f1-score": 0.8890347997373604, "support": 713.0}, "1": {"precision": 0.9452054794520548, "recall": 0.8236074270557029, "f1-score": 0.8802267895109851, "support": 754.0}, "accuracy": 0.8847989093387867, "macro avg": {"precision": 0.8905039742939287, "recall": 0.88655827173262, "f1-score": 0.8846307946241727, "support": 1467.0}, "weighted avg": {"precision": 0.8920327825498817, "recall": 0.8847989093387867, "f1-score": 0.8845077106366875, "support": 1467.0}}, "confusion_matrix": [[677, 36], [133, 621]], "roc_auc": 0.953125174385512, "training_time": 0.077098, "train_size": 489, "test_size": 1467, "algorithm_type": "Support Vector Machine"}, "NB_Multinomial": {"accuracy": 0.8623040218132243, "precision": [0.8924731182795699, 0.8382352941176471], "recall": [0.814866760168303, 0.9071618037135278], "f1_score": [0.8519061583577713, 0.8713375796178344], "support": [713, 754], "classification_report": {"0": {"precision": 0.8924731182795699, "recall": 0.814866760168303, "f1-score": 0.8519061583577713, "support": 713.0}, "1": {"precision": 0.8382352941176471, "recall": 0.9071618037135278, "f1-score": 0.8713375796178344, "support": 754.0}, "accuracy": 0.8623040218132243, "macro avg": {"precision": 0.8653542061986085, "recall": 0.8610142819409154, "f1-score": 0.8616218689878028, "support": 1467.0}, "weighted avg": {"precision": 0.8645962815937556, "recall": 0.8623040218132243, "f1-score": 0.8618934055493783, "support": 1467.0}}, "confusion_matrix": [[581, 132], [70, 684]], "roc_auc": 0.9455554480824104, "training_time": 0.02135, "train_size": 489, "test_size": 1467, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli": {"accuracy": 0.8057259713701431, "precision": [0.7165991902834008, 0.9895615866388309], "recall": [0.9929873772791024, 0.6286472148541115], "f1_score": [0.8324514991181657, 0.7688564476885644], "support": [713, 754], "classification_report": {"0": {"precision": 0.7165991902834008, "recall": 0.9929873772791024, "f1-score": 0.8324514991181657, "support": 713.0}, "1": {"precision": 0.9895615866388309, "recall": 0.6286472148541115, "f1-score": 0.7688564476885644, "support": 754.0}, "accuracy": 0.8057259713701431, "macro avg": {"precision": 0.8530803884611158, "recall": 0.8108172960666069, "f1-score": 0.8006539734033651, "support": 1467.0}, "weighted avg": {"precision": 0.8568947914095045, "recall": 0.8057259713701431, "f1-score": 0.7997652899989296, "support": 1467.0}}, "confusion_matrix": [[708, 5], [280, 474]], "roc_auc": 0.9421635336178065, "training_time": 0.020103, "train_size": 489, "test_size": 1467, "algorithm_type": "<PERSON><PERSON>"}, "NB_Complement": {"accuracy": 0.8609406952965235, "precision": [0.8344283837056504, 0.8895184135977338], "recall": [0.8906030855539971, 0.8328912466843501], "f1_score": [0.8616010854816825, 0.8602739726027397], "support": [713, 754], "classification_report": {"0": {"precision": 0.8344283837056504, "recall": 0.8906030855539971, "f1-score": 0.8616010854816825, "support": 713.0}, "1": {"precision": 0.8895184135977338, "recall": 0.8328912466843501, "f1-score": 0.8602739726027397, "support": 754.0}, "accuracy": 0.8609406952965235, "macro avg": {"precision": 0.8619733986516921, "recall": 0.8617471661191736, "f1-score": 0.8609375290422111, "support": 1467.0}, "weighted avg": {"precision": 0.8627432320619087, "recall": 0.8609406952965235, "f1-score": 0.8609189838383813, "support": 1467.0}}, "confusion_matrix": [[635, 78], [126, 628]], "roc_auc": 0.9455554480824104, "training_time": 0.0, "train_size": 489, "test_size": 1467, "algorithm_type": "<PERSON><PERSON>"}, "NB_Multinomial_Tuned": {"accuracy": 0.869120654396728, "precision": [0.8859259259259259, 0.8547979797979798], "recall": [0.8387096774193549, 0.8978779840848806], "f1_score": [0.861671469740634, 0.8758085381630013], "support": [713, 754], "classification_report": {"0": {"precision": 0.8859259259259259, "recall": 0.8387096774193549, "f1-score": 0.861671469740634, "support": 713.0}, "1": {"precision": 0.8547979797979798, "recall": 0.8978779840848806, "f1-score": 0.8758085381630013, "support": 754.0}, "accuracy": 0.869120654396728, "macro avg": {"precision": 0.8703619528619528, "recall": 0.8682938307521177, "f1-score": 0.8687400039518176, "support": 1467.0}, "weighted avg": {"precision": 0.8699269679296946, "recall": 0.869120654396728, "f1-score": 0.8689375567143661, "support": 1467.0}}, "confusion_matrix": [[598, 115], [77, 677]], "roc_auc": 0.9438478651493112, "training_time": 0.007986, "train_size": 489, "test_size": 1467, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli_Tuned": {"accuracy": 0.8377641445126107, "precision": [0.7624309392265194, 0.9590747330960854], "recall": [0.967741935483871, 0.7148541114058355], "f1_score": [0.8529048207663782, 0.8191489361702128], "support": [713, 754], "classification_report": {"0": {"precision": 0.7624309392265194, "recall": 0.967741935483871, "f1-score": 0.8529048207663782, "support": 713.0}, "1": {"precision": 0.9590747330960854, "recall": 0.7148541114058355, "f1-score": 0.8191489361702128, "support": 754.0}, "accuracy": 0.8377641445126107, "macro avg": {"precision": 0.8607528361613024, "recall": 0.8412980234448533, "f1-score": 0.8360268784682955, "support": 1467.0}, "weighted avg": {"precision": 0.8635007555712044, "recall": 0.8377641445126107, "f1-score": 0.8355551704695079, "support": 1467.0}}, "confusion_matrix": [[690, 23], [215, 539]], "roc_auc": 0.9361116588107932, "training_time": 0.004937, "train_size": 489, "test_size": 1467, "algorithm_type": "<PERSON><PERSON>"}, "LR_L1": {"accuracy": 0.8425357873210634, "precision": [0.7660044150110376, 0.966131907308378], "recall": [0.9733520336605891, 0.7188328912466844], "f1_score": [0.8573193329215565, 0.8243346007604563], "support": [713, 754], "classification_report": {"0": {"precision": 0.7660044150110376, "recall": 0.9733520336605891, "f1-score": 0.8573193329215565, "support": 713.0}, "1": {"precision": 0.966131907308378, "recall": 0.7188328912466844, "f1-score": 0.8243346007604563, "support": 754.0}, "accuracy": 0.8425357873210634, "macro avg": {"precision": 0.8660681611597078, "recall": 0.8460924624536368, "f1-score": 0.8408269668410064, "support": 1467.0}, "weighted avg": {"precision": 0.868864762108648, "recall": 0.8425357873210634, "f1-score": 0.8403660350009909, "support": 1467.0}}, "confusion_matrix": [[694, 19], [212, 542]], "roc_auc": 0.9007034944066428, "training_time": 0.008984, "train_size": 489, "test_size": 1467, "algorithm_type": "Logistic Regression"}, "LR_L2": {"accuracy": 0.8779822767552828, "precision": [0.8396946564885496, 0.922173274596182], "recall": [0.9256661991584852, 0.8328912466843501], "f1_score": [0.8805870580386924, 0.8752613240418119], "support": [713, 754], "classification_report": {"0": {"precision": 0.8396946564885496, "recall": 0.9256661991584852, "f1-score": 0.8805870580386924, "support": 713.0}, "1": {"precision": 0.922173274596182, "recall": 0.8328912466843501, "f1-score": 0.8752613240418119, "support": 754.0}, "accuracy": 0.8779822767552828, "macro avg": {"precision": 0.8809339655423658, "recall": 0.8792787229214176, "f1-score": 0.8779241910402522, "support": 1467.0}, "weighted avg": {"precision": 0.8820865297354173, "recall": 0.8779822767552828, "f1-score": 0.877849768717869, "support": 1467.0}}, "confusion_matrix": [[660, 53], [126, 628]], "roc_auc": 0.946468763137042, "training_time": 0.010949, "train_size": 489, "test_size": 1467, "algorithm_type": "Logistic Regression"}, "LR_ElasticNet": {"accuracy": 0.8725289706884799, "precision": [0.8145933014354066, 0.9492868462757528], "recall": [0.9551192145862553, 0.7944297082228117], "f1_score": [0.8792769528728211, 0.8649819494584837], "support": [713, 754], "classification_report": {"0": {"precision": 0.8145933014354066, "recall": 0.9551192145862553, "f1-score": 0.8792769528728211, "support": 713.0}, "1": {"precision": 0.9492868462757528, "recall": 0.7944297082228117, "f1-score": 0.8649819494584837, "support": 754.0}, "accuracy": 0.8725289706884799, "macro avg": {"precision": 0.8819400738555797, "recall": 0.8747744614045334, "f1-score": 0.8721294511656524, "support": 1467.0}, "weighted avg": {"precision": 0.8838222944889997, "recall": 0.8725289706884799, "f1-score": 0.8719296914042387, "support": 1467.0}}, "confusion_matrix": [[681, 32], [155, 599]], "roc_auc": 0.9273728520355208, "training_time": 0.009992, "train_size": 489, "test_size": 1467, "algorithm_type": "Logistic Regression"}, "LR_LBFGS": {"accuracy": 0.8779822767552828, "precision": [0.8396946564885496, 0.922173274596182], "recall": [0.9256661991584852, 0.8328912466843501], "f1_score": [0.8805870580386924, 0.8752613240418119], "support": [713, 754], "classification_report": {"0": {"precision": 0.8396946564885496, "recall": 0.9256661991584852, "f1-score": 0.8805870580386924, "support": 713.0}, "1": {"precision": 0.922173274596182, "recall": 0.8328912466843501, "f1-score": 0.8752613240418119, "support": 754.0}, "accuracy": 0.8779822767552828, "macro avg": {"precision": 0.8809339655423658, "recall": 0.8792787229214176, "f1-score": 0.8779241910402522, "support": 1467.0}, "weighted avg": {"precision": 0.8820865297354173, "recall": 0.8779822767552828, "f1-score": 0.877849768717869, "support": 1467.0}}, "confusion_matrix": [[660, 53], [126, 628]], "roc_auc": 0.9465599086312924, "training_time": 0.017698, "train_size": 489, "test_size": 1467, "algorithm_type": "Logistic Regression"}, "LR_SAG": {"accuracy": 0.8773006134969326, "precision": [0.8386277001270648, 0.9220588235294118], "recall": [0.9256661991584852, 0.8315649867374005], "f1_score": [0.88, 0.8744769874476988], "support": [713, 754], "classification_report": {"0": {"precision": 0.8386277001270648, "recall": 0.9256661991584852, "f1-score": 0.88, "support": 713.0}, "1": {"precision": 0.9220588235294118, "recall": 0.8315649867374005, "f1-score": 0.8744769874476988, "support": 754.0}, "accuracy": 0.8773006134969326, "macro avg": {"precision": 0.8803432618282383, "recall": 0.8786155929479429, "f1-score": 0.8772384937238493, "support": 1467.0}, "weighted avg": {"precision": 0.8815091364224771, "recall": 0.8773006134969326, "f1-score": 0.877161314611837, "support": 1467.0}}, "confusion_matrix": [[660, 53], [127, 627]], "roc_auc": 0.9465096856038483, "training_time": 0.0, "train_size": 489, "test_size": 1467, "algorithm_type": "Logistic Regression"}, "LR_SAGA": {"accuracy": 0.8779822767552828, "precision": [0.8396946564885496, 0.922173274596182], "recall": [0.9256661991584852, 0.8328912466843501], "f1_score": [0.8805870580386924, 0.8752613240418119], "support": [713, 754], "classification_report": {"0": {"precision": 0.8396946564885496, "recall": 0.9256661991584852, "f1-score": 0.8805870580386924, "support": 713.0}, "1": {"precision": 0.922173274596182, "recall": 0.8328912466843501, "f1-score": 0.8752613240418119, "support": 754.0}, "accuracy": 0.8779822767552828, "macro avg": {"precision": 0.8809339655423658, "recall": 0.8792787229214176, "f1-score": 0.8779241910402522, "support": 1467.0}, "weighted avg": {"precision": 0.8820865297354173, "recall": 0.8779822767552828, "f1-score": 0.877849768717869, "support": 1467.0}}, "confusion_matrix": [[660, 53], [126, 628]], "roc_auc": 0.9462939125970514, "training_time": 0.011267, "train_size": 489, "test_size": 1467, "algorithm_type": "Logistic Regression"}, "DT_Gini": {"accuracy": 0.8214042263122018, "precision": [0.8921739130434783, 0.7757847533632287], "recall": [0.7194950911640954, 0.9177718832891246], "f1_score": [0.796583850931677, 0.8408262454434994], "support": [713, 754], "classification_report": {"0": {"precision": 0.8921739130434783, "recall": 0.7194950911640954, "f1-score": 0.796583850931677, "support": 713.0}, "1": {"precision": 0.7757847533632287, "recall": 0.9177718832891246, "f1-score": 0.8408262454434994, "support": 754.0}, "accuracy": 0.8214042263122018, "macro avg": {"precision": 0.8339793332033535, "recall": 0.81863348722661, "f1-score": 0.8187050481875882, "support": 1467.0}, "weighted avg": {"precision": 0.8323528998199552, "recall": 0.8214042263122018, "f1-score": 0.8193232956909915, "support": 1467.0}}, "confusion_matrix": [[513, 200], [62, 692]], "roc_auc": 0.9141846570511272, "training_time": 0.022977, "train_size": 489, "test_size": 1467, "algorithm_type": "Decision Tree"}, "DT_Entropy": {"accuracy": 0.8173142467620995, "precision": [0.8856152512998267, 0.7730337078651686], "recall": [0.7166900420757363, 0.9124668435013262], "f1_score": [0.7922480620155039, 0.8369829683698297], "support": [713, 754], "classification_report": {"0": {"precision": 0.8856152512998267, "recall": 0.7166900420757363, "f1-score": 0.7922480620155039, "support": 713.0}, "1": {"precision": 0.7730337078651686, "recall": 0.9124668435013262, "f1-score": 0.8369829683698297, "support": 754.0}, "accuracy": 0.8173142467620995, "macro avg": {"precision": 0.8293244795824977, "recall": 0.8145784427885312, "f1-score": 0.8146155151926668, "support": 1467.0}, "weighted avg": {"precision": 0.8277512541970781, "recall": 0.8173142467620995, "f1-score": 0.8152406451042303, "support": 1467.0}}, "confusion_matrix": [[511, 202], [66, 688]], "roc_auc": 0.9051761340173586, "training_time": 0.02528, "train_size": 489, "test_size": 1467, "algorithm_type": "Decision Tree"}, "DT_Log_Loss": {"accuracy": 0.8173142467620995, "precision": [0.8856152512998267, 0.7730337078651686], "recall": [0.7166900420757363, 0.9124668435013262], "f1_score": [0.7922480620155039, 0.8369829683698297], "support": [713, 754], "classification_report": {"0": {"precision": 0.8856152512998267, "recall": 0.7166900420757363, "f1-score": 0.7922480620155039, "support": 713.0}, "1": {"precision": 0.7730337078651686, "recall": 0.9124668435013262, "f1-score": 0.8369829683698297, "support": 754.0}, "accuracy": 0.8173142467620995, "macro avg": {"precision": 0.8293244795824977, "recall": 0.8145784427885312, "f1-score": 0.8146155151926668, "support": 1467.0}, "weighted avg": {"precision": 0.8277512541970781, "recall": 0.8173142467620995, "f1-score": 0.8152406451042303, "support": 1467.0}}, "confusion_matrix": [[511, 202], [66, 688]], "roc_auc": 0.9051761340173586, "training_time": 0.027984, "train_size": 489, "test_size": 1467, "algorithm_type": "Decision Tree"}, "DT_Gini_Pruned": {"accuracy": 0.858214042263122, "precision": [0.7840269966254219, 0.972318339100346], "recall": [0.9775596072931276, 0.7453580901856764], "f1_score": [0.8701622971285893, 0.8438438438438438], "support": [713, 754], "classification_report": {"0": {"precision": 0.7840269966254219, "recall": 0.9775596072931276, "f1-score": 0.8701622971285893, "support": 713.0}, "1": {"precision": 0.972318339100346, "recall": 0.7453580901856764, "f1-score": 0.8438438438438438, "support": 754.0}, "accuracy": 0.858214042263122, "macro avg": {"precision": 0.878172667862884, "recall": 0.861458848739402, "f1-score": 0.8570030704862166, "support": 1467.0}, "weighted avg": {"precision": 0.8808038693085117, "recall": 0.858214042263122, "f1-score": 0.8566352938724896, "support": 1467.0}}, "confusion_matrix": [[697, 16], [192, 562]], "roc_auc": 0.8579032444075728, "training_time": 0.018774, "train_size": 489, "test_size": 1467, "algorithm_type": "Decision Tree"}, "DT_Entropy_Pruned": {"accuracy": 0.8289025221540559, "precision": [0.746268656716418, 0.9754253308128544], "recall": [0.9817671809256662, 0.6843501326259946], "f1_score": [0.8479709267110842, 0.8043647700701481], "support": [713, 754], "classification_report": {"0": {"precision": 0.746268656716418, "recall": 0.9817671809256662, "f1-score": 0.8479709267110842, "support": 713.0}, "1": {"precision": 0.9754253308128544, "recall": 0.6843501326259946, "f1-score": 0.8043647700701481, "support": 754.0}, "accuracy": 0.8289025221540559, "macro avg": {"precision": 0.8608469937646361, "recall": 0.8330586567758305, "f1-score": 0.8261678483906161, "support": 1467.0}, "weighted avg": {"precision": 0.8640492513099511, "recall": 0.8289025221540559, "f1-score": 0.8255584917368062, "support": 1467.0}}, "confusion_matrix": [[700, 13], [238, 516]], "roc_auc": 0.8560012797571438, "training_time": 0.018963, "train_size": 489, "test_size": 1467, "algorithm_type": "Decision Tree"}, "DT_Best_First": {"accuracy": 0.8214042263122018, "precision": [0.8921739130434783, 0.7757847533632287], "recall": [0.7194950911640954, 0.9177718832891246], "f1_score": [0.796583850931677, 0.8408262454434994], "support": [713, 754], "classification_report": {"0": {"precision": 0.8921739130434783, "recall": 0.7194950911640954, "f1-score": 0.796583850931677, "support": 713.0}, "1": {"precision": 0.7757847533632287, "recall": 0.9177718832891246, "f1-score": 0.8408262454434994, "support": 754.0}, "accuracy": 0.8214042263122018, "macro avg": {"precision": 0.8339793332033535, "recall": 0.81863348722661, "f1-score": 0.8187050481875882, "support": 1467.0}, "weighted avg": {"precision": 0.8323528998199552, "recall": 0.8214042263122018, "f1-score": 0.8193232956909915, "support": 1467.0}}, "confusion_matrix": [[513, 200], [62, 692]], "roc_auc": 0.9141846570511272, "training_time": 0.0235, "train_size": 489, "test_size": 1467, "algorithm_type": "Decision Tree"}, "DT_Random_Split": {"accuracy": 0.8159509202453987, "precision": [0.8773424190800682, 0.775], "recall": [0.7223001402524544, 0.9045092838196287], "f1_score": [0.7923076923076923, 0.8347613219094248], "support": [713, 754], "classification_report": {"0": {"precision": 0.8773424190800682, "recall": 0.7223001402524544, "f1-score": 0.7923076923076923, "support": 713.0}, "1": {"precision": 0.775, "recall": 0.9045092838196287, "f1-score": 0.8347613219094248, "support": 754.0}, "accuracy": 0.8159509202453987, "macro avg": {"precision": 0.8261712095400341, "recall": 0.8134047120360415, "f1-score": 0.8135345071085585, "support": 1467.0}, "weighted avg": {"precision": 0.8247410666694538, "recall": 0.8159509202453987, "f1-score": 0.8141277582379624, "support": 1467.0}}, "confusion_matrix": [[515, 198], [72, 682]], "roc_auc": 0.9029663208098184, "training_time": 0.017964, "train_size": 489, "test_size": 1467, "algorithm_type": "Decision Tree"}, "RF_Gini": {"accuracy": 0.8084526244035446, "precision": [0.8985239852398524, 0.7556756756756757], "recall": [0.6830294530154277, 0.9270557029177718], "f1_score": [0.7760956175298804, 0.8326384752829065], "support": [713, 754], "classification_report": {"0": {"precision": 0.8985239852398524, "recall": 0.6830294530154277, "f1-score": 0.7760956175298804, "support": 713.0}, "1": {"precision": 0.7556756756756757, "recall": 0.9270557029177718, "f1-score": 0.8326384752829065, "support": 754.0}, "accuracy": 0.8084526244035446, "macro avg": {"precision": 0.8270998304577641, "recall": 0.8050425779665997, "f1-score": 0.8043670464063934, "support": 1467.0}, "weighted avg": {"precision": 0.8251036543527432, "recall": 0.8084526244035446, "f1-score": 0.805157181773767, "support": 1467.0}}, "confusion_matrix": [[487, 226], [55, 699]], "roc_auc": 0.9001249995349719, "training_time": 0.314842, "train_size": 489, "test_size": 1467, "algorithm_type": "Random Forest"}, "RF_Entropy": {"accuracy": 0.8084526244035446, "precision": [0.9060150375939849, 0.7529411764705882], "recall": [0.6760168302945302, 0.9336870026525199], "f1_score": [0.7742971887550201, 0.8336293664890467], "support": [713, 754], "classification_report": {"0": {"precision": 0.9060150375939849, "recall": 0.6760168302945302, "f1-score": 0.7742971887550201, "support": 713.0}, "1": {"precision": 0.7529411764705882, "recall": 0.9336870026525199, "f1-score": 0.8336293664890467, "support": 754.0}, "accuracy": 0.8084526244035446, "macro avg": {"precision": 0.8294781070322865, "recall": 0.804851916473525, "f1-score": 0.8039632776220333, "support": 1467.0}, "weighted avg": {"precision": 0.8273390380799828, "recall": 0.8084526244035446, "f1-score": 0.804792391216817, "support": 1467.0}}, "confusion_matrix": [[482, 231], [50, 704]], "roc_auc": 0.9033932165430933, "training_time": 0.237189, "train_size": 489, "test_size": 1467, "algorithm_type": "Random Forest"}, "RF_Log_Loss": {"accuracy": 0.8084526244035446, "precision": [0.9060150375939849, 0.7529411764705882], "recall": [0.6760168302945302, 0.9336870026525199], "f1_score": [0.7742971887550201, 0.8336293664890467], "support": [713, 754], "classification_report": {"0": {"precision": 0.9060150375939849, "recall": 0.6760168302945302, "f1-score": 0.7742971887550201, "support": 713.0}, "1": {"precision": 0.7529411764705882, "recall": 0.9336870026525199, "f1-score": 0.8336293664890467, "support": 754.0}, "accuracy": 0.8084526244035446, "macro avg": {"precision": 0.8294781070322865, "recall": 0.804851916473525, "f1-score": 0.8039632776220333, "support": 1467.0}, "weighted avg": {"precision": 0.8273390380799828, "recall": 0.8084526244035446, "f1-score": 0.804792391216817, "support": 1467.0}}, "confusion_matrix": [[482, 231], [50, 704]], "roc_auc": 0.9033932165430933, "training_time": 0.243431, "train_size": 489, "test_size": 1467, "algorithm_type": "Random Forest"}, "RF_Large": {"accuracy": 0.8616223585548739, "precision": [0.791095890410959, 0.9661590524534687], "recall": [0.9719495091164095, 0.7572944297082228], "f1_score": [0.8722466960352423, 0.8490706319702602], "support": [713, 754], "classification_report": {"0": {"precision": 0.791095890410959, "recall": 0.9719495091164095, "f1-score": 0.8722466960352423, "support": 713.0}, "1": {"precision": 0.9661590524534687, "recall": 0.7572944297082228, "f1-score": 0.8490706319702602, "support": 754.0}, "accuracy": 0.8616223585548739, "macro avg": {"precision": 0.8786274714322138, "recall": 0.8646219694123162, "f1-score": 0.8606586640027513, "support": 1467.0}, "weighted avg": {"precision": 0.8810738210040416, "recall": 0.8616223585548739, "f1-score": 0.8603347994401528, "support": 1467.0}}, "confusion_matrix": [[693, 20], [183, 571]], "roc_auc": 0.9428331739837277, "training_time": 0.293418, "train_size": 489, "test_size": 1467, "algorithm_type": "Random Forest"}, "RF_Small": {"accuracy": 0.8568507157464212, "precision": [0.7848244620611552, 0.9657534246575342], "recall": [0.9719495091164095, 0.7480106100795756], "f1_score": [0.868421052631579, 0.8430493273542601], "support": [713, 754], "classification_report": {"0": {"precision": 0.7848244620611552, "recall": 0.9719495091164095, "f1-score": 0.868421052631579, "support": 713.0}, "1": {"precision": 0.9657534246575342, "recall": 0.7480106100795756, "f1-score": 0.8430493273542601, "support": 754.0}, "accuracy": 0.8568507157464212, "macro avg": {"precision": 0.8752889433593447, "recall": 0.8599800595979925, "f1-score": 0.8557351899929195, "support": 1467.0}, "weighted avg": {"precision": 0.8778172621958994, "recall": 0.8568507157464212, "f1-score": 0.8553806430480081, "support": 1467.0}}, "confusion_matrix": [[693, 20], [190, 564]], "roc_auc": 0.9302495154407907, "training_time": 0.071284, "train_size": 489, "test_size": 1467, "algorithm_type": "Random Forest"}, "ExtraTrees": {"accuracy": 0.8486707566462167, "precision": [0.9139966273187183, 0.8043478260869565], "recall": [0.7601683029453016, 0.9323607427055703], "f1_score": [0.8300153139356815, 0.8636363636363636], "support": [713, 754], "classification_report": {"0": {"precision": 0.9139966273187183, "recall": 0.7601683029453016, "f1-score": 0.8300153139356815, "support": 713.0}, "1": {"precision": 0.8043478260869565, "recall": 0.9323607427055703, "f1-score": 0.8636363636363636, "support": 754.0}, "accuracy": 0.8486707566462167, "macro avg": {"precision": 0.8591722267028374, "recall": 0.846264522825436, "f1-score": 0.8468258387860226, "support": 1467.0}, "weighted avg": {"precision": 0.8576399837408394, "recall": 0.8486707566462167, "f1-score": 0.8472956625889292, "support": 1467.0}}, "confusion_matrix": [[542, 171], [51, 703]], "roc_auc": 0.9211172949505397, "training_time": 0.255983, "train_size": 489, "test_size": 1467, "algorithm_type": "Random Forest"}, "ExtraTrees_Large": {"accuracy": 0.8629856850715747, "precision": [0.7976744186046512, 0.9555189456342669], "recall": [0.9621318373071529, 0.7692307692307693], "f1_score": [0.8722186904005086, 0.852314474650992], "support": [713, 754], "classification_report": {"0": {"precision": 0.7976744186046512, "recall": 0.9621318373071529, "f1-score": 0.8722186904005086, "support": 713.0}, "1": {"precision": 0.9555189456342669, "recall": 0.7692307692307693, "f1-score": 0.852314474650992, "support": 754.0}, "accuracy": 0.8629856850715747, "macro avg": {"precision": 0.8765966821194591, "recall": 0.8656813032689611, "f1-score": 0.8622665825257503, "support": 1467.0}, "weighted avg": {"precision": 0.8788024168189186, "recall": 0.8629856850715747, "f1-score": 0.8619884390882145, "support": 1467.0}}, "confusion_matrix": [[686, 27], [174, 580]], "roc_auc": 0.9451601742553041, "training_time": 0.233306, "train_size": 489, "test_size": 1467, "algorithm_type": "Random Forest"}, "GradientBoosting": {"accuracy": 0.8813905930470347, "precision": [0.8122827346465817, 0.9801324503311258], "recall": [0.9831697054698457, 0.7851458885941645], "f1_score": [0.8895939086294417, 0.8718703976435935], "support": [713, 754], "classification_report": {"0": {"precision": 0.8122827346465817, "recall": 0.9831697054698457, "f1-score": 0.8895939086294417, "support": 713.0}, "1": {"precision": 0.9801324503311258, "recall": 0.7851458885941645, "f1-score": 0.8718703976435935, "support": 754.0}, "accuracy": 0.8813905930470347, "macro avg": {"precision": 0.8962075924888537, "recall": 0.8841577970320051, "f1-score": 0.8807321531365175, "support": 1467.0}, "weighted avg": {"precision": 0.8985531406630415, "recall": 0.8813905930470347, "f1-score": 0.8804844830784332, "support": 1467.0}}, "confusion_matrix": [[701, 12], [162, 592]], "roc_auc": 0.9325049013954559, "training_time": 0.295825, "train_size": 489, "test_size": 1467, "algorithm_type": "Ensemble"}, "AdaBoost": {"accuracy": 0.8575323790047716, "precision": [0.7916666666666666, 0.9519071310116086], "recall": [0.9593267882187938, 0.7612732095490716], "f1_score": [0.8674698795180723, 0.8459837877671333], "support": [713, 754], "classification_report": {"0": {"precision": 0.7916666666666666, "recall": 0.9593267882187938, "f1-score": 0.8674698795180723, "support": 713.0}, "1": {"precision": 0.9519071310116086, "recall": 0.7612732095490716, "f1-score": 0.8459837877671333, "support": 754.0}, "accuracy": 0.8575323790047716, "macro avg": {"precision": 0.8717868988391376, "recall": 0.8602999988839327, "f1-score": 0.8567268336426028, "support": 1467.0}, "weighted avg": {"precision": 0.8740261145985591, "recall": 0.8575323790047716, "f1-score": 0.8564265849167035, "support": 1467.0}}, "confusion_matrix": [[684, 29], [180, 574]], "roc_auc": 0.9162484514566539, "training_time": 0.278965, "train_size": 489, "test_size": 1467, "algorithm_type": "Ensemble"}, "AdaBoost_DT": {"accuracy": 0.8548057259713702, "precision": [0.8918495297805643, 0.8262967430639324], "recall": [0.7980364656381487, 0.9084880636604774], "f1_score": [0.8423390081421169, 0.8654453569172458], "support": [713, 754], "classification_report": {"0": {"precision": 0.8918495297805643, "recall": 0.7980364656381487, "f1-score": 0.8423390081421169, "support": 713.0}, "1": {"precision": 0.8262967430639324, "recall": 0.9084880636604774, "f1-score": 0.8654453569172458, "support": 754.0}, "accuracy": 0.8548057259713702, "macro avg": {"precision": 0.8590731364222484, "recall": 0.8532622646493131, "f1-score": 0.8538921825296814, "support": 1467.0}, "weighted avg": {"precision": 0.8581570954354106, "recall": 0.8548057259713702, "f1-score": 0.8542150728840714, "support": 1467.0}}, "confusion_matrix": [[569, 144], [69, 685]], "roc_auc": 0.9409749219682962, "training_time": 0.47017, "train_size": 489, "test_size": 1467, "algorithm_type": "Decision Tree"}}, "train_30_test_70_REVERSED": {"SVM_Linear": {"accuracy": 0.8905109489051095, "precision": [0.844, 0.9467741935483871], "recall": [0.9504504504504504, 0.8338068181818182], "f1_score": [0.8940677966101694, 0.8867069486404834], "support": [666, 704], "classification_report": {"0": {"precision": 0.844, "recall": 0.9504504504504504, "f1-score": 0.8940677966101694, "support": 666.0}, "1": {"precision": 0.9467741935483871, "recall": 0.8338068181818182, "f1-score": 0.8867069486404834, "support": 704.0}, "accuracy": 0.8905109489051095, "macro avg": {"precision": 0.8953870967741935, "recall": 0.8921286343161343, "f1-score": 0.8903873726253264, "support": 1370.0}, "weighted avg": {"precision": 0.8968124323051564, "recall": 0.8905109489051095, "f1-score": 0.8902852878724621, "support": 1370.0}}, "confusion_matrix": [[633, 33], [117, 587]], "roc_auc": 0.9574396840021839, "training_time": 0.105607, "train_size": 586, "test_size": 1370, "algorithm_type": "Support Vector Machine"}, "SVM_Polynomial": {"accuracy": 0.8467153284671532, "precision": [0.8713355048859935, 0.8267195767195767], "recall": [0.8033033033033034, 0.8877840909090909], "f1_score": [0.8359375, 0.8561643835616438], "support": [666, 704], "classification_report": {"0": {"precision": 0.8713355048859935, "recall": 0.8033033033033034, "f1-score": 0.8359375, "support": 666.0}, "1": {"precision": 0.8267195767195767, "recall": 0.8877840909090909, "f1-score": 0.8561643835616438, "support": 704.0}, "accuracy": 0.8467153284671532, "macro avg": {"precision": 0.8490275408027852, "recall": 0.8455436971061971, "f1-score": 0.846050941780822, "support": 1370.0}, "weighted avg": {"precision": 0.8484087797552217, "recall": 0.8467153284671532, "f1-score": 0.8463314606039397, "support": 1370.0}}, "confusion_matrix": [[535, 131], [79, 625]], "roc_auc": 0.9277988926426426, "training_time": 0.114819, "train_size": 586, "test_size": 1370, "algorithm_type": "Support Vector Machine"}, "SVM_RBF": {"accuracy": 0.8897810218978102, "precision": [0.8694404591104734, 0.9108469539375929], "recall": [0.9099099099099099, 0.8707386363636364], "f1_score": [0.8892149669845928, 0.8903413217138707], "support": [666, 704], "classification_report": {"0": {"precision": 0.8694404591104734, "recall": 0.9099099099099099, "f1-score": 0.8892149669845928, "support": 666.0}, "1": {"precision": 0.9108469539375929, "recall": 0.8707386363636364, "f1-score": 0.8903413217138707, "support": 704.0}, "accuracy": 0.8897810218978102, "macro avg": {"precision": 0.8901437065240332, "recall": 0.8903242731367731, "f1-score": 0.8897781443492317, "support": 1370.0}, "weighted avg": {"precision": 0.8907179571822195, "recall": 0.8897810218978102, "f1-score": 0.889793765327229, "support": 1370.0}}, "confusion_matrix": [[606, 60], [91, 613]], "roc_auc": 0.9347998566748567, "training_time": 0.102122, "train_size": 586, "test_size": 1370, "algorithm_type": "Support Vector Machine"}, "SVM_Sigmoid": {"accuracy": 0.8861313868613139, "precision": [0.8337696335078534, 0.9521452145214522], "recall": [0.9564564564564565, 0.8196022727272727], "f1_score": [0.8909090909090909, 0.8809160305343512], "support": [666, 704], "classification_report": {"0": {"precision": 0.8337696335078534, "recall": 0.9564564564564565, "f1-score": 0.8909090909090909, "support": 666.0}, "1": {"precision": 0.9521452145214522, "recall": 0.8196022727272727, "f1-score": 0.8809160305343512, "support": 704.0}, "accuracy": 0.8861313868613139, "macro avg": {"precision": 0.8929574240146527, "recall": 0.8880293645918647, "f1-score": 0.8859125607217211, "support": 1370.0}, "weighted avg": {"precision": 0.8945991291527976, "recall": 0.8861313868613139, "f1-score": 0.8857739708333123, "support": 1370.0}}, "confusion_matrix": [[637, 29], [127, 577]], "roc_auc": 0.9574599457411958, "training_time": 0.083393, "train_size": 586, "test_size": 1370, "algorithm_type": "Support Vector Machine"}, "SVM_RBF_Tuned": {"accuracy": 0.891970802919708, "precision": [0.8775510204081632, 0.9064327485380117], "recall": [0.9039039039039038, 0.8806818181818182], "f1_score": [0.8905325443786982, 0.8933717579250721], "support": [666, 704], "classification_report": {"0": {"precision": 0.8775510204081632, "recall": 0.9039039039039038, "f1-score": 0.8905325443786982, "support": 666.0}, "1": {"precision": 0.9064327485380117, "recall": 0.8806818181818182, "f1-score": 0.8933717579250721, "support": 704.0}, "accuracy": 0.891970802919708, "macro avg": {"precision": 0.8919918844730874, "recall": 0.8922928610428611, "f1-score": 0.8919521511518851, "support": 1370.0}, "weighted avg": {"precision": 0.892392433987297, "recall": 0.891970802919708, "f1-score": 0.8919915271061779, "support": 1370.0}}, "confusion_matrix": [[602, 64], [84, 620]], "roc_auc": 0.9354162401037401, "training_time": 0.125672, "train_size": 586, "test_size": 1370, "algorithm_type": "Support Vector Machine"}, "SVM_Linear_Tuned": {"accuracy": 0.8905109489051095, "precision": [0.844, 0.9467741935483871], "recall": [0.9504504504504504, 0.8338068181818182], "f1_score": [0.8940677966101694, 0.8867069486404834], "support": [666, 704], "classification_report": {"0": {"precision": 0.844, "recall": 0.9504504504504504, "f1-score": 0.8940677966101694, "support": 666.0}, "1": {"precision": 0.9467741935483871, "recall": 0.8338068181818182, "f1-score": 0.8867069486404834, "support": 704.0}, "accuracy": 0.8905109489051095, "macro avg": {"precision": 0.8953870967741935, "recall": 0.8921286343161343, "f1-score": 0.8903873726253264, "support": 1370.0}, "weighted avg": {"precision": 0.8968124323051564, "recall": 0.8905109489051095, "f1-score": 0.8902852878724621, "support": 1370.0}}, "confusion_matrix": [[633, 33], [117, 587]], "roc_auc": 0.9574396840021839, "training_time": 0.101092, "train_size": 586, "test_size": 1370, "algorithm_type": "Support Vector Machine"}, "NB_Multinomial": {"accuracy": 0.872992700729927, "precision": [0.9006514657980456, 0.8505291005291006], "recall": [0.8303303303303303, 0.9133522727272727], "f1_score": [0.8640625, 0.8808219178082192], "support": [666, 704], "classification_report": {"0": {"precision": 0.9006514657980456, "recall": 0.8303303303303303, "f1-score": 0.8640625, "support": 666.0}, "1": {"precision": 0.8505291005291006, "recall": 0.9133522727272727, "f1-score": 0.8808219178082192, "support": 704.0}, "accuracy": 0.872992700729927, "macro avg": {"precision": 0.8755902831635731, "recall": 0.8718413015288016, "f1-score": 0.8724422089041095, "support": 1370.0}, "weighted avg": {"precision": 0.8748951554700621, "recall": 0.872992700729927, "f1-score": 0.8726746387861214, "support": 1370.0}}, "confusion_matrix": [[553, 113], [61, 643]], "roc_auc": 0.9488103159978158, "training_time": 0.010873, "train_size": 586, "test_size": 1370, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli": {"accuracy": 0.8262773722627738, "precision": [0.7388392857142857, 0.9915611814345991], "recall": [0.993993993993994, 0.6676136363636364], "f1_score": [0.8476312419974392, 0.797962648556876], "support": [666, 704], "classification_report": {"0": {"precision": 0.7388392857142857, "recall": 0.993993993993994, "f1-score": 0.8476312419974392, "support": 666.0}, "1": {"precision": 0.9915611814345991, "recall": 0.6676136363636364, "f1-score": 0.797962648556876, "support": 704.0}, "accuracy": 0.8262773722627738, "macro avg": {"precision": 0.8652002335744424, "recall": 0.8308038151788152, "f1-score": 0.8227969452771576, "support": 1370.0}, "weighted avg": {"precision": 0.8687051357778628, "recall": 0.8262773722627738, "f1-score": 0.8221081107695878, "support": 1370.0}}, "confusion_matrix": [[662, 4], [234, 470]], "roc_auc": 0.9429557398307398, "training_time": 0.014616, "train_size": 586, "test_size": 1370, "algorithm_type": "<PERSON><PERSON>"}, "NB_Complement": {"accuracy": 0.8693430656934307, "precision": [0.8463726884779517, 0.8935532233883059], "recall": [0.8933933933933934, 0.8465909090909091], "f1_score": [0.8692476260043828, 0.8694383661560905], "support": [666, 704], "classification_report": {"0": {"precision": 0.8463726884779517, "recall": 0.8933933933933934, "f1-score": 0.8692476260043828, "support": 666.0}, "1": {"precision": 0.8935532233883059, "recall": 0.8465909090909091, "f1-score": 0.8694383661560905, "support": 704.0}, "accuracy": 0.8693430656934307, "macro avg": {"precision": 0.8699629559331288, "recall": 0.8699921512421512, "f1-score": 0.8693429960802366, "support": 1370.0}, "weighted avg": {"precision": 0.8706172845194767, "recall": 0.8693430656934307, "f1-score": 0.8693456413816106, "support": 1370.0}}, "confusion_matrix": [[595, 71], [108, 596]], "roc_auc": 0.9488103159978158, "training_time": 0.0, "train_size": 586, "test_size": 1370, "algorithm_type": "<PERSON><PERSON>"}, "NB_Multinomial_Tuned": {"accuracy": 0.8788321167883212, "precision": [0.8943217665615142, 0.8654891304347826], "recall": [0.8513513513513513, 0.9048295454545454], "f1_score": [0.8723076923076923, 0.8847222222222222], "support": [666, 704], "classification_report": {"0": {"precision": 0.8943217665615142, "recall": 0.8513513513513513, "f1-score": 0.8723076923076923, "support": 666.0}, "1": {"precision": 0.8654891304347826, "recall": 0.9048295454545454, "f1-score": 0.8847222222222222, "support": 704.0}, "accuracy": 0.8788321167883212, "macro avg": {"precision": 0.8799054484981483, "recall": 0.8780904484029484, "f1-score": 0.8785149572649573, "support": 1370.0}, "weighted avg": {"precision": 0.8795055798219383, "recall": 0.8788321167883212, "f1-score": 0.8786871295776404, "support": 1370.0}}, "confusion_matrix": [[567, 99], [67, 637]], "roc_auc": 0.9476415335790336, "training_time": 0.009315, "train_size": 586, "test_size": 1370, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli_Tuned": {"accuracy": 0.8481751824817518, "precision": [0.7752403846153846, 0.9609665427509294], "recall": [0.9684684684684685, 0.734375], "f1_score": [0.8611481975967957, 0.8325281803542673], "support": [666, 704], "classification_report": {"0": {"precision": 0.7752403846153846, "recall": 0.9684684684684685, "f1-score": 0.8611481975967957, "support": 666.0}, "1": {"precision": 0.9609665427509294, "recall": 0.734375, "f1-score": 0.8325281803542673, "support": 704.0}, "accuracy": 0.8481751824817518, "macro avg": {"precision": 0.868103463683157, "recall": 0.8514217342342343, "f1-score": 0.8468381889755315, "support": 1370.0}, "weighted avg": {"precision": 0.8706792279200734, "recall": 0.8481751824817518, "f1-score": 0.8464412690283725, "support": 1370.0}}, "confusion_matrix": [[645, 21], [187, 517]], "roc_auc": 0.9371971403221403, "training_time": 0.002869, "train_size": 586, "test_size": 1370, "algorithm_type": "<PERSON><PERSON>"}, "LR_L1": {"accuracy": 0.8496350364963504, "precision": [0.7744630071599046, 0.9680451127819549], "recall": [0.9744744744744744, 0.7315340909090909], "f1_score": [0.863031914893617, 0.8333333333333334], "support": [666, 704], "classification_report": {"0": {"precision": 0.7744630071599046, "recall": 0.9744744744744744, "f1-score": 0.863031914893617, "support": 666.0}, "1": {"precision": 0.9680451127819549, "recall": 0.7315340909090909, "f1-score": 0.8333333333333334, "support": 704.0}, "accuracy": 0.8496350364963504, "macro avg": {"precision": 0.8712540599709298, "recall": 0.8530042826917827, "f1-score": 0.8481826241134751, "support": 1370.0}, "weighted avg": {"precision": 0.8739387753043742, "recall": 0.8496350364963504, "f1-score": 0.8477707459750479, "support": 1370.0}}, "confusion_matrix": [[649, 17], [189, 515]], "roc_auc": 0.9153005988943489, "training_time": 0.00866, "train_size": 586, "test_size": 1370, "algorithm_type": "Logistic Regression"}, "LR_L2": {"accuracy": 0.8846715328467153, "precision": [0.8441734417344173, 0.9319620253164557], "recall": [0.9354354354354354, 0.8366477272727273], "f1_score": [0.8874643874643875, 0.8817365269461078], "support": [666, 704], "classification_report": {"0": {"precision": 0.8441734417344173, "recall": 0.9354354354354354, "f1-score": 0.8874643874643875, "support": 666.0}, "1": {"precision": 0.9319620253164557, "recall": 0.8366477272727273, "f1-score": 0.8817365269461078, "support": 704.0}, "accuracy": 0.8846715328467153, "macro avg": {"precision": 0.8880677335254366, "recall": 0.8860415813540814, "f1-score": 0.8846004572052477, "support": 1370.0}, "weighted avg": {"precision": 0.8892852394291291, "recall": 0.8846715328467153, "f1-score": 0.8845210197236073, "support": 1370.0}}, "confusion_matrix": [[623, 43], [115, 589]], "roc_auc": 0.9488828316953316, "training_time": 0.0, "train_size": 586, "test_size": 1370, "algorithm_type": "Logistic Regression"}, "LR_ElasticNet": {"accuracy": 0.8686131386861314, "precision": [0.8099489795918368, 0.947098976109215], "recall": [0.9534534534534534, 0.7883522727272727], "f1_score": [0.8758620689655172, 0.8604651162790697], "support": [666, 704], "classification_report": {"0": {"precision": 0.8099489795918368, "recall": 0.9534534534534534, "f1-score": 0.8758620689655172, "support": 666.0}, "1": {"precision": 0.947098976109215, "recall": 0.7883522727272727, "f1-score": 0.8604651162790697, "support": 704.0}, "accuracy": 0.8686131386861314, "macro avg": {"precision": 0.8785239778505258, "recall": 0.870902863090363, "f1-score": 0.8681635926222935, "support": 1370.0}, "weighted avg": {"precision": 0.8804260580941976, "recall": 0.8686131386861314, "f1-score": 0.8679500582419705, "support": 1370.0}}, "confusion_matrix": [[635, 31], [149, 555]], "roc_auc": 0.9312817789380289, "training_time": 0.025944, "train_size": 586, "test_size": 1370, "algorithm_type": "Logistic Regression"}, "LR_LBFGS": {"accuracy": 0.8846715328467153, "precision": [0.8432432432432433, 0.9333333333333333], "recall": [0.9369369369369369, 0.8352272727272727], "f1_score": [0.887624466571835, 0.881559220389805], "support": [666, 704], "classification_report": {"0": {"precision": 0.8432432432432433, "recall": 0.9369369369369369, "f1-score": 0.887624466571835, "support": 666.0}, "1": {"precision": 0.9333333333333333, "recall": 0.8352272727272727, "f1-score": 0.881559220389805, "support": 704.0}, "accuracy": 0.8846715328467153, "macro avg": {"precision": 0.8882882882882883, "recall": 0.8860821048321048, "f1-score": 0.88459184348082, "support": 1370.0}, "weighted avg": {"precision": 0.8895377128953772, "recall": 0.8846715328467153, "f1-score": 0.8845077269279306, "support": 1370.0}}, "confusion_matrix": [[624, 42], [116, 588]], "roc_auc": 0.9489105582855583, "training_time": 0.013647, "train_size": 586, "test_size": 1370, "algorithm_type": "Logistic Regression"}, "LR_SAG": {"accuracy": 0.8854014598540146, "precision": [0.8434547908232118, 0.9348171701112877], "recall": [0.9384384384384384, 0.8352272727272727], "f1_score": [0.8884150675195451, 0.8822205551387847], "support": [666, 704], "classification_report": {"0": {"precision": 0.8434547908232118, "recall": 0.9384384384384384, "f1-score": 0.8884150675195451, "support": 666.0}, "1": {"precision": 0.9348171701112877, "recall": 0.8352272727272727, "f1-score": 0.8822205551387847, "support": 704.0}, "accuracy": 0.8854014598540146, "macro avg": {"precision": 0.8891359804672498, "recall": 0.8868328555828555, "f1-score": 0.8853178113291649, "support": 1370.0}, "weighted avg": {"precision": 0.890403049961026, "recall": 0.8854014598540146, "f1-score": 0.8852319020333734, "support": 1370.0}}, "confusion_matrix": [[625, 41], [116, 588]], "roc_auc": 0.9489105582855583, "training_time": 0.004015, "train_size": 586, "test_size": 1370, "algorithm_type": "Logistic Regression"}, "LR_SAGA": {"accuracy": 0.8854014598540146, "precision": [0.8434547908232118, 0.9348171701112877], "recall": [0.9384384384384384, 0.8352272727272727], "f1_score": [0.8884150675195451, 0.8822205551387847], "support": [666, 704], "classification_report": {"0": {"precision": 0.8434547908232118, "recall": 0.9384384384384384, "f1-score": 0.8884150675195451, "support": 666.0}, "1": {"precision": 0.9348171701112877, "recall": 0.8352272727272727, "f1-score": 0.8822205551387847, "support": 704.0}, "accuracy": 0.8854014598540146, "macro avg": {"precision": 0.8891359804672498, "recall": 0.8868328555828555, "f1-score": 0.8853178113291649, "support": 1370.0}, "weighted avg": {"precision": 0.890403049961026, "recall": 0.8854014598540146, "f1-score": 0.8852319020333734, "support": 1370.0}}, "confusion_matrix": [[625, 41], [116, 588]], "roc_auc": 0.9489873396123396, "training_time": 0.011063, "train_size": 586, "test_size": 1370, "algorithm_type": "Logistic Regression"}, "DT_Gini": {"accuracy": 0.8306569343065694, "precision": [0.8959854014598541, 0.7871046228710462], "recall": [0.7372372372372372, 0.9190340909090909], "f1_score": [0.8088962108731467, 0.8479685452162516], "support": [666, 704], "classification_report": {"0": {"precision": 0.8959854014598541, "recall": 0.7372372372372372, "f1-score": 0.8088962108731467, "support": 666.0}, "1": {"precision": 0.7871046228710462, "recall": 0.9190340909090909, "f1-score": 0.8479685452162516, "support": 704.0}, "accuracy": 0.8306569343065694, "macro avg": {"precision": 0.8415450121654502, "recall": 0.8281356640731641, "f1-score": 0.8284323780446992, "support": 1370.0}, "weighted avg": {"precision": 0.840034986768963, "recall": 0.8306569343065694, "f1-score": 0.8289742571341291, "support": 1370.0}}, "confusion_matrix": [[491, 175], [57, 647]], "roc_auc": 0.9205355924105925, "training_time": 0.029084, "train_size": 586, "test_size": 1370, "algorithm_type": "Decision Tree"}, "DT_Entropy": {"accuracy": 0.8262773722627738, "precision": [0.8614864864864865, 0.7994858611825193], "recall": [0.7657657657657657, 0.8835227272727273], "f1_score": [0.8108108108108109, 0.8394062078272605], "support": [666, 704], "classification_report": {"0": {"precision": 0.8614864864864865, "recall": 0.7657657657657657, "f1-score": 0.8108108108108109, "support": 666.0}, "1": {"precision": 0.7994858611825193, "recall": 0.8835227272727273, "f1-score": 0.8394062078272605, "support": 704.0}, "accuracy": 0.8262773722627738, "macro avg": {"precision": 0.8304861738345028, "recall": 0.8246442465192465, "f1-score": 0.8251085093190357, "support": 1370.0}, "weighted avg": {"precision": 0.8296263111478055, "recall": 0.8262773722627738, "f1-score": 0.8255050878178039, "support": 1370.0}}, "confusion_matrix": [[510, 156], [82, 622]], "roc_auc": 0.9043134043134042, "training_time": 0.029088, "train_size": 586, "test_size": 1370, "algorithm_type": "Decision Tree"}, "DT_Log_Loss": {"accuracy": 0.8262773722627738, "precision": [0.8614864864864865, 0.7994858611825193], "recall": [0.7657657657657657, 0.8835227272727273], "f1_score": [0.8108108108108109, 0.8394062078272605], "support": [666, 704], "classification_report": {"0": {"precision": 0.8614864864864865, "recall": 0.7657657657657657, "f1-score": 0.8108108108108109, "support": 666.0}, "1": {"precision": 0.7994858611825193, "recall": 0.8835227272727273, "f1-score": 0.8394062078272605, "support": 704.0}, "accuracy": 0.8262773722627738, "macro avg": {"precision": 0.8304861738345028, "recall": 0.8246442465192465, "f1-score": 0.8251085093190357, "support": 1370.0}, "weighted avg": {"precision": 0.8296263111478055, "recall": 0.8262773722627738, "f1-score": 0.8255050878178039, "support": 1370.0}}, "confusion_matrix": [[510, 156], [82, 622]], "roc_auc": 0.9043134043134042, "training_time": 0.018223, "train_size": 586, "test_size": 1370, "algorithm_type": "Decision Tree"}, "DT_Gini_Pruned": {"accuracy": 0.8759124087591241, "precision": [0.803921568627451, 0.9819494584837545], "recall": [0.984984984984985, 0.7727272727272727], "f1_score": [0.8852901484480432, 0.8648648648648649], "support": [666, 704], "classification_report": {"0": {"precision": 0.803921568627451, "recall": 0.984984984984985, "f1-score": 0.8852901484480432, "support": 666.0}, "1": {"precision": 0.9819494584837545, "recall": 0.7727272727272727, "f1-score": 0.8648648648648649, "support": 704.0}, "accuracy": 0.8759124087591241, "macro avg": {"precision": 0.8929355135556027, "recall": 0.8788561288561288, "f1-score": 0.875077506656454, "support": 1370.0}, "weighted avg": {"precision": 0.8954045134879164, "recall": 0.8759124087591241, "f1-score": 0.8747942363001909, "support": 1370.0}}, "confusion_matrix": [[656, 10], [160, 544]], "roc_auc": 0.8730548730548731, "training_time": 0.025902, "train_size": 586, "test_size": 1370, "algorithm_type": "Decision Tree"}, "DT_Entropy_Pruned": {"accuracy": 0.8423357664233576, "precision": [0.7616279069767442, 0.9784313725490196], "recall": [0.9834834834834835, 0.7088068181818182], "f1_score": [0.8584534731323722, 0.8220757825370676], "support": [666, 704], "classification_report": {"0": {"precision": 0.7616279069767442, "recall": 0.9834834834834835, "f1-score": 0.8584534731323722, "support": 666.0}, "1": {"precision": 0.9784313725490196, "recall": 0.7088068181818182, "f1-score": 0.8220757825370676, "support": 704.0}, "accuracy": 0.8423357664233576, "macro avg": {"precision": 0.870029639762882, "recall": 0.8461451508326508, "f1-score": 0.8402646278347199, "support": 1370.0}, "weighted avg": {"precision": 0.8730364031540302, "recall": 0.8423357664233576, "f1-score": 0.8397601197169747, "support": 1370.0}}, "confusion_matrix": [[655, 11], [205, 499]], "roc_auc": 0.8744337377149878, "training_time": 0.022502, "train_size": 586, "test_size": 1370, "algorithm_type": "Decision Tree"}, "DT_Best_First": {"accuracy": 0.8306569343065694, "precision": [0.8959854014598541, 0.7871046228710462], "recall": [0.7372372372372372, 0.9190340909090909], "f1_score": [0.8088962108731467, 0.8479685452162516], "support": [666, 704], "classification_report": {"0": {"precision": 0.8959854014598541, "recall": 0.7372372372372372, "f1-score": 0.8088962108731467, "support": 666.0}, "1": {"precision": 0.7871046228710462, "recall": 0.9190340909090909, "f1-score": 0.8479685452162516, "support": 704.0}, "accuracy": 0.8306569343065694, "macro avg": {"precision": 0.8415450121654502, "recall": 0.8281356640731641, "f1-score": 0.8284323780446992, "support": 1370.0}, "weighted avg": {"precision": 0.840034986768963, "recall": 0.8306569343065694, "f1-score": 0.8289742571341291, "support": 1370.0}}, "confusion_matrix": [[491, 175], [57, 647]], "roc_auc": 0.9205355924105925, "training_time": 0.024967, "train_size": 586, "test_size": 1370, "algorithm_type": "Decision Tree"}, "DT_Random_Split": {"accuracy": 0.8218978102189781, "precision": [0.8936567164179104, 0.7757793764988009], "recall": [0.7192192192192193, 0.9190340909090909], "f1_score": [0.7970049916805324, 0.8413524057217165], "support": [666, 704], "classification_report": {"0": {"precision": 0.8936567164179104, "recall": 0.7192192192192193, "f1-score": 0.7970049916805324, "support": 666.0}, "1": {"precision": 0.7757793764988009, "recall": 0.9190340909090909, "f1-score": 0.8413524057217165, "support": 704.0}, "accuracy": 0.8218978102189781, "macro avg": {"precision": 0.8347180464583557, "recall": 0.8191266550641552, "f1-score": 0.8191786987011245, "support": 1370.0}, "weighted avg": {"precision": 0.8330832512332003, "recall": 0.8218978102189781, "f1-score": 0.8197937358301629, "support": 1370.0}}, "confusion_matrix": [[479, 187], [57, 647]], "roc_auc": 0.9082943028255529, "training_time": 0.022355, "train_size": 586, "test_size": 1370, "algorithm_type": "Decision Tree"}, "RF_Gini": {"accuracy": 0.8335766423357664, "precision": [0.9025735294117647, 0.788135593220339], "recall": [0.7372372372372372, 0.9247159090909091], "f1_score": [0.8115702479338843, 0.8509803921568627], "support": [666, 704], "classification_report": {"0": {"precision": 0.9025735294117647, "recall": 0.7372372372372372, "f1-score": 0.8115702479338843, "support": 666.0}, "1": {"precision": 0.788135593220339, "recall": 0.9247159090909091, "f1-score": 0.8509803921568627, "support": 704.0}, "accuracy": 0.8335766423357664, "macro avg": {"precision": 0.8453545613160518, "recall": 0.8309765731640731, "f1-score": 0.8312753200453735, "support": 1370.0}, "weighted avg": {"precision": 0.8437674658506232, "recall": 0.8335766423357664, "f1-score": 0.8318218840893419, "support": 1370.0}}, "confusion_matrix": [[491, 175], [53, 651]], "roc_auc": 0.9191993840431341, "training_time": 0.332043, "train_size": 586, "test_size": 1370, "algorithm_type": "Random Forest"}, "RF_Entropy": {"accuracy": 0.8328467153284671, "precision": [0.9038817005545287, 0.7864897466827503], "recall": [0.7342342342342343, 0.9261363636363636], "f1_score": [0.8102734051367025, 0.8506196999347684], "support": [666, 704], "classification_report": {"0": {"precision": 0.9038817005545287, "recall": 0.7342342342342343, "f1-score": 0.8102734051367025, "support": 666.0}, "1": {"precision": 0.7864897466827503, "recall": 0.9261363636363636, "f1-score": 0.8506196999347684, "support": 704.0}, "accuracy": 0.8328467153284671, "macro avg": {"precision": 0.8451857236186395, "recall": 0.8301852989352989, "f1-score": 0.8304465525357354, "support": 1370.0}, "weighted avg": {"precision": 0.8435576600247973, "recall": 0.8328467153284671, "f1-score": 0.8310060996898693, "support": 1370.0}}, "confusion_matrix": [[489, 177], [52, 652]], "roc_auc": 0.9189818369505869, "training_time": 0.256431, "train_size": 586, "test_size": 1370, "algorithm_type": "Random Forest"}, "RF_Log_Loss": {"accuracy": 0.8328467153284671, "precision": [0.9038817005545287, 0.7864897466827503], "recall": [0.7342342342342343, 0.9261363636363636], "f1_score": [0.8102734051367025, 0.8506196999347684], "support": [666, 704], "classification_report": {"0": {"precision": 0.9038817005545287, "recall": 0.7342342342342343, "f1-score": 0.8102734051367025, "support": 666.0}, "1": {"precision": 0.7864897466827503, "recall": 0.9261363636363636, "f1-score": 0.8506196999347684, "support": 704.0}, "accuracy": 0.8328467153284671, "macro avg": {"precision": 0.8451857236186395, "recall": 0.8301852989352989, "f1-score": 0.8304465525357354, "support": 1370.0}, "weighted avg": {"precision": 0.8435576600247973, "recall": 0.8328467153284671, "f1-score": 0.8310060996898693, "support": 1370.0}}, "confusion_matrix": [[489, 177], [52, 652]], "roc_auc": 0.9189818369505869, "training_time": 0.301991, "train_size": 586, "test_size": 1370, "algorithm_type": "Random Forest"}, "RF_Large": {"accuracy": 0.8678832116788321, "precision": [0.8019925280199253, 0.9611992945326279], "recall": [0.9669669669669669, 0.7741477272727273], "f1_score": [0.876786929884275, 0.8575924468922108], "support": [666, 704], "classification_report": {"0": {"precision": 0.8019925280199253, "recall": 0.9669669669669669, "f1-score": 0.876786929884275, "support": 666.0}, "1": {"precision": 0.9611992945326279, "recall": 0.7741477272727273, "f1-score": 0.8575924468922108, "support": 704.0}, "accuracy": 0.8678832116788321, "macro avg": {"precision": 0.8815959112762766, "recall": 0.8705573471198471, "f1-score": 0.8671896883882428, "support": 1370.0}, "weighted avg": {"precision": 0.8838038883301025, "recall": 0.8678832116788321, "f1-score": 0.8669234875292289, "support": 1370.0}}, "confusion_matrix": [[644, 22], [159, 545]], "roc_auc": 0.9431828845891347, "training_time": 0.289289, "train_size": 586, "test_size": 1370, "algorithm_type": "Random Forest"}, "RF_Small": {"accuracy": 0.8583941605839416, "precision": [0.7920792079207921, 0.9537366548042705], "recall": [0.960960960960961, 0.7613636363636364], "f1_score": [0.8683853459972863, 0.8467614533965245], "support": [666, 704], "classification_report": {"0": {"precision": 0.7920792079207921, "recall": 0.960960960960961, "f1-score": 0.8683853459972863, "support": 666.0}, "1": {"precision": 0.9537366548042705, "recall": 0.7613636363636364, "f1-score": 0.8467614533965245, "support": 704.0}, "accuracy": 0.8583941605839416, "macro avg": {"precision": 0.8729079313625313, "recall": 0.8611622986622987, "f1-score": 0.8575733996969055, "support": 1370.0}, "weighted avg": {"precision": 0.8751498959543459, "recall": 0.8583941605839416, "f1-score": 0.857273506295873, "support": 1370.0}}, "confusion_matrix": [[640, 26], [168, 536]], "roc_auc": 0.9330573471198471, "training_time": 0.064888, "train_size": 586, "test_size": 1370, "algorithm_type": "Random Forest"}, "ExtraTrees": {"accuracy": 0.845985401459854, "precision": [0.8956521739130435, 0.810062893081761], "recall": [0.7732732732732732, 0.9147727272727273], "f1_score": [0.8299758259468171, 0.8592394929953302], "support": [666, 704], "classification_report": {"0": {"precision": 0.8956521739130435, "recall": 0.7732732732732732, "f1-score": 0.8299758259468171, "support": 666.0}, "1": {"precision": 0.810062893081761, "recall": 0.9147727272727273, "f1-score": 0.8592394929953302, "support": 704.0}, "accuracy": 0.845985401459854, "macro avg": {"precision": 0.8528575334974022, "recall": 0.8440230002730003, "f1-score": 0.8446076594710736, "support": 1370.0}, "weighted avg": {"precision": 0.851670528872735, "recall": 0.845985401459854, "f1-score": 0.8450135059483888, "support": 1370.0}}, "confusion_matrix": [[515, 151], [60, 644]], "roc_auc": 0.9288951593639094, "training_time": 0.332318, "train_size": 586, "test_size": 1370, "algorithm_type": "Random Forest"}, "ExtraTrees_Large": {"accuracy": 0.8722627737226277, "precision": [0.8080301129234629, 0.9616055846422339], "recall": [0.9669669669669669, 0.7826704545454546], "f1_score": [0.8803827751196173, 0.8629600626468285], "support": [666, 704], "classification_report": {"0": {"precision": 0.8080301129234629, "recall": 0.9669669669669669, "f1-score": 0.8803827751196173, "support": 666.0}, "1": {"precision": 0.9616055846422339, "recall": 0.7826704545454546, "f1-score": 0.8629600626468285, "support": 704.0}, "accuracy": 0.8722627737226277, "macro avg": {"precision": 0.8848178487828484, "recall": 0.8748187107562108, "f1-score": 0.8716714188832229, "support": 1370.0}, "weighted avg": {"precision": 0.8869477275877073, "recall": 0.8722627737226277, "f1-score": 0.8714297900241113, "support": 1370.0}}, "confusion_matrix": [[644, 22], [153, 551]], "roc_auc": 0.9411225856538357, "training_time": 0.226227, "train_size": 586, "test_size": 1370, "algorithm_type": "Random Forest"}, "GradientBoosting": {"accuracy": 0.8824817518248175, "precision": [0.8136645962732919, 0.9805309734513274], "recall": [0.9834834834834835, 0.7869318181818182], "f1_score": [0.8905506458191706, 0.8731284475965327], "support": [666, 704], "classification_report": {"0": {"precision": 0.8136645962732919, "recall": 0.9834834834834835, "f1-score": 0.8905506458191706, "support": 666.0}, "1": {"precision": 0.9805309734513274, "recall": 0.7869318181818182, "f1-score": 0.8731284475965327, "support": 704.0}, "accuracy": 0.8824817518248175, "macro avg": {"precision": 0.8970977848623096, "recall": 0.8852076508326508, "f1-score": 0.8818395467078517, "support": 1370.0}, "weighted avg": {"precision": 0.899411990093246, "recall": 0.8824817518248175, "f1-score": 0.8815979249806764, "support": 1370.0}}, "confusion_matrix": [[655, 11], [150, 554]], "roc_auc": 0.9383339305214305, "training_time": 0.370832, "train_size": 586, "test_size": 1370, "algorithm_type": "Ensemble"}, "AdaBoost": {"accuracy": 0.8737226277372263, "precision": [0.8116308470290771, 0.9585492227979274], "recall": [0.963963963963964, 0.7883522727272727], "f1_score": [0.8812628689087165, 0.8651597817614964], "support": [666, 704], "classification_report": {"0": {"precision": 0.8116308470290771, "recall": 0.963963963963964, "f1-score": 0.8812628689087165, "support": 666.0}, "1": {"precision": 0.9585492227979274, "recall": 0.7883522727272727, "f1-score": 0.8651597817614964, "support": 704.0}, "accuracy": 0.8737226277372263, "macro avg": {"precision": 0.8850900349135022, "recall": 0.8761581183456184, "f1-score": 0.8732113253351065, "support": 1370.0}, "weighted avg": {"precision": 0.8871275890300045, "recall": 0.8737226277372263, "f1-score": 0.8729879978491233, "support": 1370.0}}, "confusion_matrix": [[642, 24], [149, 555]], "roc_auc": 0.9254357340294841, "training_time": 0.24281, "train_size": 586, "test_size": 1370, "algorithm_type": "Ensemble"}, "AdaBoost_DT": {"accuracy": 0.8452554744525548, "precision": [0.8913793103448275, 0.8113924050632911], "recall": [0.7762762762762763, 0.9105113636363636], "f1_score": [0.8298555377207063, 0.85809906291834], "support": [666, 704], "classification_report": {"0": {"precision": 0.8913793103448275, "recall": 0.7762762762762763, "f1-score": 0.8298555377207063, "support": 666.0}, "1": {"precision": 0.8113924050632911, "recall": 0.9105113636363636, "f1-score": 0.85809906291834, "support": 704.0}, "accuracy": 0.8452554744525548, "macro avg": {"precision": 0.8513858577040594, "recall": 0.84339381995632, "f1-score": 0.8439773003195232, "support": 1370.0}, "weighted avg": {"precision": 0.850276550258549, "recall": 0.8452554744525548, "f1-score": 0.8443689988441617, "support": 1370.0}}, "confusion_matrix": [[517, 149], [63, 641]], "roc_auc": 0.9453082343707343, "training_time": 0.431648, "train_size": 586, "test_size": 1370, "algorithm_type": "Decision Tree"}}, "train_35_test_65_REVERSED": {"SVM_Linear": {"accuracy": 0.8962264150943396, "precision": [0.8521739130434782, 0.9484536082474226], "recall": [0.9514563106796117, 0.8440366972477065], "f1_score": [0.8990825688073395, 0.8932038834951457], "support": [618, 654], "classification_report": {"0": {"precision": 0.8521739130434782, "recall": 0.9514563106796117, "f1-score": 0.8990825688073395, "support": 618.0}, "1": {"precision": 0.9484536082474226, "recall": 0.8440366972477065, "f1-score": 0.8932038834951457, "support": 654.0}, "accuracy": 0.8962264150943396, "macro avg": {"precision": 0.9003137606454504, "recall": 0.8977465039636591, "f1-score": 0.8961432261512425, "support": 1272.0}, "weighted avg": {"precision": 0.9016762091624874, "recall": 0.8962264150943396, "f1-score": 0.8960600372081455, "support": 1272.0}}, "confusion_matrix": [[588, 30], [102, 552]], "roc_auc": 0.9617638035291907, "training_time": 0.113852, "train_size": 684, "test_size": 1272, "algorithm_type": "Support Vector Machine"}, "SVM_Polynomial": {"accuracy": 0.8592767295597484, "precision": [0.8830715532286213, 0.8397711015736766], "recall": [0.8187702265372169, 0.8975535168195719], "f1_score": [0.8497061293031066, 0.8677014042867701], "support": [618, 654], "classification_report": {"0": {"precision": 0.8830715532286213, "recall": 0.8187702265372169, "f1-score": 0.8497061293031066, "support": 618.0}, "1": {"precision": 0.8397711015736766, "recall": 0.8975535168195719, "f1-score": 0.8677014042867701, "support": 654.0}, "accuracy": 0.8592767295597484, "macro avg": {"precision": 0.861421327401149, "recall": 0.8581618716783943, "f1-score": 0.8587037667949384, "support": 1272.0}, "weighted avg": {"precision": 0.8608085851607489, "recall": 0.8592767295597484, "f1-score": 0.8589584169126319, "support": 1272.0}}, "confusion_matrix": [[506, 112], [67, 587]], "roc_auc": 0.9358824955711924, "training_time": 0.137816, "train_size": 684, "test_size": 1272, "algorithm_type": "Support Vector Machine"}, "SVM_RBF": {"accuracy": 0.8954402515723271, "precision": [0.8783151326053042, 0.9128367670364501], "recall": [0.9110032362459547, 0.8807339449541285], "f1_score": [0.8943606036536934, 0.8964980544747082], "support": [618, 654], "classification_report": {"0": {"precision": 0.8783151326053042, "recall": 0.9110032362459547, "f1-score": 0.8943606036536934, "support": 618.0}, "1": {"precision": 0.9128367670364501, "recall": 0.8807339449541285, "f1-score": 0.8964980544747082, "support": 654.0}, "accuracy": 0.8954402515723271, "macro avg": {"precision": 0.8955759498208772, "recall": 0.8958685906000416, "f1-score": 0.8954293290642008, "support": 1272.0}, "weighted avg": {"precision": 0.8960644635156574, "recall": 0.8954402515723271, "f1-score": 0.8954595760097813, "support": 1272.0}}, "confusion_matrix": [[563, 55], [78, 576]], "roc_auc": 0.9396618271428996, "training_time": 0.146745, "train_size": 684, "test_size": 1272, "algorithm_type": "Support Vector Machine"}, "SVM_Sigmoid": {"accuracy": 0.8946540880503144, "precision": [0.8447293447293447, 0.956140350877193], "recall": [0.959546925566343, 0.8333333333333334], "f1_score": [0.8984848484848484, 0.8905228758169934], "support": [618, 654], "classification_report": {"0": {"precision": 0.8447293447293447, "recall": 0.959546925566343, "f1-score": 0.8984848484848484, "support": 618.0}, "1": {"precision": 0.956140350877193, "recall": 0.8333333333333334, "f1-score": 0.8905228758169934, "support": 654.0}, "accuracy": 0.8946540880503144, "macro avg": {"precision": 0.9004348478032689, "recall": 0.8964401294498382, "f1-score": 0.8945038621509209, "support": 1272.0}, "weighted avg": {"precision": 0.9020114186449836, "recall": 0.8946540880503144, "f1-score": 0.8943911927263757, "support": 1272.0}}, "confusion_matrix": [[593, 25], [109, 545]], "roc_auc": 0.9616871035103867, "training_time": 0.123403, "train_size": 684, "test_size": 1272, "algorithm_type": "Support Vector Machine"}, "SVM_RBF_Tuned": {"accuracy": 0.8970125786163522, "precision": [0.8822605965463108, 0.9118110236220472], "recall": [0.9093851132686084, 0.8853211009174312], "f1_score": [0.895617529880478, 0.8983708301008534], "support": [618, 654], "classification_report": {"0": {"precision": 0.8822605965463108, "recall": 0.9093851132686084, "f1-score": 0.895617529880478, "support": 618.0}, "1": {"precision": 0.9118110236220472, "recall": 0.8853211009174312, "f1-score": 0.8983708301008534, "support": 654.0}, "accuracy": 0.8970125786163522, "macro avg": {"precision": 0.8970358100841791, "recall": 0.8973531070930199, "f1-score": 0.8969941799906658, "support": 1272.0}, "weighted avg": {"precision": 0.8974539765050622, "recall": 0.8970125786163522, "f1-score": 0.897033141786237, "support": 1272.0}}, "confusion_matrix": [[562, 56], [75, 579]], "roc_auc": 0.9389344140613403, "training_time": 0.171334, "train_size": 684, "test_size": 1272, "algorithm_type": "Support Vector Machine"}, "SVM_Linear_Tuned": {"accuracy": 0.8962264150943396, "precision": [0.8521739130434782, 0.9484536082474226], "recall": [0.9514563106796117, 0.8440366972477065], "f1_score": [0.8990825688073395, 0.8932038834951457], "support": [618, 654], "classification_report": {"0": {"precision": 0.8521739130434782, "recall": 0.9514563106796117, "f1-score": 0.8990825688073395, "support": 618.0}, "1": {"precision": 0.9484536082474226, "recall": 0.8440366972477065, "f1-score": 0.8932038834951457, "support": 654.0}, "accuracy": 0.8962264150943396, "macro avg": {"precision": 0.9003137606454504, "recall": 0.8977465039636591, "f1-score": 0.8961432261512425, "support": 1272.0}, "weighted avg": {"precision": 0.9016762091624874, "recall": 0.8962264150943396, "f1-score": 0.8960600372081455, "support": 1272.0}}, "confusion_matrix": [[588, 30], [102, 552]], "roc_auc": 0.9617638035291907, "training_time": 0.124466, "train_size": 684, "test_size": 1272, "algorithm_type": "Support Vector Machine"}, "NB_Multinomial": {"accuracy": 0.8797169811320755, "precision": [0.91005291005291, 0.8553191489361702], "recall": [0.8349514563106796, 0.9220183486238532], "f1_score": [0.8708860759493671, 0.8874172185430463], "support": [618, 654], "classification_report": {"0": {"precision": 0.91005291005291, "recall": 0.8349514563106796, "f1-score": 0.8708860759493671, "support": 618.0}, "1": {"precision": 0.8553191489361702, "recall": 0.9220183486238532, "f1-score": 0.8874172185430463, "support": 654.0}, "accuracy": 0.8797169811320755, "macro avg": {"precision": 0.8826860294945401, "recall": 0.8784849024672664, "f1-score": 0.8791516472462066, "support": 1272.0}, "weighted avg": {"precision": 0.8819114951391146, "recall": 0.8797169811320755, "f1-score": 0.8793855785093248, "support": 1272.0}}, "confusion_matrix": [[516, 102], [51, 603]], "roc_auc": 0.9552393535425512, "training_time": 0.02435, "train_size": 684, "test_size": 1272, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli": {"accuracy": 0.8301886792452831, "precision": [0.7439320388349514, 0.9888392857142857], "recall": [0.9919093851132686, 0.6773700305810397], "f1_score": [0.8502080443828016, 0.8039927404718693], "support": [618, 654], "classification_report": {"0": {"precision": 0.7439320388349514, "recall": 0.9919093851132686, "f1-score": 0.8502080443828016, "support": 618.0}, "1": {"precision": 0.9888392857142857, "recall": 0.6773700305810397, "f1-score": 0.8039927404718693, "support": 654.0}, "accuracy": 0.8301886792452831, "macro avg": {"precision": 0.8663856622746186, "recall": 0.8346397078471541, "f1-score": 0.8271003924273355, "support": 1272.0}, "weighted avg": {"precision": 0.8698513308625337, "recall": 0.8301886792452831, "f1-score": 0.8264464022776525, "support": 1272.0}}, "confusion_matrix": [[613, 5], [211, 443]], "roc_auc": 0.9495239650445849, "training_time": 0.011567, "train_size": 684, "test_size": 1272, "algorithm_type": "<PERSON><PERSON>"}, "NB_Complement": {"accuracy": 0.8781446540880503, "precision": [0.8545176110260337, 0.9030694668820679], "recall": [0.9029126213592233, 0.8547400611620795], "f1_score": [0.8780487804878049, 0.8782403770620582], "support": [618, 654], "classification_report": {"0": {"precision": 0.8545176110260337, "recall": 0.9029126213592233, "f1-score": 0.8780487804878049, "support": 618.0}, "1": {"precision": 0.9030694668820679, "recall": 0.8547400611620795, "f1-score": 0.8782403770620582, "support": 654.0}, "accuracy": 0.8781446540880503, "macro avg": {"precision": 0.8787935389540509, "recall": 0.8788263412606514, "f1-score": 0.8781445787749316, "support": 1272.0}, "weighted avg": {"precision": 0.8794805935180513, "recall": 0.8781446540880503, "f1-score": 0.8781472900472086, "support": 1272.0}}, "confusion_matrix": [[558, 60], [95, 559]], "roc_auc": 0.9552393535425512, "training_time": 0.012167, "train_size": 684, "test_size": 1272, "algorithm_type": "<PERSON><PERSON>"}, "NB_Multinomial_Tuned": {"accuracy": 0.8773584905660378, "precision": [0.9010416666666666, 0.8577586206896551], "recall": [0.8398058252427184, 0.9128440366972477], "f1_score": [0.8693467336683417, 0.8844444444444445], "support": [618, 654], "classification_report": {"0": {"precision": 0.9010416666666666, "recall": 0.8398058252427184, "f1-score": 0.8693467336683417, "support": 618.0}, "1": {"precision": 0.8577586206896551, "recall": 0.9128440366972477, "f1-score": 0.8844444444444445, "support": 654.0}, "accuracy": 0.8773584905660378, "macro avg": {"precision": 0.8794001436781609, "recall": 0.876324930969983, "f1-score": 0.8768955890563931, "support": 1272.0}, "weighted avg": {"precision": 0.8787876477445239, "recall": 0.8773584905660378, "f1-score": 0.8771092359069984, "support": 1272.0}}, "confusion_matrix": [[519, 99], [57, 597]], "roc_auc": 0.9533564917906238, "training_time": 0.002376, "train_size": 684, "test_size": 1272, "algorithm_type": "<PERSON><PERSON>"}, "NB_Bernoulli_Tuned": {"accuracy": 0.8529874213836478, "precision": [0.7816993464052288, 0.960552268244576], "recall": [0.9676375404530745, 0.7446483180428135], "f1_score": [0.8647866955892987, 0.8389319552110249], "support": [618, 654], "classification_report": {"0": {"precision": 0.7816993464052288, "recall": 0.9676375404530745, "f1-score": 0.8647866955892987, "support": 618.0}, "1": {"precision": 0.960552268244576, "recall": 0.7446483180428135, "f1-score": 0.8389319552110249, "support": 654.0}, "accuracy": 0.8529874213836478, "macro avg": {"precision": 0.8711258073249024, "recall": 0.856142929247944, "f1-score": 0.8518593254001618, "support": 1272.0}, "weighted avg": {"precision": 0.8736567448981007, "recall": 0.8529874213836478, "f1-score": 0.8514934564325448, "support": 1272.0}}, "confusion_matrix": [[598, 20], [167, 487]], "roc_auc": 0.9440559959621151, "training_time": 0.002589, "train_size": 684, "test_size": 1272, "algorithm_type": "<PERSON><PERSON>"}, "LR_L1": {"accuracy": 0.8498427672955975, "precision": [0.7726692209450831, 0.9734151329243353], "recall": [0.9789644012944984, 0.72782874617737], "f1_score": [0.8636688079942898, 0.8328958880139983], "support": [618, 654], "classification_report": {"0": {"precision": 0.7726692209450831, "recall": 0.9789644012944984, "f1-score": 0.8636688079942898, "support": 618.0}, "1": {"precision": 0.9734151329243353, "recall": 0.72782874617737, "f1-score": 0.8328958880139983, "support": 654.0}, "accuracy": 0.8498427672955975, "macro avg": {"precision": 0.8730421769347092, "recall": 0.8533965737359341, "f1-score": 0.848282348004144, "support": 1272.0}, "weighted avg": {"precision": 0.8758829209721514, "recall": 0.8498427672955975, "f1-score": 0.8478468821553663, "support": 1272.0}}, "confusion_matrix": [[605, 13], [178, 476]], "roc_auc": 0.9206575418386231, "training_time": 0.020663, "train_size": 684, "test_size": 1272, "algorithm_type": "Logistic Regression"}, "LR_L2": {"accuracy": 0.8922955974842768, "precision": [0.8531571218795888, 0.937394247038917], "recall": [0.9401294498381877, 0.8470948012232415], "f1_score": [0.8945342571208622, 0.8899598393574297], "support": [618, 654], "classification_report": {"0": {"precision": 0.8531571218795888, "recall": 0.9401294498381877, "f1-score": 0.8945342571208622, "support": 618.0}, "1": {"precision": 0.937394247038917, "recall": 0.8470948012232415, "f1-score": 0.8899598393574297, "support": 654.0}, "accuracy": 0.8922955974842768, "macro avg": {"precision": 0.8952756844592529, "recall": 0.8936121255307146, "f1-score": 0.892247048239146, "support": 1272.0}, "weighted avg": {"precision": 0.8964677192492434, "recall": 0.8922955974842768, "f1-score": 0.8921823159123049, "support": 1272.0}}, "confusion_matrix": [[581, 37], [100, 554]], "roc_auc": 0.9534678305275972, "training_time": 0.0, "train_size": 684, "test_size": 1272, "algorithm_type": "Logistic Regression"}, "LR_ElasticNet": {"accuracy": 0.8765723270440252, "precision": [0.8196948682385575, 0.9509981851179673], "recall": [0.9563106796116505, 0.8012232415902141], "f1_score": [0.8827483196415236, 0.8697095435684647], "support": [618, 654], "classification_report": {"0": {"precision": 0.8196948682385575, "recall": 0.9563106796116505, "f1-score": 0.8827483196415236, "support": 618.0}, "1": {"precision": 0.9509981851179673, "recall": 0.8012232415902141, "f1-score": 0.8697095435684647, "support": 654.0}, "accuracy": 0.8765723270440252, "macro avg": {"precision": 0.8853465266782624, "recall": 0.8787669606009323, "f1-score": 0.8762289316049942, "support": 1272.0}, "weighted avg": {"precision": 0.8872045924831596, "recall": 0.8765723270440252, "f1-score": 0.8760444206228282, "support": 1272.0}}, "confusion_matrix": [[591, 27], [130, 524]], "roc_auc": 0.9397979078214225, "training_time": 0.058822, "train_size": 684, "test_size": 1272, "algorithm_type": "Logistic Regression"}, "LR_LBFGS": {"accuracy": 0.8930817610062893, "precision": [0.8533724340175953, 0.9389830508474576], "recall": [0.941747572815534, 0.8470948012232415], "f1_score": [0.8953846153846153, 0.8906752411575563], "support": [618, 654], "classification_report": {"0": {"precision": 0.8533724340175953, "recall": 0.941747572815534, "f1-score": 0.8953846153846153, "support": 618.0}, "1": {"precision": 0.9389830508474576, "recall": 0.8470948012232415, "f1-score": 0.8906752411575563, "support": 654.0}, "accuracy": 0.8930817610062893, "macro avg": {"precision": 0.8961777424325265, "recall": 0.8944211870193878, "f1-score": 0.8930299282710858, "support": 1272.0}, "weighted avg": {"precision": 0.8973892134254018, "recall": 0.8930817610062893, "f1-score": 0.8929632861829669, "support": 1272.0}}, "confusion_matrix": [[582, 36], [100, 554]], "roc_auc": 0.9535148402165414, "training_time": 0.022874, "train_size": 684, "test_size": 1272, "algorithm_type": "Logistic Regression"}, "LR_SAG": {"accuracy": 0.8922955974842768, "precision": [0.8521229868228404, 0.9388794567062818], "recall": [0.941747572815534, 0.845565749235474], "f1_score": [0.8946963873943121, 0.8897827835880934], "support": [618, 654], "classification_report": {"0": {"precision": 0.8521229868228404, "recall": 0.941747572815534, "f1-score": 0.8946963873943121, "support": 618.0}, "1": {"precision": 0.9388794567062818, "recall": 0.845565749235474, "f1-score": 0.8897827835880934, "support": 654.0}, "accuracy": 0.8922955974842768, "macro avg": {"precision": 0.895501221764561, "recall": 0.893656661025504, "f1-score": 0.8922395854912027, "support": 1272.0}, "weighted avg": {"precision": 0.8967289076591382, "recall": 0.8922955974842768, "f1-score": 0.8921700533618696, "support": 1272.0}}, "confusion_matrix": [[582, 36], [101, 553]], "roc_auc": 0.9534925724691468, "training_time": 0.017925, "train_size": 684, "test_size": 1272, "algorithm_type": "Logistic Regression"}, "LR_SAGA": {"accuracy": 0.8922955974842768, "precision": [0.8531571218795888, 0.937394247038917], "recall": [0.9401294498381877, 0.8470948012232415], "f1_score": [0.8945342571208622, 0.8899598393574297], "support": [618, 654], "classification_report": {"0": {"precision": 0.8531571218795888, "recall": 0.9401294498381877, "f1-score": 0.8945342571208622, "support": 618.0}, "1": {"precision": 0.937394247038917, "recall": 0.8470948012232415, "f1-score": 0.8899598393574297, "support": 654.0}, "accuracy": 0.8922955974842768, "macro avg": {"precision": 0.8952756844592529, "recall": 0.8936121255307146, "f1-score": 0.892247048239146, "support": 1272.0}, "weighted avg": {"precision": 0.8964677192492434, "recall": 0.8922955974842768, "f1-score": 0.8921823159123049, "support": 1272.0}}, "confusion_matrix": [[581, 37], [100, 554]], "roc_auc": 0.953547004740556, "training_time": 0.001811, "train_size": 684, "test_size": 1272, "algorithm_type": "Logistic Regression"}, "DT_Gini": {"accuracy": 0.8459119496855346, "precision": [0.8892988929889298, 0.8136986301369863], "recall": [0.7799352750809061, 0.908256880733945], "f1_score": [0.8310344827586207, 0.8583815028901735], "support": [618, 654], "classification_report": {"0": {"precision": 0.8892988929889298, "recall": 0.7799352750809061, "f1-score": 0.8310344827586207, "support": 618.0}, "1": {"precision": 0.8136986301369863, "recall": 0.908256880733945, "f1-score": 0.8583815028901735, "support": 654.0}, "accuracy": 0.8459119496855346, "macro avg": {"precision": 0.8514987615629581, "recall": 0.8440960779074256, "f1-score": 0.844707992824397, "support": 1272.0}, "weighted avg": {"precision": 0.8504289465226003, "recall": 0.8459119496855346, "f1-score": 0.8450949789583342, "support": 1272.0}}, "confusion_matrix": [[482, 136], [60, 594]], "roc_auc": 0.9242624427224053, "training_time": 0.032715, "train_size": 684, "test_size": 1272, "algorithm_type": "Decision Tree"}, "DT_Entropy": {"accuracy": 0.8325471698113207, "precision": [0.867513611615245, 0.8058252427184466], "recall": [0.7734627831715211, 0.8883792048929664], "f1_score": [0.8177929854576561, 0.8450909090909091], "support": [618, 654], "classification_report": {"0": {"precision": 0.867513611615245, "recall": 0.7734627831715211, "f1-score": 0.8177929854576561, "support": 618.0}, "1": {"precision": 0.8058252427184466, "recall": 0.8883792048929664, "f1-score": 0.8450909090909091, "support": 654.0}, "accuracy": 0.8325471698113207, "macro avg": {"precision": 0.8366694271668458, "recall": 0.8309209940322437, "f1-score": 0.8314419472742827, "support": 1272.0}, "weighted avg": {"precision": 0.8357964785503817, "recall": 0.8325471698113207, "f1-score": 0.8318282386464513, "support": 1272.0}}, "confusion_matrix": [[478, 140], [73, 581]], "roc_auc": 0.9078894629019328, "training_time": 0.033966, "train_size": 684, "test_size": 1272, "algorithm_type": "Decision Tree"}, "DT_Log_Loss": {"accuracy": 0.8325471698113207, "precision": [0.867513611615245, 0.8058252427184466], "recall": [0.7734627831715211, 0.8883792048929664], "f1_score": [0.8177929854576561, 0.8450909090909091], "support": [618, 654], "classification_report": {"0": {"precision": 0.867513611615245, "recall": 0.7734627831715211, "f1-score": 0.8177929854576561, "support": 618.0}, "1": {"precision": 0.8058252427184466, "recall": 0.8883792048929664, "f1-score": 0.8450909090909091, "support": 654.0}, "accuracy": 0.8325471698113207, "macro avg": {"precision": 0.8366694271668458, "recall": 0.8309209940322437, "f1-score": 0.8314419472742827, "support": 1272.0}, "weighted avg": {"precision": 0.8357964785503817, "recall": 0.8325471698113207, "f1-score": 0.8318282386464513, "support": 1272.0}}, "confusion_matrix": [[478, 140], [73, 581]], "roc_auc": 0.9078894629019328, "training_time": 0.02322, "train_size": 684, "test_size": 1272, "algorithm_type": "Decision Tree"}, "DT_Gini_Pruned": {"accuracy": 0.8537735849056604, "precision": [0.7755102040816326, 0.9795081967213115], "recall": [0.9838187702265372, 0.7308868501529052], "f1_score": [0.8673323823109843, 0.8371278458844134], "support": [618, 654], "classification_report": {"0": {"precision": 0.7755102040816326, "recall": 0.9838187702265372, "f1-score": 0.8673323823109843, "support": 618.0}, "1": {"precision": 0.9795081967213115, "recall": 0.7308868501529052, "f1-score": 0.8371278458844134, "support": 654.0}, "accuracy": 0.8537735849056604, "macro avg": {"precision": 0.8775092004014721, "recall": 0.8573528101897212, "f1-score": 0.8522301140976989, "support": 1272.0}, "weighted avg": {"precision": 0.8803959644482598, "recall": 0.8537735849056604, "f1-score": 0.8518026914124173, "support": 1272.0}}, "confusion_matrix": [[608, 10], [176, 478]], "roc_auc": 0.8734251754203656, "training_time": 0.018067, "train_size": 684, "test_size": 1272, "algorithm_type": "Decision Tree"}, "DT_Entropy_Pruned": {"accuracy": 0.845125786163522, "precision": [0.762796504369538, 0.9851380042462845], "recall": [0.988673139158576, 0.709480122324159], "f1_score": [0.8611698379140239, 0.8248888888888889], "support": [618, 654], "classification_report": {"0": {"precision": 0.762796504369538, "recall": 0.988673139158576, "f1-score": 0.8611698379140239, "support": 618.0}, "1": {"precision": 0.9851380042462845, "recall": 0.709480122324159, "f1-score": 0.8248888888888889, "support": 654.0}, "accuracy": 0.845125786163522, "macro avg": {"precision": 0.8739672543079113, "recall": 0.8490766307413675, "f1-score": 0.8430293634014564, "support": 1272.0}, "weighted avg": {"precision": 0.8771135962872991, "recall": 0.845125786163522, "f1-score": 0.8425159537454403, "support": 1272.0}}, "confusion_matrix": [[611, 7], [190, 464]], "roc_auc": 0.874453203091753, "training_time": 0.023296, "train_size": 684, "test_size": 1272, "algorithm_type": "Decision Tree"}, "DT_Best_First": {"accuracy": 0.8459119496855346, "precision": [0.8892988929889298, 0.8136986301369863], "recall": [0.7799352750809061, 0.908256880733945], "f1_score": [0.8310344827586207, 0.8583815028901735], "support": [618, 654], "classification_report": {"0": {"precision": 0.8892988929889298, "recall": 0.7799352750809061, "f1-score": 0.8310344827586207, "support": 618.0}, "1": {"precision": 0.8136986301369863, "recall": 0.908256880733945, "f1-score": 0.8583815028901735, "support": 654.0}, "accuracy": 0.8459119496855346, "macro avg": {"precision": 0.8514987615629581, "recall": 0.8440960779074256, "f1-score": 0.844707992824397, "support": 1272.0}, "weighted avg": {"precision": 0.8504289465226003, "recall": 0.8459119496855346, "f1-score": 0.8450949789583342, "support": 1272.0}}, "confusion_matrix": [[482, 136], [60, 594]], "roc_auc": 0.9242624427224053, "training_time": 0.022969, "train_size": 684, "test_size": 1272, "algorithm_type": "Decision Tree"}, "DT_Random_Split": {"accuracy": 0.8482704402515723, "precision": [0.9001883239171374, 0.8110661268556005], "recall": [0.7734627831715211, 0.918960244648318], "f1_score": [0.8320278503046127, 0.8616487455197133], "support": [618, 654], "classification_report": {"0": {"precision": 0.9001883239171374, "recall": 0.7734627831715211, "f1-score": 0.8320278503046127, "support": 618.0}, "1": {"precision": 0.8110661268556005, "recall": 0.918960244648318, "f1-score": 0.8616487455197133, "support": 654.0}, "accuracy": 0.8482704402515723, "macro avg": {"precision": 0.855627225386369, "recall": 0.8462115139099196, "f1-score": 0.8468382979121629, "support": 1272.0}, "weighted avg": {"precision": 0.8543660622204038, "recall": 0.8482704402515723, "f1-score": 0.8472574615236974, "support": 1272.0}}, "confusion_matrix": [[478, 140], [53, 601]], "roc_auc": 0.9242104846451511, "training_time": 0.033225, "train_size": 684, "test_size": 1272, "algorithm_type": "Decision Tree"}, "RF_Gini": {"accuracy": 0.85062893081761, "precision": [0.9147286821705426, 0.8068783068783069], "recall": [0.7637540453074434, 0.9327217125382263], "f1_score": [0.8324514991181657, 0.8652482269503546], "support": [618, 654], "classification_report": {"0": {"precision": 0.9147286821705426, "recall": 0.7637540453074434, "f1-score": 0.8324514991181657, "support": 618.0}, "1": {"precision": 0.8068783068783069, "recall": 0.9327217125382263, "f1-score": 0.8652482269503546, "support": 654.0}, "accuracy": 0.85062893081761, "macro avg": {"precision": 0.8608034945244247, "recall": 0.8482378789228349, "f1-score": 0.8488498630342602, "support": 1272.0}, "weighted avg": {"precision": 0.8592773099684026, "recall": 0.85062893081761, "f1-score": 0.8493139676733948, "support": 1272.0}}, "confusion_matrix": [[472, 146], [44, 610]], "roc_auc": 0.9272042595726573, "training_time": 0.295358, "train_size": 684, "test_size": 1272, "algorithm_type": "Random Forest"}, "RF_Entropy": {"accuracy": 0.8427672955974843, "precision": [0.908203125, 0.7986842105263158], "recall": [0.7524271844660194, 0.9281345565749235], "f1_score": [0.8230088495575221, 0.8585572842998586], "support": [618, 654], "classification_report": {"0": {"precision": 0.908203125, "recall": 0.7524271844660194, "f1-score": 0.8230088495575221, "support": 618.0}, "1": {"precision": 0.7986842105263158, "recall": 0.9281345565749235, "f1-score": 0.8585572842998586, "support": 654.0}, "accuracy": 0.8427672955974843, "macro avg": {"precision": 0.8534436677631578, "recall": 0.8402808705204714, "f1-score": 0.8407830669286903, "support": 1272.0}, "weighted avg": {"precision": 0.8518938718036246, "recall": 0.8427672955974843, "f1-score": 0.8412861108165537, "support": 1272.0}}, "confusion_matrix": [[465, 153], [47, 607]], "roc_auc": 0.9250814009876982, "training_time": 0.321762, "train_size": 684, "test_size": 1272, "algorithm_type": "Random Forest"}, "RF_Log_Loss": {"accuracy": 0.8427672955974843, "precision": [0.908203125, 0.7986842105263158], "recall": [0.7524271844660194, 0.9281345565749235], "f1_score": [0.8230088495575221, 0.8585572842998586], "support": [618, 654], "classification_report": {"0": {"precision": 0.908203125, "recall": 0.7524271844660194, "f1-score": 0.8230088495575221, "support": 618.0}, "1": {"precision": 0.7986842105263158, "recall": 0.9281345565749235, "f1-score": 0.8585572842998586, "support": 654.0}, "accuracy": 0.8427672955974843, "macro avg": {"precision": 0.8534436677631578, "recall": 0.8402808705204714, "f1-score": 0.8407830669286903, "support": 1272.0}, "weighted avg": {"precision": 0.8518938718036246, "recall": 0.8427672955974843, "f1-score": 0.8412861108165537, "support": 1272.0}}, "confusion_matrix": [[465, 153], [47, 607]], "roc_auc": 0.9250814009876982, "training_time": 0.28244, "train_size": 684, "test_size": 1272, "algorithm_type": "Random Forest"}, "RF_Large": {"accuracy": 0.875, "precision": [0.8113975576662144, 0.9626168224299065], "recall": [0.9676375404530745, 0.7874617737003058], "f1_score": [0.8826568265682657, 0.8662741799831791], "support": [618, 654], "classification_report": {"0": {"precision": 0.8113975576662144, "recall": 0.9676375404530745, "f1-score": 0.8826568265682657, "support": 618.0}, "1": {"precision": 0.9626168224299065, "recall": 0.7874617737003058, "f1-score": 0.8662741799831791, "support": 654.0}, "accuracy": 0.875, "macro avg": {"precision": 0.8870071900480605, "recall": 0.8775496570766901, "f1-score": 0.8744655032757225, "support": 1272.0}, "weighted avg": {"precision": 0.8891470853041503, "recall": 0.875, "f1-score": 0.8742336733712164, "support": 1272.0}}, "confusion_matrix": [[598, 20], [139, 515]], "roc_auc": 0.9499606603129361, "training_time": 0.290423, "train_size": 684, "test_size": 1272, "algorithm_type": "Random Forest"}, "RF_Small": {"accuracy": 0.8742138364779874, "precision": [0.8061497326203209, 0.9713740458015268], "recall": [0.9757281553398058, 0.7782874617737003], "f1_score": [0.8828696925329429, 0.8641765704584041], "support": [618, 654], "classification_report": {"0": {"precision": 0.8061497326203209, "recall": 0.9757281553398058, "f1-score": 0.8828696925329429, "support": 618.0}, "1": {"precision": 0.9713740458015268, "recall": 0.7782874617737003, "f1-score": 0.8641765704584041, "support": 654.0}, "accuracy": 0.8742138364779874, "macro avg": {"precision": 0.8887618892109238, "recall": 0.8770078085567531, "f1-score": 0.8735231314956735, "support": 1272.0}, "weighted avg": {"precision": 0.8910999691144313, "recall": 0.8742138364779874, "f1-score": 0.8732586061832979, "support": 1272.0}}, "confusion_matrix": [[603, 15], [145, 509]], "roc_auc": 0.9452275268944904, "training_time": 0.079271, "train_size": 684, "test_size": 1272, "algorithm_type": "Random Forest"}, "ExtraTrees": {"accuracy": 0.8694968553459119, "precision": [0.9124087591240876, 0.8370165745856354], "recall": [0.8090614886731392, 0.926605504587156], "f1_score": [0.8576329331046312, 0.8795355587808418], "support": [618, 654], "classification_report": {"0": {"precision": 0.9124087591240876, "recall": 0.8090614886731392, "f1-score": 0.8576329331046312, "support": 618.0}, "1": {"precision": 0.8370165745856354, "recall": 0.926605504587156, "f1-score": 0.8795355587808418, "support": 654.0}, "accuracy": 0.8694968553459119, "macro avg": {"precision": 0.8747126668548615, "recall": 0.8678334966301475, "f1-score": 0.8685842459427364, "support": 1272.0}, "weighted avg": {"precision": 0.8736457963189401, "recall": 0.8694968553459119, "f1-score": 0.8688941887589094, "support": 1272.0}}, "confusion_matrix": [[500, 118], [48, 606]], "roc_auc": 0.9334639707847153, "training_time": 0.346005, "train_size": 684, "test_size": 1272, "algorithm_type": "Random Forest"}, "ExtraTrees_Large": {"accuracy": 0.8765723270440252, "precision": [0.8188105117565698, 0.9526411657559198], "recall": [0.9579288025889967, 0.7996941896024465], "f1_score": [0.8829231916480239, 0.8694929343308395], "support": [618, 654], "classification_report": {"0": {"precision": 0.8188105117565698, "recall": 0.9579288025889967, "f1-score": 0.8829231916480239, "support": 618.0}, "1": {"precision": 0.9526411657559198, "recall": 0.7996941896024465, "f1-score": 0.8694929343308395, "support": 654.0}, "accuracy": 0.8765723270440252, "macro avg": {"precision": 0.8857258387562448, "recall": 0.8788114960957216, "f1-score": 0.8762080629894318, "support": 1272.0}, "weighted avg": {"precision": 0.8876196687656697, "recall": 0.8765723270440252, "f1-score": 0.8760180121783395, "support": 1272.0}}, "confusion_matrix": [[592, 26], [131, 523]], "roc_auc": 0.9459858674029867, "training_time": 0.249743, "train_size": 684, "test_size": 1272, "algorithm_type": "Random Forest"}, "GradientBoosting": {"accuracy": 0.8828616352201258, "precision": [0.8105960264900662, 0.988394584139265], "recall": [0.9902912621359223, 0.7813455657492355], "f1_score": [0.8914785142024764, 0.8727583262169086], "support": [618, 654], "classification_report": {"0": {"precision": 0.8105960264900662, "recall": 0.9902912621359223, "f1-score": 0.8914785142024764, "support": 618.0}, "1": {"precision": 0.988394584139265, "recall": 0.7813455657492355, "f1-score": 0.8727583262169086, "support": 654.0}, "accuracy": 0.8828616352201258, "macro avg": {"precision": 0.8994953053146656, "recall": 0.8858184139425789, "f1-score": 0.8821184202096926, "support": 1272.0}, "weighted avg": {"precision": 0.90201132263989, "recall": 0.8828616352201258, "f1-score": 0.881853511889142, "support": 1272.0}}, "confusion_matrix": [[612, 6], [143, 511]], "roc_auc": 0.9350944647328366, "training_time": 0.3896, "train_size": 684, "test_size": 1272, "algorithm_type": "Ensemble"}, "AdaBoost": {"accuracy": 0.8781446540880503, "precision": [0.8141112618724559, 0.9663551401869159], "recall": [0.970873786407767, 0.790519877675841], "f1_score": [0.8856088560885609, 0.8696383515559294], "support": [618, 654], "classification_report": {"0": {"precision": 0.8141112618724559, "recall": 0.970873786407767, "f1-score": 0.8856088560885609, "support": 618.0}, "1": {"precision": 0.9663551401869159, "recall": 0.790519877675841, "f1-score": 0.8696383515559294, "support": 654.0}, "accuracy": 0.8781446540880503, "macro avg": {"precision": 0.8902332010296858, "recall": 0.880696832041804, "f1-score": 0.8776236038222451, "support": 1272.0}, "weighted avg": {"precision": 0.8923875955341357, "recall": 0.8781446540880503, "f1-score": 0.8773976061165948, "support": 1272.0}}, "confusion_matrix": [[600, 18], [137, 517]], "roc_auc": 0.9218451550330057, "training_time": 0.260594, "train_size": 684, "test_size": 1272, "algorithm_type": "Ensemble"}, "AdaBoost_DT": {"accuracy": 0.8608490566037735, "precision": [0.8795180722891566, 0.8451519536903039], "recall": [0.8268608414239482, 0.8929663608562691], "f1_score": [0.8523769808173478, 0.8684014869888476], "support": [618, 654], "classification_report": {"0": {"precision": 0.8795180722891566, "recall": 0.8268608414239482, "f1-score": 0.8523769808173478, "support": 618.0}, "1": {"precision": 0.8451519536903039, "recall": 0.8929663608562691, "f1-score": 0.8684014869888476, "support": 654.0}, "accuracy": 0.8608490566037735, "macro avg": {"precision": 0.8623350129897303, "recall": 0.8599136011401087, "f1-score": 0.8603892339030976, "support": 1272.0}, "weighted avg": {"precision": 0.86184869999069, "recall": 0.8608490566037735, "f1-score": 0.8606159957828831, "support": 1272.0}}, "confusion_matrix": [[511, 107], [70, 584]], "roc_auc": 0.9512311590115101, "training_time": 0.48064, "train_size": 684, "test_size": 1272, "algorithm_type": "Decision Tree"}}}