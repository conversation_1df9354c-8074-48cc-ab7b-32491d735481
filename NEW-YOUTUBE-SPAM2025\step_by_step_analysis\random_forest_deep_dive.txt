RANDOM FOREST - DE<PERSON>ILED ANALYSIS
============================================================
Analysis Date: 2025-06-30 00:39:29

OVERVIEW:
--------------------
Total Variants Tested: 7
Total Models Trained: 42
Best Accuracy: 0.9012 (90.12%)
Average Accuracy: 0.8659 (86.59%)
Worst Accuracy: 0.8085 (80.85%)

BEST PERFORMER:
--------------------
Algorithm: ExtraTrees
Split Type: Original
Split Name: train_70_test_30
Accuracy: 0.9012 (90.12%)
Spam F1-Score: 0.9073
Training Time: 0.79 seconds

VARIANT COMPARISON:
--------------------
RF_Gini:
  Average Accuracy: 0.8608
  Best Accuracy: 0.8937
  Consistency (Std): 0.0354
  Average Training Time: 0.44s
  Average Spam F1: 0.8741

RF_Entropy:
  Average Accuracy: 0.8561
  Best Accuracy: 0.8905
  Consistency (Std): 0.0329
  Average Training Time: 0.43s
  Average Spam F1: 0.8705

RF_Log_Loss:
  Average Accuracy: 0.8561
  Best Accuracy: 0.8905
  Consistency (Std): 0.0329
  Average Training Time: 0.44s
  Average Spam F1: 0.8705

RF_Large:
  Average Accuracy: 0.8723
  Best Accuracy: 0.8803
  Consistency (Std): 0.0066
  Average Training Time: 0.37s
  Average Spam F1: 0.8623

RF_Small:
  Average Accuracy: 0.8686
  Best Accuracy: 0.8773
  Consistency (Std): 0.0090
  Average Training Time: 0.09s
  Average Spam F1: 0.8586

ExtraTrees:
  Average Accuracy: 0.8756
  Best Accuracy: 0.9012
  Consistency (Std): 0.0244
  Average Training Time: 0.55s
  Average Spam F1: 0.8852

ExtraTrees_Large:
  Average Accuracy: 0.8721
  Best Accuracy: 0.8790
  Consistency (Std): 0.0057
  Average Training Time: 0.29s
  Average Spam F1: 0.8635

PERFORMANCE BY SPLIT TYPE:
------------------------------
Original Splits:
  Models: 21
  Average Accuracy: 0.8828
  Best Accuracy: 0.9012
  Average Training Time: 0.49s

Reversed Splits:
  Models: 21
  Average Accuracy: 0.8491
  Best Accuracy: 0.8766
  Average Training Time: 0.25s

RECOMMENDATIONS:
--------------------
• Increase n_estimators for better performance
• ExtraTrees can provide good alternatives
• Consider max_depth tuning
• Excellent for robust predictions

============================================================